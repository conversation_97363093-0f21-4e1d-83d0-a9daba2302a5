#!/usr/bin/env python3
"""
Cross-platform launcher for AdhocLog
Works on Windows, macOS, and Linux
"""

import os
import sys
import subprocess
import platform
import socket
from pathlib import Path

def print_header():
    print("=" * 50)
    print("  AdhocLog - Universal Launcher")
    print("=" * 50)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version.split()[0]}")
    print()

def check_python():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Error: Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} - Compatible")
    return True

def find_available_port(start_port=8000):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def setup_virtual_environment():
    """Create and setup virtual environment"""
    venv_path = Path("venv")

    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print("✅ Virtual environment created")
        except subprocess.CalledProcessError:
            print("❌ Failed to create virtual environment")
            return False
    else:
        print("✅ Virtual environment already exists")

    return True

def get_venv_python():
    """Get the path to Python in virtual environment"""
    system = platform.system().lower()
    if system == "windows":
        return Path("venv/Scripts/python.exe")
    else:
        return Path("venv/bin/python")

def install_dependencies():
    """Install required dependencies"""
    venv_python = get_venv_python()

    if not venv_python.exists():
        print("❌ Virtual environment Python not found")
        return False

    print("📥 Installing dependencies...")
    try:
        # Upgrade pip first
        subprocess.run([str(venv_python), "-m", "pip", "install", "--upgrade", "pip"],
                      check=True, capture_output=True)

        # Install requirements
        subprocess.run([str(venv_python), "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_sample_data():
    """Create sample data if needed"""
    data_dir = Path("data")
    if not data_dir.exists() or not any(data_dir.glob("tasks_*.json")):
        print("📝 Creating sample data...")
        venv_python = get_venv_python()
        try:
            subprocess.run([str(venv_python), "utils/create_sample_data.py"], check=True)
            print("✅ Sample data created")
        except subprocess.CalledProcessError:
            print("⚠️  Warning: Could not create sample data")

def start_application():
    """Start the Flask application"""
    venv_python = get_venv_python()
    port = find_available_port()

    if not port:
        print("❌ No available ports found")
        return False

    print(f"🚀 Starting application on port {port}...")
    print(f"📱 Open your browser to: http://127.0.0.1:{port}")
    print("🛑 Press Ctrl+C to stop the server")
    print("-" * 50)

    # Set environment variable for port
    env = os.environ.copy()
    env['FLASK_RUN_PORT'] = str(port)

    try:
        # Get project root directory
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        os.chdir(project_root)

        # Start the Flask app
        startup_script = f"""
import sys
import os

# Ensure we're in the right directory and path
sys.path.insert(0, os.getcwd())

try:
    from app import app
    port = int(os.environ.get('FLASK_RUN_PORT', {port}))
    print(f'Starting Flask app on port {{port}}...')
    print('Access the app at: http://127.0.0.1:{{port}}')
    print('Or try: http://localhost:{{port}}')
    print('Press Ctrl+C to stop the server')
    print('-' * 50)
    app.run(debug=True, host='0.0.0.0', port=port)
except Exception as e:
    print(f'Error starting Flask app: {{e}}')
    import traceback
    traceback.print_exc()
"""
        subprocess.run([str(venv_python), "-c", startup_script], env=env)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return False

    return True

def main():
    """Main function"""
    print_header()

    # Check Python version
    if not check_python():
        input("Press Enter to exit...")
        return 1

    # Setup virtual environment
    if not setup_virtual_environment():
        input("Press Enter to exit...")
        return 1

    # Install dependencies
    if not install_dependencies():
        input("Press Enter to exit...")
        return 1

    # Create sample data if needed
    create_sample_data()

    # Start application
    if not start_application():
        input("Press Enter to exit...")
        return 1

    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
