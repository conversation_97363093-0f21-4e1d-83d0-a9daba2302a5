#!/usr/bin/env python3
"""
AdhocLog - GUI Launcher Wrapper
Simplified launcher for GUI compatibility

Note: Import functionality has been moved to the web application.
Access import via the Tasks page in the web interface.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def main():
    print("🚀 AdhocLog - GUI Launcher")
    print("==========================")
    print("💡 Note: Import functionality is now available in the web application")
    print("� Access it via the Tasks page → Import button")
    print()

    # Get the script directory and change to parent (project root)
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)

    # Set environment variables for macOS compatibility
    os.environ['TK_SILENCE_DEPRECATION'] = '1'

    # Try to run the GUI launcher
    try:
        print("🖥️ Starting GUI Launcher...")
        from gui_launcher import main as gui_main
        gui_main()

    except ImportError as e:
        print(f"❌ Could not import GUI launcher: {e}")
        print("💡 Try running: python gui_launcher.py directly")
        print("💡 Or use the web interface: python scripts/run.py")

    except Exception as e:
        print(f"❌ GUI launcher error: {e}")

        # Check if it's a tkinter-related error
        if "tkinter" in str(e).lower() or "tk" in str(e).lower():
            print("💡 This appears to be a tkinter compatibility issue")
            print("💡 Try: brew install python-tk")
            print("💡 Or run the web interface with: python run.py")

        sys.exit(1)

    print("👋 GUI Launcher closed successfully")

if __name__ == "__main__":
    main()
