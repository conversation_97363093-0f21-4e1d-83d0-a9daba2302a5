#!/usr/bin/env python3
"""
Documentation builder for AdhocLog
Builds MkDocs documentation and integrates it with the Flask application
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def get_project_root():
    """Get the project root directory"""
    return Path(__file__).parent.parent

def check_mkdocs_installed():
    """Check if MkDocs is installed"""
    try:
        subprocess.run(['mkdocs', '--version'],
                      capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_mkdocs_dependencies():
    """Install MkDocs and required plugins"""
    print("📦 Installing MkDocs dependencies...")

    dependencies = [
        'mkdocs>=1.5.0',
        'mkdocs-material>=9.0.0',
        'mkdocs-awesome-pages-plugin>=2.8.0'
    ]

    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

    if not in_venv:
        print("⚠️  Not in a virtual environment. Installing with --user flag...")
        pip_args = [sys.executable, '-m', 'pip', 'install', '--user']
    else:
        pip_args = [sys.executable, '-m', 'pip', 'install']

    for dep in dependencies:
        try:
            subprocess.run(pip_args + [dep], check=True)
            print(f"✅ Installed {dep}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {dep}: {e}")
            return False

    return True

def build_documentation():
    """Build the MkDocs documentation"""
    project_root = get_project_root()

    print("🔨 Building documentation...")

    try:
        # Change to project root directory
        os.chdir(project_root)

        # Build the documentation
        result = subprocess.run(['mkdocs', 'build'],
                               capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Documentation built successfully!")
            return True
        else:
            print(f"❌ Documentation build failed:")
            print(result.stderr)
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ Error building documentation: {e}")
        return False

def serve_documentation(port=8001):
    """Serve the documentation using MkDocs dev server"""
    project_root = get_project_root()

    print(f"🌐 Starting documentation server on port {port}...")

    try:
        # Change to project root directory
        os.chdir(project_root)

        # Start the dev server
        subprocess.run(['mkdocs', 'serve', '--dev-addr', f'127.0.0.1:{port}'])

    except KeyboardInterrupt:
        print("\n📖 Documentation server stopped.")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting documentation server: {e}")

def integrate_with_flask():
    """Integrate built documentation with Flask application"""
    project_root = get_project_root()
    docs_site_dir = project_root / 'docs_site'
    static_docs_dir = project_root / 'app' / 'static' / 'docs'

    print("🔗 Integrating documentation with Flask application...")

    if not docs_site_dir.exists():
        print("❌ Documentation not built. Run build command first.")
        return False

    try:
        # Remove existing docs in static folder
        if static_docs_dir.exists():
            shutil.rmtree(static_docs_dir)

        # Copy built docs to Flask static folder
        shutil.copytree(docs_site_dir, static_docs_dir)

        print("✅ Documentation integrated with Flask application!")
        print(f"📁 Documentation available at: /static/docs/")
        return True

    except Exception as e:
        print(f"❌ Error integrating documentation: {e}")
        return False

def create_docs_route():
    """Create a Flask route for serving documentation"""
    project_root = get_project_root()
    routes_file = project_root / 'app' / 'routes' / 'docs.py'

    # Create routes directory if it doesn't exist
    routes_file.parent.mkdir(exist_ok=True)

    route_content = '''"""
Documentation routes for AdhocLog
Serves the built MkDocs documentation
"""

from flask import Blueprint, send_from_directory, redirect, url_for
from pathlib import Path
import os

docs_bp = Blueprint('docs', __name__, url_prefix='/docs')

@docs_bp.route('/')
def docs_index():
    """Redirect to documentation index"""
    return redirect(url_for('docs.serve_docs', filename='index.html'))

@docs_bp.route('/<path:filename>')
def serve_docs(filename):
    """Serve documentation files"""
    docs_dir = Path(__file__).parent.parent / 'static' / 'docs'

    if not docs_dir.exists():
        return "Documentation not available. Please build the documentation first.", 404

    # Handle directory requests
    if filename.endswith('/') or '.' not in filename:
        if not filename.endswith('/'):
            filename += '/'
        filename += 'index.html'

    try:
        return send_from_directory(docs_dir, filename)
    except FileNotFoundError:
        return "Documentation page not found.", 404

@docs_bp.route('/rebuild')
def rebuild_docs():
    """Rebuild documentation (admin only)"""
    # This would typically require admin authentication
    import subprocess
    import sys

    try:
        # Run the build script
        result = subprocess.run([
            sys.executable,
            str(Path(__file__).parent.parent.parent / 'scripts' / 'build_docs.py'),
            'build'
        ], capture_output=True, text=True)

        if result.returncode == 0:
            return "Documentation rebuilt successfully!", 200
        else:
            return f"Documentation build failed: {result.stderr}", 500

    except Exception as e:
        return f"Error rebuilding documentation: {e}", 500
'''

    try:
        with open(routes_file, 'w') as f:
            f.write(route_content)
        print(f"✅ Created documentation route: {routes_file}")
        return True
    except Exception as e:
        print(f"❌ Error creating documentation route: {e}")
        return False

def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description='AdhocLog Documentation Builder')
    parser.add_argument('command', choices=['install', 'build', 'serve', 'integrate', 'setup'],
                       help='Command to execute')
    parser.add_argument('--port', type=int, default=8001,
                       help='Port for documentation server (default: 8001)')

    args = parser.parse_args()

    if args.command == 'install':
        if not install_mkdocs_dependencies():
            sys.exit(1)

    elif args.command == 'build':
        if not check_mkdocs_installed():
            print("❌ MkDocs not installed. Run 'install' command first.")
            sys.exit(1)

        if not build_documentation():
            sys.exit(1)

    elif args.command == 'serve':
        if not check_mkdocs_installed():
            print("❌ MkDocs not installed. Run 'install' command first.")
            sys.exit(1)

        serve_documentation(args.port)

    elif args.command == 'integrate':
        if not integrate_with_flask():
            sys.exit(1)

        if not create_docs_route():
            sys.exit(1)

    elif args.command == 'setup':
        print("🚀 Setting up MkDocs documentation system...")

        # Install dependencies
        if not check_mkdocs_installed():
            if not install_mkdocs_dependencies():
                sys.exit(1)

        # Build documentation
        if not build_documentation():
            sys.exit(1)

        # Integrate with Flask
        if not integrate_with_flask():
            sys.exit(1)

        # Create Flask routes
        if not create_docs_route():
            sys.exit(1)

        print("✅ MkDocs documentation system setup complete!")
        print("🌐 Documentation available at: http://localhost:8000/docs/")
        print("📖 To serve standalone: python scripts/build_docs.py serve")

if __name__ == '__main__':
    main()
