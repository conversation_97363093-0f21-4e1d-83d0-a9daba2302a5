<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Event Listeners</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Debug Event Listeners</h1>
    
    <div class="debug">
        <h3>Event Log:</h3>
        <div id="event-log" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <form id="test-form" action="http://127.0.0.1:5000/tasks/add" method="POST">
        <div>
            <label>Title: <input type="text" name="title" value="Debug Event Test" required></label>
        </div>
        <div>
            <label>Classification: <input type="text" name="classification" value="Planning" required></label>
        </div>
        <div>
            <label>Description: <input type="text" name="description" value="Testing event listeners"></label>
        </div>
        <div>
            <label>Est Time: <input type="number" name="est_time" value="30"></label>
        </div>
        <div>
            <label>Date: <input type="date" name="date" value="2025-08-01"></label>
        </div>
        <button type="submit" id="submit-btn">Submit Test Form</button>
    </form>

    <div class="debug">
        <h3>Test Multiple Event Listeners:</h3>
        <button onclick="addListener1()">Add Listener 1 (Analytics Style)</button>
        <button onclick="addListener2()">Add Listener 2 (Form Handler Style)</button>
        <button onclick="removeAllListeners()">Remove All Listeners</button>
    </div>

    <script>
        let listenerCount = 0;
        
        function logEvent(message) {
            const logDiv = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += '<div>' + timestamp + ': ' + message + '</div>';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(timestamp + ': ' + message);
        }

        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        // Simulate TaskFormManager analytics listener
        function addListener1() {
            const form = document.getElementById('test-form');
            const listener = () => {
                logEvent('LISTENER 1 (Analytics): Form submit detected');
                setTimeout(() => {
                    logEvent('LISTENER 1 (Analytics): Analytics tracking completed');
                }, 0);
            };
            
            form.addEventListener('submit', listener);
            listenerCount++;
            logEvent('Added Listener 1 (Analytics style) - Total listeners: ' + listenerCount);
        }

        // Simulate AddTaskManager form handler
        function addListener2() {
            const form = document.getElementById('test-form');
            const listener = (event) => {
                logEvent('LISTENER 2 (Form Handler): Form submit detected');
                
                // Check form validity first
                if (!form.checkValidity()) {
                    logEvent('LISTENER 2 (Form Handler): Form validation failed - preventing default');
                    event.preventDefault();
                    event.stopPropagation();
                    form.classList.add('was-validated');
                    return false;
                }

                logEvent('LISTENER 2 (Form Handler): Form validation passed, allowing submission');
                form.classList.add('was-validated');

                // Set processing state with a small delay
                setTimeout(() => {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = 'Processing...';
                        logEvent('LISTENER 2 (Form Handler): Button state changed to processing');
                    }
                }, 50);

                logEvent('LISTENER 2 (Form Handler): Returning true - form should submit');
                return true;
            };
            
            form.addEventListener('submit', listener);
            listenerCount++;
            logEvent('Added Listener 2 (Form Handler style) - Total listeners: ' + listenerCount);
        }

        function removeAllListeners() {
            const form = document.getElementById('test-form');
            const newForm = form.cloneNode(true);
            form.parentNode.replaceChild(newForm, form);
            listenerCount = 0;
            logEvent('Removed all listeners - Total listeners: ' + listenerCount);
        }

        // Monitor page unload (when redirecting)
        window.addEventListener('beforeunload', function() {
            logEvent('Page is redirecting - this is expected behavior');
        });

        logEvent('Debug page loaded - ready for testing');
    </script>
</body>
</html>
