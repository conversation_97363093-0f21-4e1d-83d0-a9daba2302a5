#!/usr/bin/env python3
"""
Test script for import functionality
Creates sample CSV and Excel files for testing
"""

import csv
import json
import os
from datetime import datetime, timedelta
import sys
from pathlib import Path

def create_sample_csv():
    """Create a sample CSV file for testing import"""
    sample_data = [
        ['Date', 'Team Member', 'Task Title', 'Classification', 'Actions Taken / Description', 'Estimated Time (minute)', 'Category'],
        ['2025-07-28', '<PERSON>', 'Daily Stand-up Meeting', 'Business Support Activities', 'Participated in daily team standup', '15', 'Business Support Activities'],
        ['2025-07-28', '<PERSON>', 'Code Review Session', 'Execution', 'Reviewed pull requests and provided feedback', '60', 'Adhoc'],
        ['2025-07-29', '<PERSON>', 'Project Planning', 'Planning', 'Created project timeline and milestones', '90', 'Adhoc'],
        ['07/30/2025', 'Alice Brown', 'Bug Investigation', 'Offline Processing', 'Investigated production bug reports', '45', 'Adhoc'],
        ['30-07-2025', '<PERSON>', 'Client Meeting', 'Business Support Activities', 'Met with client to discuss requirements', '120', 'Business Support Activities']
    ]

    filename = 'sample_import_data.csv'
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(sample_data)

    print(f"✅ Created sample CSV file: {filename}")
    return filename

def create_sample_excel():
    """Create a sample Excel file for testing import"""
    try:
        import pandas as pd

        sample_data = {
            'Date': ['2025-07-28', '2025-07-29', '2025-07-30', '07/31/2025', '31-07-2025'],
            'Team Member': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Mike Wilson'],
            'Task Title': [
                'Daily Stand-up Meeting',
                'Code Review Session',
                'Project Planning',
                'Bug Investigation',
                'Client Meeting'
            ],
            'Classification': [
                'Business Support Activities',
                'Execution',
                'Planning',
                'Offline Processing',
                'Business Support Activities'
            ],
            'Actions Taken / Description': [
                'Participated in daily team standup',
                'Reviewed pull requests and provided feedback',
                'Created project timeline and milestones',
                'Investigated production bug reports',
                'Met with client to discuss requirements'
            ],
            'Estimated Time (minute)': [15, 60, 90, 45, 120],
            'Category': [
                'Business Support Activities',
                'Adhoc',
                'Adhoc',
                'Adhoc',
                'Business Support Activities'
            ]
        }

        df = pd.DataFrame(sample_data)
        filename = 'sample_import_data.xlsx'
        df.to_excel(filename, index=False)

        print(f"✅ Created sample Excel file: {filename}")
        return filename

    except ImportError:
        print("⚠️ Pandas/openpyxl not available, skipping Excel file creation")
        return None

def test_import_functionality():
    """Test the import utility with sample data"""
    try:
        from utils.import_utility import ImportUtility

        # Create sample files
        csv_file = create_sample_csv()
        excel_file = create_sample_excel()

        importer = ImportUtility()

        # Test CSV import
        print("\n🔍 Testing CSV import...")
        csv_result = importer.import_file(csv_file)
        if csv_result['success']:
            print(f"✅ CSV import successful: {csv_result['tasks_imported']} tasks found")
            print(f"📊 Column mapping: {csv_result['column_mapping']}")

            # Show first task as example
            if csv_result['tasks']:
                first_task = csv_result['tasks'][0]
                print(f"📝 First task example:")
                print(f"   Title: {first_task['title']}")
                print(f"   Classification: {first_task['classification']}")
                print(f"   Date: {first_task['date']}")
                print(f"   Team Member: {first_task['team_member']}")
        else:
            print(f"❌ CSV import failed: {csv_result['error']}")

        # Test Excel import if file was created
        if excel_file:
            print("\n🔍 Testing Excel import...")
            excel_result = importer.import_file(excel_file)
            if excel_result['success']:
                print(f"✅ Excel import successful: {excel_result['tasks_imported']} tasks found")
                print(f"📊 Column mapping: {excel_result['column_mapping']}")
            else:
                print(f"❌ Excel import failed: {excel_result['error']}")

        # Clean up test files
        if os.path.exists(csv_file):
            os.remove(csv_file)
            print(f"🗑️ Cleaned up: {csv_file}")

        if excel_file and os.path.exists(excel_file):
            os.remove(excel_file)
            print(f"🗑️ Cleaned up: {excel_file}")

    except ImportError as e:
        print(f"❌ Import utility not available: {e}")
        print("💡 Make sure to run this from the AdhocLog directory")
    except Exception as e:
        print(f"❌ Test failed: {e}")

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking import dependencies...")

    # Check pandas
    try:
        import pandas as pd
        print(f"✅ pandas available: {pd.__version__}")
    except ImportError:
        print("❌ pandas not available - install with: pip install pandas")

    # Check openpyxl
    try:
        import openpyxl
        print(f"✅ openpyxl available: {openpyxl.__version__}")
    except ImportError:
        print("❌ openpyxl not available - install with: pip install openpyxl")

    # Check if import utility exists
    if os.path.exists('import_utility.py'):
        print("✅ import_utility.py found")
    else:
        print("❌ import_utility.py not found in current directory")

if __name__ == "__main__":
    print("🧪 AdhocLog Import Functionality Test")
    print("=" * 50)

    # Check dependencies first
    check_dependencies()
    print()

    # Run tests
    test_import_functionality()

    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    print("\nTo use import functionality:")
    print("1. Ensure pandas and openpyxl are installed")
    print("2. Use the GUI launcher and click 'Import Excel/CSV'")
    print("3. Select your Excel (.xlsx, .xls) or CSV (.csv) file")
    print("4. Review the preview and confirm import")
