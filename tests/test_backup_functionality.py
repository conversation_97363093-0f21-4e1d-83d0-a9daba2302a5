#!/usr/bin/env python3
"""
Test script for backup and restore functionality
Supports both legacy and SharePoint (user-specific) data structures
"""

import os
import json
import shutil
import getpass
from pathlib import Path
from datetime import datetime

def get_data_directory():
    """Get the appropriate data directory based on environment (SharePoint vs local)"""
    # Check for SharePoint mode environment variables
    sharepoint_mode = os.environ.get('ADHOCLOG_SHAREPOINT_MODE', '0') == '1'
    user_data_dir = os.environ.get('ADHOCLOG_USER_DATA_DIR', '')

    if sharepoint_mode and user_data_dir:
        # Use SharePoint user-specific directory
        return Path(user_data_dir)
    else:
        # Check if user-specific directories exist (auto-detect SharePoint mode)
        username = getpass.getuser()
        user_specific_dir = Path(f"data/user_{username}")
        if user_specific_dir.exists():
            return user_specific_dir

        # Fall back to legacy data directory
        return Path("data")

def get_all_data_directories():
    """Get all data directories that contain user data (for comprehensive backup)"""
    data_dirs = []
    base_data_dir = Path("data")

    # Add the primary data directory
    primary_dir = get_data_directory()
    if primary_dir.exists():
        data_dirs.append(primary_dir)

    # In SharePoint mode, also include other user directories for comprehensive backup
    if base_data_dir.exists():
        for user_dir in base_data_dir.glob("user_*"):
            if user_dir.is_dir() and user_dir != primary_dir:
                data_dirs.append(user_dir)

    # Remove duplicates while preserving order
    seen = set()
    unique_dirs = []
    for dir_path in data_dirs:
        if dir_path not in seen:
            seen.add(dir_path)
            unique_dirs.append(dir_path)

    return unique_dirs

def create_test_data():
    """Create some test data files for backup testing"""
    data_dir = get_data_directory()
    data_dir.mkdir(parents=True, exist_ok=True)

    # Determine file naming based on data directory structure
    if data_dir.name.startswith("user_"):
        # SharePoint mode: use simple names
        tasks_filename = "tasks.json"
        archived_filename = "archived_tasks.json"
    else:
        # Legacy mode: use username-based names
        tasks_filename = "tasks_test_user.json"
        archived_filename = "archived_tasks_test_user.json"

    # Create sample tasks file
    sample_tasks = [
        {
            "id": 1,
            "date": "2025-01-28",
            "team_member": "test_user",
            "title": "Test Task 1",
            "classification": "Planning",
            "category": "Adhoc",
            "description": "This is a test task for backup testing",
            "est_time": 30,
            "completed": False
        },
        {
            "id": 2,
            "date": "2025-01-28",
            "team_member": "test_user",
            "title": "Test Task 2",
            "classification": "Execution",
            "category": "Adhoc",
            "description": "Another test task",
            "est_time": 45,
            "completed": True
        }
    ]

    # Write tasks file
    tasks_file = data_dir / tasks_filename
    with open(tasks_file, 'w', encoding='utf-8') as f:
        json.dump(sample_tasks, f, indent=2)

    # Create sample archived tasks
    archived_tasks = [
        {
            "id": 3,
            "date": "2025-01-27",
            "team_member": "test_user",
            "title": "Archived Test Task",
            "classification": "Business Support Activities",
            "category": "Business Support Activities",
            "description": "This task was archived",
            "est_time": 60,
            "completed": True,
            "archived": True,
            "archived_date": "2025-01-28"
        }
    ]

    archived_file = data_dir / archived_filename
    with open(archived_file, 'w', encoding='utf-8') as f:
        json.dump(archived_tasks, f, indent=2)

    # Create analytics file (always in base data directory)
    base_data_dir = Path("data")
    base_data_dir.mkdir(exist_ok=True)
    analytics_data = {
        "last_updated": datetime.now().isoformat(),
        "total_tasks_created": 3,
        "total_time_logged": 135
    }

    analytics_file = base_data_dir / "analytics.json"
    with open(analytics_file, 'w', encoding='utf-8') as f:
        json.dump(analytics_data, f, indent=2)

    print("✅ Created test data files:")
    print(f"  • {tasks_file}")
    print(f"  • {archived_file}")
    print(f"  • {analytics_file}")
    print(f"📁 Data directory: {data_dir}")
    print(f"🔧 Mode: {'SharePoint' if data_dir.name.startswith('user_') else 'Legacy'}")

def test_backup_creation():
    """Test manual backup creation (simulating GUI functionality)"""
    # Get all data directories that contain user data
    data_directories = get_all_data_directories()

    if not data_directories:
        print("❌ No data directories found. Run create_test_data() first.")
        return False

    # Collect all data files from all directories
    all_data_files = []
    for data_dir in data_directories:
        data_files = list(data_dir.glob("*.json"))
        for file in data_files:
            # Store both the file and its source directory for proper backup structure
            all_data_files.append((file, data_dir))

    if not all_data_files:
        print("❌ No data files found for backup.")
        return False

    # Create backup
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    backup_root = Path("backups")
    backup_dir = backup_root / f"backup_{timestamp}"

    backup_root.mkdir(exist_ok=True)
    backup_dir.mkdir(exist_ok=True)

    print(f"📁 Creating backup directory: {backup_dir}")

    # Copy files with proper directory structure preservation
    copied_count = 0
    for data_file, source_dir in all_data_files:
        try:
            # Create a unique filename that preserves source directory info
            if source_dir.name.startswith("user_"):
                # For user-specific directories, prefix with directory name
                destination_name = f"{source_dir.name}_{data_file.name}"
            else:
                # For legacy data directory, use original name
                destination_name = data_file.name

            destination = backup_dir / destination_name
            shutil.copy2(data_file, destination)
            copied_count += 1
            print(f"💾 Backed up: {data_file.name} from {source_dir}")
        except Exception as e:
            print(f"⚠️ Failed to backup {data_file.name}: {e}")

    # Create backup info
    info_file = backup_dir / "backup_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(f"AdhocLog Data Backup\n")
        f.write(f"Created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Files backed up: {copied_count}\n")
        f.write(f"Source directories: {', '.join([str(d.absolute()) for d in data_directories])}\n")
        f.write(f"Backup mode: {'SharePoint (user-specific)' if any(d.name.startswith('user_') for d in data_directories) else 'Legacy'}\n\n")
        f.write("Files included:\n")
        for data_file, source_dir in all_data_files:
            if source_dir.name.startswith("user_"):
                destination_name = f"{source_dir.name}_{data_file.name}"
            else:
                destination_name = data_file.name

            if (backup_dir / destination_name).exists():
                f.write(f"  ✅ {data_file.name} (from {source_dir})\n")
            else:
                f.write(f"  ❌ {data_file.name} (from {source_dir}) - failed to copy\n")

    print(f"✅ Backup created successfully!")
    print(f"📊 Files backed up: {copied_count} out of {len(all_data_files)}")
    print(f"📁 Backup location: {backup_dir}")

    return backup_dir

def test_backup_restore(backup_dir):
    """Test restore functionality"""
    if not backup_dir or not backup_dir.exists():
        print("❌ Invalid backup directory")
        return False

    print(f"🔄 Testing restore from: {backup_dir}")

    # Simulate clearing current data from all data directories
    data_directories = get_all_data_directories()
    total_cleared = 0

    for data_dir in data_directories:
        if data_dir.exists():
            current_files = list(data_dir.glob("*.json"))
            # Clear current data
            for file in current_files:
                file.unlink()
                total_cleared += 1

    print(f"🗑️ Cleared {total_cleared} current data files")

    # Restore from backup
    backup_files = list(backup_dir.glob("*.json"))

    # Ensure base data directory exists
    Path("data").mkdir(exist_ok=True)

    restored_count = 0
    for backup_file in backup_files:
        try:
            # Determine destination based on backup file naming
            if backup_file.name.startswith("user_"):
                # This is a user-specific file: user_username_filename.json
                parts = backup_file.name.split("_", 2)
                if len(parts) >= 3:
                    user_dir_name = f"{parts[0]}_{parts[1]}"  # user_username
                    original_filename = parts[2]  # filename.json

                    # Create user directory if it doesn't exist
                    user_data_dir = Path("data") / user_dir_name
                    user_data_dir.mkdir(parents=True, exist_ok=True)
                    destination = user_data_dir / original_filename
                else:
                    # Fallback to main data directory
                    data_path = get_data_directory()
                    data_path.mkdir(parents=True, exist_ok=True)
                    destination = data_path / backup_file.name
            else:
                # This is a legacy file, restore to main data directory
                data_path = get_data_directory()
                data_path.mkdir(parents=True, exist_ok=True)
                destination = data_path / backup_file.name

            shutil.copy2(backup_file, destination)
            restored_count += 1
            print(f"🔄 Restored: {backup_file.name} to {destination.parent}")
        except Exception as e:
            print(f"⚠️ Failed to restore {backup_file.name}: {e}")

    print(f"✅ Restore completed!")
    print(f"📊 Files restored: {restored_count} out of {len(backup_files)}")

    # Verify restored data across all directories
    total_files = 0
    for data_dir in get_all_data_directories():
        if data_dir.exists():
            restored_files = list(data_dir.glob("*.json"))
            total_files += len(restored_files)
    print(f"📋 Verified {total_files} files in data directories")

    return True

def list_backups():
    """List available backups"""
    backup_root = Path("backups")

    if not backup_root.exists():
        print("ℹ️ No backups directory found")
        return []

    backup_dirs = [d for d in backup_root.iterdir() if d.is_dir() and d.name.startswith("backup_")]

    if not backup_dirs:
        print("ℹ️ No backup directories found")
        return []

    backup_dirs.sort(key=lambda x: x.name, reverse=True)

    print("📋 Available backups:")
    for i, backup_dir in enumerate(backup_dirs, 1):
        timestamp_str = backup_dir.name.replace("backup_", "").replace("_", " at ").replace("-", "/")
        file_count = len(list(backup_dir.glob("*.json")))
        print(f"  {i}. {timestamp_str} ({file_count} files)")

    return backup_dirs

def main():
    """Main test function"""
    print("🧪 AdhocLog Backup & Restore Test")
    print("=" * 40)

    print("\n1. Creating test data...")
    create_test_data()

    print("\n2. Listing current data files...")
    total_files = 0
    data_directories = get_all_data_directories()
    for data_dir in data_directories:
        if data_dir.exists():
            data_files = list(data_dir.glob("*.json"))
            total_files += len(data_files)
            print(f"📁 {data_dir}: {len(data_files)} files")
    print(f"📊 Found {total_files} total data files")

    print("\n3. Creating backup...")
    backup_dir = test_backup_creation()

    print("\n4. Listing available backups...")
    available_backups = list_backups()

    print("\n5. Testing restore...")
    if backup_dir:
        test_backup_restore(backup_dir)

    print("\n6. Final verification...")
    total_final_files = 0
    for data_dir in get_all_data_directories():
        if data_dir.exists():
            final_files = list(data_dir.glob("*.json"))
            total_final_files += len(final_files)
    print(f"📊 Final data files: {total_final_files}")

    print("\n✅ Backup and restore test completed!")
    print("\nYou can now test the GUI backup/restore buttons:")
    print("  • Run: python gui_launcher.py")
    print("  • Click 'Backup Data' to create a backup")
    print("  • Click 'Restore Data' to restore from a backup")

if __name__ == "__main__":
    main()
