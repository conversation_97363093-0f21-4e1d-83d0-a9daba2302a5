#!/usr/bin/env python3
"""
AdhocLog Virtual Environment Setup Utility

This utility helps create and manage user-specific virtual environments
in the user's home directory to prevent OneDrive sync issues when the
application is shared across teams.
"""

import os
import sys
import platform
import subprocess
import shutil
import getpass
from pathlib import Path


class VirtualEnvironmentManager:
    """Manages user-specific virtual environments for AdhocLog in home directory."""

    def __init__(self):
        self.username = getpass.getuser()
        self.platform_os = platform.system()
        self.platform_arch = platform.machine()

        # Map platform.machine() output to consistent naming
        arch_mapping = {
            'x86_64': 'x64',
            'AMD64': 'x64',
            'aarch64': 'ARM64',
            'arm64': 'ARM64',
            'i386': 'x86',
            'i686': 'x86'
        }
        self.platform_arch = arch_mapping.get(self.platform_arch, self.platform_arch)

        # Create virtual environment in user's home directory
        self.home_dir = Path.home()
        self.venvs_base_dir = self.home_dir / ".venvs"
        self.venv_name = "adhoc-log-app"
        self.venv_dir = self.venvs_base_dir / self.venv_name

    def detect_python_executable(self):
        """Detect the best Python executable to use."""
        python_candidates = [
            "python3", "python", "py -3",
            "python3.11", "python3.10", "python3.9", "python3.8", "python3.7"
        ]

        for candidate in python_candidates:
            try:
                result = subprocess.run(
                    candidate.split() + ["--version"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                version = result.stdout.strip()
                if "3." in version:
                    print(f"[SUCCESS] Found Python: {candidate} ({version})")
                    return candidate.split()[0] if " " not in candidate else candidate
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue

        raise RuntimeError("No suitable Python 3.7+ installation found")

    def check_existing_venv(self):
        """Check if existing virtual environment is valid and compatible."""
        if not self.venv_dir.exists():
            return False, "Virtual environment directory does not exist"

        # Check for Python executable
        if self.platform_os == "Windows":
            python_exe = self.venv_dir / "Scripts" / "python.exe"
        else:
            python_exe = self.venv_dir / "bin" / "python"

        if not python_exe.exists():
            return False, "Python executable not found in virtual environment"

        try:
            # Test if Python works
            result = subprocess.run(
                [str(python_exe), "--version"],
                capture_output=True,
                text=True,
                check=True
            )

            # Check platform compatibility
            result_platform = subprocess.run(
                [str(python_exe), "-c", "import platform; print(platform.machine())"],
                capture_output=True,
                text=True,
                check=True
            )

            venv_arch = result_platform.stdout.strip()
            # Map to consistent naming
            arch_mapping = {
                'x86_64': 'x64',
                'AMD64': 'x64',
                'aarch64': 'ARM64',
                'arm64': 'ARM64',
                'i386': 'x86',
                'i686': 'x86'
            }
            venv_arch = arch_mapping.get(venv_arch, venv_arch)

            if venv_arch != self.platform_arch:
                return False, f"Architecture mismatch: {venv_arch} != {self.platform_arch}"

            # Check if requirements are installed
            try:
                subprocess.run(
                    [str(python_exe), "-c", "import flask"],
                    capture_output=True,
                    check=True
                )
                return True, "Virtual environment is valid and requirements are installed"
            except subprocess.CalledProcessError:
                return True, "Virtual environment is valid but requirements need installation"

        except subprocess.CalledProcessError as e:
            return False, f"Virtual environment Python is broken: {e}"

    def create_venv(self, python_cmd):
        """Create a new virtual environment in user's home directory."""
        print(f"[VENV] Creating virtual environment: {self.venv_dir}")

        # Remove existing broken venv if it exists
        if self.venv_dir.exists():
            print("[VENV] Removing existing broken virtual environment...")
            shutil.rmtree(self.venv_dir)

        # Create parent directories (including .venvs if it doesn't exist)
        try:
            self.venvs_base_dir.mkdir(parents=True, exist_ok=True)
            print(f"[VENV] Created venvs directory: {self.venvs_base_dir}")
        except OSError as e:
            print(f"[ERROR] Failed to create venvs directory: {e}")
            return False

        try:
            subprocess.run(
                [python_cmd, "-m", "venv", str(self.venv_dir)],
                check=True
            )
            print("[SUCCESS] Virtual environment created successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"[ERROR] Failed to create virtual environment: {e}")
            return False

    def install_requirements(self):
        """Install requirements in the virtual environment."""
        if self.platform_os == "Windows":
            python_exe = self.venv_dir / "Scripts" / "python.exe"
            pip_exe = self.venv_dir / "Scripts" / "pip.exe"
        else:
            python_exe = self.venv_dir / "bin" / "python"
            pip_exe = self.venv_dir / "bin" / "pip"

        print("[INSTALL] Installing requirements...")

        # Upgrade pip first
        try:
            subprocess.run(
                [str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                check=True,
                capture_output=True
            )
            print("[SUCCESS] pip upgraded successfully")
        except subprocess.CalledProcessError:
            print("[WARNING] pip upgrade failed, continuing with existing version")

        # Install requirements - look for requirements.txt in current working directory
        requirements_file = Path.cwd() / "requirements.txt"
        if not requirements_file.exists():
            print("[WARNING] requirements.txt not found in current directory")
            return False

        try:
            subprocess.run(
                [str(python_exe), "-m", "pip", "install", "-r", str(requirements_file)],
                check=True,
                capture_output=True
            )
            print("[SUCCESS] Requirements installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("[WARNING] Standard installation failed, trying with trusted hosts...")
            try:
                subprocess.run([
                    str(python_exe), "-m", "pip", "install",
                    "--trusted-host", "pypi.org",
                    "--trusted-host", "pypi.python.org",
                    "--trusted-host", "files.pythonhosted.org",
                    "-r", str(requirements_file)
                ], check=True, capture_output=True)
                print("[SUCCESS] Requirements installed with trusted hosts")
                return True
            except subprocess.CalledProcessError as e:
                print(f"[ERROR] Failed to install requirements: {e}")
                return False

    def cleanup_old_venvs(self):
        """Remove old virtual environments from project directory (legacy cleanup)."""
        # Clean up old project-based virtual environments
        project_venvs_dir = Path("venvs")
        if project_venvs_dir.exists():
            print("[CLEANUP] Cleaning up old project-based virtual environments...")
            cleaned = 0

            try:
                for venv_path in project_venvs_dir.iterdir():
                    if venv_path.is_dir():
                        print(f"[CLEANUP] Removing old project venv: {venv_path.name}")
                        try:
                            shutil.rmtree(venv_path)
                            cleaned += 1
                        except OSError as e:
                            print(f"[WARNING] Could not remove {venv_path.name}: {e}")

                # Remove the entire venvs directory if empty
                if cleaned > 0:
                    try:
                        project_venvs_dir.rmdir()
                        print("[SUCCESS] Removed old venvs directory")
                    except OSError:
                        print("[INFO] venvs directory not empty, keeping it")

                if cleaned > 0:
                    print(f"[SUCCESS] Cleaned up {cleaned} old virtual environment(s)")
                else:
                    print("[INFO] No old virtual environments to clean")
            except OSError as e:
                print(f"[WARNING] Could not access old venvs directory: {e}")

        # Note: We don't clean up other virtual environments in the home directory
        # as they might be used by other projects

    def setup(self):
        """Main setup function."""
        print("=" * 50)
        print("[SETUP] AdhocLog Virtual Environment Setup")
        print("=" * 50)
        print(f"[INFO] User: {self.username}")
        print(f"[INFO] Platform: {self.platform_os} {self.platform_arch}")
        print(f"[INFO] Home Directory: {self.home_dir}")
        print(f"[INFO] Virtual Environment: {self.venv_dir}")
        print()

        # Check existing virtual environment
        is_valid, reason = self.check_existing_venv()
        if is_valid:
            print(f"[SUCCESS] {reason}")
            if "requirements need installation" in reason:
                if not self.install_requirements():
                    return False
            self.cleanup_old_venvs()
            print("[COMPLETE] Virtual environment setup complete!")
            return True

        print(f"[INFO] {reason}")

        # Detect Python
        try:
            python_cmd = self.detect_python_executable()
        except RuntimeError as e:
            print(f"[ERROR] {e}")
            return False

        # Create virtual environment
        if not self.create_venv(python_cmd):
            return False

        # Install requirements
        if not self.install_requirements():
            return False

        # Cleanup old environments
        self.cleanup_old_venvs()

        print("[COMPLETE] Virtual environment setup complete!")
        return True


def main():
    """Main entry point."""
    try:
        manager = VirtualEnvironmentManager()
        success = manager.setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n[ERROR] Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
