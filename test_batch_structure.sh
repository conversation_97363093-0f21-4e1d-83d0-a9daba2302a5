#!/bin/bash

echo "========================================"
echo "  Testing Windows Batch File Structure"
echo "========================================"
echo

# Test 1: Check if batch files exist
echo "🔍 Test 1: Checking batch file existence..."
if [ -f "run.bat" ]; then
    echo "✅ run.bat exists"
else
    echo "❌ run.bat missing"
fi

if [ -f "launch_app.bat" ]; then
    echo "✅ launch_app.bat exists"
else
    echo "❌ launch_app.bat missing"
fi

echo

# Test 2: Check for correct script paths
echo "🔍 Test 2: Checking script paths in batch files..."
if grep -q "scripts\\\\run.py" run.bat; then
    echo "✅ run.bat uses correct script path (scripts\\run.py)"
else
    echo "❌ run.bat has incorrect script path"
fi

if grep -q "scripts\\\\run.py" launch_app.bat; then
    echo "✅ launch_app.bat uses correct script path (scripts\\run.py)"
else
    echo "❌ launch_app.bat has incorrect script path"
fi

echo

# Test 3: Check for debug output
echo "🔍 Test 3: Checking debug output additions..."
if grep -q "\\[DEBUG\\]" run.bat; then
    echo "✅ run.bat has debug output"
else
    echo "❌ run.bat missing debug output"
fi

if grep -q "\\[DEBUG\\]" launch_app.bat; then
    echo "✅ launch_app.bat has debug output"
else
    echo "❌ launch_app.bat missing debug output"
fi

echo

# Test 4: Check for error handling
echo "🔍 Test 4: Checking error handling..."
if grep -q "if !errorlevel! neq 0" run.bat; then
    echo "✅ run.bat has error handling"
else
    echo "❌ run.bat missing error handling"
fi

if grep -q "if !errorlevel! neq 0" launch_app.bat; then
    echo "✅ launch_app.bat has error handling"
else
    echo "❌ launch_app.bat missing error handling"
fi

echo

# Test 5: Check for pause statements
echo "🔍 Test 5: Checking pause statements..."
if grep -q "pause" run.bat; then
    echo "✅ run.bat has pause statements"
else
    echo "❌ run.bat missing pause statements"
fi

if grep -q "pause" launch_app.bat; then
    echo "✅ launch_app.bat has pause statements"
else
    echo "❌ launch_app.bat missing pause statements"
fi

echo

# Test 6: Check project structure
echo "🔍 Test 6: Checking project structure..."
if [ -f "scripts/run.py" ]; then
    echo "✅ scripts/run.py exists"
else
    echo "❌ scripts/run.py missing"
fi

if [ -f "app/__init__.py" ]; then
    echo "✅ app/__init__.py exists"
else
    echo "❌ app/__init__.py missing"
fi

if [ -f "requirements.txt" ]; then
    echo "✅ requirements.txt exists"
else
    echo "❌ requirements.txt missing"
fi

echo
echo "========================================"
echo "  Test Results Summary"
echo "========================================"
echo
echo "💡 The batch files have been updated with:"
echo "   • Correct script paths (scripts\\run.py)"
echo "   • Debug output for troubleshooting"
echo "   • Error handling with exit codes"
echo "   • Pause statements to prevent immediate exit"
echo
echo "🚀 Key improvements:"
echo "   • SharePoint environment detection"
echo "   • Better error messages"
echo "   • Debug information for troubleshooting"
echo "   • Proper exit handling"
echo
echo "✅ The batch files should now work properly in SharePoint environments!"
