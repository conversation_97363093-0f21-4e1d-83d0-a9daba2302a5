# 📋 AdhocLog

A robust, cross-platform Flask web application for tracking daily ad hoc tasks with integrated AI-powered analysis and conversational interface. Designed for team members to record, edit, delete, and export their daily work activities with enterprise-grade reliability and user-friendly interfaces.

## ✨ Features

### 🤖 AI-Powered Analysis & Chatbot
- **Real-time Task Analysis**: Instant AI analysis of task inputs for priority, complexity, and duration
- **Smart Classification**: Automatic categorization into 5 predefined categories with confidence scoring
- **Duration Prediction**: Intelligent time estimation based on task complexity and historical patterns
- **Similar Task Matching**: Find related tasks from your history using semantic similarity
- **Next Task Predictions**: AI suggests likely next tasks based on your patterns and current context
- **Priority Detection**: Automatic urgency assessment using advanced NLP algorithms
- **Conversational Interface**: Natural language chatbot for task management and queries
- **Temporal Understanding**: Advanced date parsing for queries like "last month", "Q1", "week 15"
- **Intelligent Suggestions**: Context-aware task recommendations and productivity insights
- **Analytics Engine**: Local user behavior tracking and optimization (privacy-safe)
- **100% Local Processing**: All AI runs on your machine - no data leaves your computer
- **Privacy First**: No external API calls, works offline, corporate network safe

### 📊 Task Management
- **Complete CRUD Operations**: Add, edit, delete, and view tasks
- **Smart Classification**: Auto-mapping from classification to category
- **Advanced Filtering**: Filter by date, classification, category, or search terms
- **CSV Export**: Export filtered tasks for reporting and analysis
- **Archive System**: Archive tasks instead of deleting (with restore capability)
- **Task Templates**: Save and reuse common task patterns (Enhanced feature)
- **Pagination**: Handle large datasets efficiently

## 🆕 Recent Major Updates & Refactoring

### 🏗️ Complete Architecture Refactoring (Latest)
We've completely restructured the application into a professional, maintainable package architecture:

#### **New Package Structure**
- **`app/` Package**: All core application logic, templates, and static assets now organized in a single package
- **`utils/` Package**: Utility scripts for setup, data management, and maintenance
- **Proper Import System**: Clean relative imports throughout the codebase
- **Enhanced Modularity**: Each component is now properly encapsulated

#### **Benefits of New Structure**
- ✅ **Better Organization**: Code is logically grouped and easier to navigate
- ✅ **Improved Maintainability**: Clear separation of concerns and dependencies
- ✅ **Enhanced Testing**: Comprehensive test suite with organized test categories
- ✅ **Professional Standards**: Follows Python packaging best practices
- ✅ **Scalability**: Structure supports future feature additions
- ✅ **Developer Experience**: Cleaner imports and better IDE support

#### **Migration Highlights**
- **27+ Test Files Updated**: All imports updated to work with new package structure
- **Flask Configuration Enhanced**: Proper static and template folder configuration
- **Import System Overhaul**: All internal imports converted to relative imports
- **Backward Compatibility**: All existing functionality preserved
- **Documentation Updated**: Complete documentation refresh for new structure

### 🤖 AI & Chatbot Enhancements
#### **New Conversational Interface**
- **Natural Language Processing**: Ask questions about your tasks in plain English
- **Temporal Query Support**: "What did I work on last week?", "Show me Q1 tasks"
- **Context-Aware Responses**: Intelligent responses based on your task history
- **Domain Safety**: Built-in boundaries prevent off-topic conversations

#### **Enhanced AI Analysis**
- **Improved Confidence Scoring**: More accurate task classification predictions
- **Pattern Recognition**: Better learning from your historical task patterns
- **Performance Optimization**: Faster analysis with improved algorithms
- **Extended Analytics**: Local user behavior tracking and productivity insights

### 📊 Data & Analytics Improvements
#### **Enhanced Data Storage**
- **Template System**: Save and reuse common task configurations
- **Analytics Tracking**: Local behavior analysis for productivity insights
- **Enhanced Keywords**: Improved AI keyword database for better analysis
- **Backup Integration**: Automated backup system for data protection

#### **Privacy-First Analytics**
- **100% Local Processing**: All analytics data stays on your machine
- **No External Calls**: Zero data transmission to external services
- **User Behavior Insights**: Track patterns without compromising privacy
- **Performance Optimization**: Identify peak productivity times and patterns

### 🧪 Testing & Quality Improvements
#### **Comprehensive Test Suite**
- **AI Engine Testing**: Complete coverage of AI analysis functionality
- **Chatbot Testing**: Conversational interface validation
- **Temporal Testing**: Complex date parsing and aggregation tests
- **Integration Testing**: End-to-end feature validation
- **Edge Case Coverage**: Comprehensive edge case and error handling tests

#### **Quality Assurance**
- **Cross-Platform Testing**: Validated on Windows, macOS, and Linux
- **Import Validation**: All new package imports thoroughly tested
- **Performance Testing**: Startup time and functionality verification
- **Backward Compatibility**: Existing data and workflows preserved

### 🖥️ User Experience
- **Dual Interface**: GUI launcher for beginners, command-line for power users
- **Auto-Detection**: Automatically detects system user and environment
- **Responsive Design**: Modern Bootstrap 5 interface that works on all devices
- **Offline Ready**: Works without internet connection after initial setup
- **Real-time Feedback**: Progress bars, status updates, and clear error messages

### 🔧 System Reliability
- **Cross-Platform**: Windows, macOS, and Linux support
- **Robust Setup**: Automatic environment detection and repair
- **Virtual Environment**: Isolated Python environment for stability
- **Error Recovery**: Comprehensive error handling and recovery procedures
- **Corporate Network Support**: Works behind firewalls and VPNs

## 🚀 Quick Start

### 🖥️ GUI Launcher (Recommended for Everyone!)

**The Easiest Way - Zero Technical Knowledge Required:**

| Platform | How to Start |
|----------|-------------|
| **Windows** | Double-click `launch_app.bat` → Choose option 1 |
| **macOS** | Double-click `launch_app.sh` → Choose option 1 |
| **Linux** | Run `./launch_app.sh` → Choose option 1 |

**GUI Launcher Features:**
- ✅ **Zero Setup Required** - Automatically installs everything
- ✅ **Point-and-Click Interface** - No command line knowledge needed
- ✅ **Real-Time Progress** - See exactly what's happening
- ✅ **Built-in Diagnostics** - Automatic problem detection and fixing
- ✅ **One-Click Operations** - Start, stop, repair with single clicks
- ✅ **Auto-Browser Launch** - Opens your web browser automatically
- ✅ **System Information** - Shows Python version, platform details
- ✅ **Intelligent Error Handling** - Clear solutions for common problems

### ⚡ Quick Command Line (For Power Users)

**Universal Launcher:**
```bash
# macOS/Linux
./launch_app.sh

# Windows
launch_app.bat
```

**Options Available:**
1. **GUI Launcher** - Graphical interface (recommended for most users)
2. **Quick Start** - Immediate application startup
3. **Advanced Options** - Setup, diagnostics, utilities, debug mode
4. **Help & Troubleshooting** - Comprehensive help system

### 🎯 Perfect for Any Team!

| User Type | Recommended Method | Why |
|-----------|-------------------|-----|
| **End Users** | GUI Launcher | Zero technical knowledge required |
| **IT Support** | Command Line + GUI | Full control with easy troubleshooting |
| **Developers** | Command Line | Direct access to all tools and options |
| **System Admins** | Automated Setup Script | Scriptable deployment and maintenance |

## 📦 Installation Options

### 🔄 Automated Setup (Recommended)

**One-Command Setup:**
```bash
# Run the automated setup and repair tool
python3 setup_repair.py
```

This tool will:
- ✅ Validate Python installation and version
- ✅ Test tkinter availability for GUI support
- ✅ Create/repair virtual environment automatically
- ✅ Configure pip for corporate networks
- ✅ Install all dependencies with fallback strategies
- ✅ Create required directories
- ✅ Test Flask installation
- ✅ Provide detailed success/error reporting

### 🛠️ Manual Setup (When Automation Fails)

**Step-by-Step Manual Installation:**

1. **Verify Python Installation:**
   ```bash
   python3 --version  # Should be 3.7 or higher
   ```

2. **Create Virtual Environment:**
   ```bash
   python3 -m venv venv
   ```

3. **Activate Virtual Environment:**
   ```bash
   # Windows:
   venv\Scripts\activate

   # macOS/Linux:
   source venv/bin/activate
   ```

4. **Upgrade pip:**
   ```bash
   python -m pip install --upgrade pip
   ```

5. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

6. **Create Data Directory:**
   ```bash
   mkdir data  # Windows: mkdir data
   ```

7. **Test Installation:**
   ```bash
   python -c "from app import app; print('Installation successful!')"
   ```

**For detailed manual setup instructions, see [MANUAL_SETUP.md](app/docs/MANUAL_SETUP.md)**

### 🆘 Emergency Setup (Last Resort)

**If everything fails, use system Python:**
```bash
# Install globally (not recommended but works)
pip3 install flask python-dotenv werkzeug

# Run directly
python3 scripts/run.py
```

## 📁 Project Structure

```
adhoc-log-app/
├── 🚀 Entry Points & Launchers
│   ├── launch_app.sh          # Universal launcher (macOS/Linux)
│   ├── launch_app.bat         # Universal launcher (Windows)
│   ├── gui_launcher.py        # GUI launcher application
│   ├── scripts/
│   │   ├── run.py             # Direct application runner
│   └── run.bat               # Windows batch runner
│
├── 📱 Core Application Package
│   └── app/                   # Main application package
│       ├── __init__.py        # Package initialization
│       ├── app.py             # Main Flask application
│       ├── config.py          # Configuration settings
│       ├── data_manager.py    # Data management layer
│       ├── ai_engine.py       # AI analysis engine
│       ├── chatbot_engine.py  # Conversational AI interface
│       ├── analytics.py       # User behavior analytics (local)
│       ├── static/            # Web assets
│       │   ├── css/          # Stylesheets (Bootstrap 5 + custom)
│       │   ├── js/           # JavaScript files
│       │   └── images/       # Image assets
│       └── templates/         # HTML templates
│           ├── base.html     # Base template with Bootstrap 5
│           ├── index.html    # Dashboard with AI insights
│           ├── tasks.html    # Task list with filtering
│           ├── add_task.html # Task creation with AI analysis
│           ├── edit_task.html # Task editing
│           ├── archive.html  # Archive management
│           ├── 404.html      # Error pages
│           └── 500.html
│
├── 🛠️ Utility Scripts & Tools
│   ├── utils/                 # Utility package
│   │   ├── __init__.py        # Package initialization
│   │   ├── create_sample_data.py  # Sample data generator
│   │   ├── setup_repair.py    # Automated setup and repair tool
│   │   ├── temp_backup.py     # Backup utility
│   │   └── temp_template_methods.py  # Template helper methods
│   └── scripts/               # Operational scripts
│       ├── run.sh            # Basic startup script
│       ├── run_robust.sh     # Enhanced startup with error handling
│       ├── run_debug.sh      # Debug mode startup
│       ├── diagnose.sh       # System diagnostics
│       ├── clear_data.sh     # Data cleanup utility
│       ├── setup_missing_files.sh  # File repair utility
│       └── regenerate_sample_data.sh  # Data regeneration
│
├── 📊 Data Storage (User-Isolated)
│   ├── data/                 # User data storage (auto-created)
│   │   ├── tasks_<username>.json          # Active tasks
│   │   ├── archived_tasks_<username>.json # Archived tasks
│   │   ├── templates_<username>.json      # Task templates
│   │   ├── user_events.json              # User interaction events
│   │   ├── analytics.json                # Analytics data (local)
│   │   └── enhanced_keywords.json        # AI keyword database
│
├── 🧪 Testing Suite
│   └── tests/                # Comprehensive test suite
│       ├── __init__.py       # Test package
│       ├── test_flask.py     # Flask application tests
│       ├── test_all_features.py          # Comprehensive feature tests
│       ├── test_ai_engine_analysis.py    # AI engine testing
│       ├── test_ai_comprehensive.py      # Comprehensive AI tests
│       ├── test_chatbot.py              # Chatbot functionality tests
│       ├── test_conversation_context.py  # Conversation state tests
│       ├── test_temporal_edge_cases.py   # Date parsing edge cases
│       ├── test_date_parsing.py         # Date parsing validation
│       └── test_temporal_aggregation.py  # Temporal data aggregation
│
├── 🔧 Environment & Dependencies
│   ├── requirements.txt       # Python dependencies
│   ├── .env                   # Environment variables (optional)
│   ├── pyvenv.cfg            # Virtual environment config (auto-created)
│   └── venv/                 # Virtual environment (auto-created)
│       ├── bin/             # Executables (macOS/Linux)
│       ├── Scripts/         # Executables (Windows)
│       └── lib/             # Python packages
│
├── 📚 Documentation
│   ├── README.md             # This comprehensive guide
│   └── docs/                 # Detailed documentation
│       ├── MANUAL_SETUP.md           # Manual installation guide
│       ├── TROUBLESHOOTING.md        # Comprehensive troubleshooting
│       ├── PRD.md                   # Product requirements document
│       ├── AI_ANALYSIS_DOCUMENTATION.md    # Complete AI engine documentation
│       ├── AI_QUICK_REFERENCE.md           # AI features quick reference
│       ├── AI_TECHNICAL_IMPLEMENTATION.md  # AI technical implementation guide
│       ├── AI_CHATBOT_GUIDE.md             # Chatbot usage and features
│       ├── CHATBOT_INTEGRATION_CHECKLIST.md # Chatbot integration guide
│       ├── TASK_FORM_IMPROVEMENTS.md       # Task form enhancement roadmap
│       ├── TEMPORAL_EDGE_CASE_IMPLEMENTATION.md # Date parsing implementation
│       ├── ENHANCEMENT_ROADMAP.md          # Future development plans
│       ├── PHASE_3_SUMMARY.md             # Recent development summary
│       └── BACKUP_RESTORE_GUIDE.md        # Data backup and restore guide
│
└── 📦 Auto-Generated Artifacts
    ├── backups/              # Automated backups
    │   └── backup_<timestamp>/
    └── __pycache__/          # Python bytecode cache

### 🎯 Key Files & Components Explained

| File/Directory | Purpose | When to Use |
|----------------|---------|-------------|
| `launch_app.sh/.bat` | Universal launcher with GUI/CLI options | Primary way to start application |
| `gui_launcher.py` | Graphical user interface | For non-technical users |
| `scripts/run.py` | Direct application runner | For quick starts and automation |
| **`app/`** | **Main application package** | **Core application logic and assets** |
| `app/app.py` | Main Flask application | Core Flask routes and configuration |
| `app/ai_engine.py` | AI analysis engine | Real-time task analysis and predictions |
| `app/chatbot_engine.py` | Conversational AI interface | Natural language task management |
| `app/analytics.py` | User behavior analytics | Local usage tracking and optimization |
| `app/data_manager.py` | Data management layer | JSON file operations and user isolation |
| `app/config.py` | Configuration settings | Classification mappings and constants |
| `app/static/` | Web assets (CSS, JS, images) | Frontend styling and interactivity |
| `app/templates/` | HTML templates | Jinja2 templates with Bootstrap 5 |
| **`utils/`** | **Utility scripts package** | **Setup, maintenance, and data tools** |
| `utils/setup_repair.py` | Automated setup and repair | When installation issues occur |
| `utils/create_sample_data.py` | Sample data generator | For testing and demos |
| **`tests/`** | **Comprehensive test suite** | **For running tests and validation** |
| `tests/test_ai_*.py` | AI engine testing | Validating AI analysis functionality |
| `tests/test_chatbot*.py` | Chatbot testing | Conversational interface validation |
| `tests/test_temporal_*.py` | Date parsing testing | Complex temporal query validation |
| **`docs/`** | **Complete documentation** | **Understanding features and troubleshooting** |
| `docs/AI_*.md` | AI documentation suite | Understanding AI capabilities and implementation |
| `docs/MANUAL_SETUP.md` | Manual installation guide | When automation fails |
| `docs/TROUBLESHOOTING.md` | Problem-solving guide | When encountering issues |

## 🤖 AI Analysis & Chatbot Features

The AdhocLog AI engine provides intelligent task analysis and conversational interface **completely locally** - no data ever leaves your machine!

### What the AI Does
- **Real-time Analysis**: Instant analysis of every task you create with confidence scoring
- **Smart Classification**: Automatically categorizes tasks into 5 predefined categories
- **Duration Prediction**: Estimates how long tasks will take based on complexity analysis
- **Priority Detection**: Identifies urgent vs. routine tasks using NLP algorithms
- **Pattern Learning**: Learns from your task history to improve suggestions over time
- **Similar Task Matching**: Finds related tasks from your history using semantic similarity
- **Context-Aware Suggestions**: Provides intelligent next task recommendations

### Conversational AI Chatbot
- **Natural Language Interface**: Ask questions like "What did I work on last week?"
- **Temporal Understanding**: Supports complex date queries ("Q1", "last month", "week 15")
- **Task Management**: Create, edit, and search tasks using natural language
- **Analytics Queries**: Get insights about your work patterns and productivity
- **Intelligent Responses**: Context-aware responses with task-specific information
- **Conversation Memory**: Maintains context throughout your conversation session
- **Domain-Safe**: Built-in boundaries prevent off-topic queries (medical, financial, etc.)

### Local Analytics Engine
- **User Behavior Tracking**: Monitors task patterns and productivity insights (locally stored)
- **Performance Optimization**: Identifies peak productivity times and task efficiency
- **Workload Analysis**: Tracks task distribution and time allocation patterns
- **Privacy-Safe Analytics**: All analytics data stays on your machine

### AI Documentation
📚 **Complete AI Documentation**: See [`docs/AI_ANALYSIS_DOCUMENTATION.md`](app/docs/AI_ANALYSIS_DOCUMENTATION.md) for comprehensive details

🤖 **Chatbot Guide**: See [`docs/AI_CHATBOT_GUIDE.md`](app/docs/AI_CHATBOT_GUIDE.md) for conversational interface details

🚀 **Quick Reference**: See [`docs/AI_QUICK_REFERENCE.md`](app/docs/AI_QUICK_REFERENCE.md) for a quick overview

⚙️ **Technical Details**: See [`docs/AI_TECHNICAL_IMPLEMENTATION.md`](app/docs/AI_TECHNICAL_IMPLEMENTATION.md) for implementation details

### AI Examples
| Input | AI Analysis |
|-------|-------------|
| "Daily standup" | 📋 Business Support (85% confidence) • ⏱️ 25 min • 🔹 Medium priority |
| "Fix authentication bug" | ⚡ Execution (90% confidence) • ⏱️ 60 min • 🔴 High priority |
| "Design new architecture" | 📐 Planning (95% confidence) • ⏱️ 90 min • 🟡 Medium complexity |

### Chatbot Examples
| User Query | AI Response |
|------------|-------------|
| "What did I work on last Friday?" | Shows tasks from previous Friday with time breakdown |
| "How many hours did I spend on planning tasks this month?" | Calculates and displays planning task hours for current month |
| "Create a task for code review" | Guides through task creation with AI suggestions |
| "Show me similar tasks to yesterday's bug fix" | Finds and displays semantically similar debugging tasks |

> 🔒 **Privacy Guarantee**: All AI processing happens on your local machine. No data is sent to external servers or APIs.
> 📊 **Analytics Privacy**: User behavior analytics are stored locally in JSON files and never transmitted.

## 🎯 Usage

### Dashboard
- View today's tasks and recent activity
- Quick stats on tasks and time spent
- Easy access to add new tasks

### Task Management
- **Add Task**: Fill out the form with title, classification, description, and estimated time
- **AI-Powered Input**: Get real-time AI suggestions as you type
- **Quick Add**: Select from recent tasks to quickly create similar ones
- **Smart Templates**: Use predefined templates for common tasks (🚀 Enhancement in progress)
- **Edit Task**: Click the edit button on any task to modify it
- **Archive Task**: Archive tasks instead of deleting (can be restored later)
- **Filter Tasks**: Use date ranges, classification, or search terms
- **Pagination**: Navigate through large task lists with page controls
- **Export Data**: Download filtered tasks as CSV for reporting

> 📋 **Task Form Improvements**: We're actively enhancing the task creation experience with templates, smart suggestions, and better mobile support. See [TASK_FORM_IMPROVEMENTS.md](docs/TASK_FORM_IMPROVEMENTS.md) for our complete roadmap and progress.

### Archive System
- **Archive Tasks**: Tasks are archived instead of permanently deleted
- **View Archive**: Access archived tasks from the Archive menu
- **Restore Tasks**: Bring archived tasks back to active status
- **Permanent Delete**: Permanently remove tasks from archive (cannot be undone)

### Classifications & Categories

| Classification | Auto-mapped Category |
|---------------|---------------------|
| Planning | Adhoc |
| Offline Processing | Adhoc |
| Execution | Adhoc |
| Business Support Activities | Business Support Activities |
| Operational Project Involvement | Adhoc |

## 🔧 Configuration

### Environment Variables
Create a `.env` file for custom configuration:
```bash
SECRET_KEY=your-secret-key-here
```

### Data Storage
- Data is stored in `data/tasks_<username>.json`
- Each user gets their own data file
- Automatic user detection via system username

## 🛠️ Development

### Adding New Classifications
Edit `config.py` and update the `CLASSIFICATION_MAPPING` dictionary:
```python
CLASSIFICATION_MAPPING = {
    'Bug Fix': 'Issue',
    'New Classification': 'New Category',
    # ... existing mappings
}
```

### Customizing the UI
- Templates are in the `templates/` folder
- Uses Bootstrap 5 for styling
- Custom CSS can be added to `static/css/`

## 📊 Data Format

Tasks are stored as JSON with the following structure:
```json
{
  "id": 1,
  "date": "2024-01-15",
  "team_member": "username",
  "title": "Task title",
  "classification": "Bug Fix",
  "category": "Issue",
  "description": "Task description",
  "est_time": 45
}
```

## 📋 System Requirements

### Minimum Requirements
- **Python:** 3.7 or higher (3.9+ recommended)
- **Operating System:**
  - Windows 10 or higher
  - macOS 10.14 (Mojave) or higher
  - Linux (Ubuntu 18.04+, CentOS 7+, or equivalent)
- **Memory:** 512MB RAM minimum (1GB recommended)
- **Storage:** 100MB free space
- **Network:** Internet connection for initial setup only

### Supported Python Installations
- **Official Python** (python.org) - ✅ Fully supported
- **Homebrew Python** (macOS) - ✅ Fully supported
- **Microsoft Store Python** (Windows) - ✅ Supported
- **System Python** (Linux) - ✅ Supported
- **Anaconda/Miniconda** - ✅ Supported
- **pyenv** - ✅ Supported

### GUI Requirements (Optional)
- **tkinter** - Required for GUI launcher
- **Display environment** - X11, Wayland, or native GUI
- **Not required for command-line usage**

## 🚨 Troubleshooting

### 🔍 Quick Diagnosis

**Run Comprehensive Diagnostics:**
```bash
# Automated diagnosis and repair
python3 setup_repair.py

# Platform-specific diagnostics
./launch_app.sh → Option 3 → Option 2    # macOS/Linux
launch_app.bat → Option 3 → Option 2     # Windows
```

### 🛠️ Built-in Tools

| Tool | Purpose | How to Access |
|------|---------|---------------|
| **Setup/Repair** | Fix broken environments | Launcher → Advanced → Setup |
| **Diagnostics** | System analysis | Launcher → Advanced → Diagnostics |
| **GUI Launcher** | User-friendly interface | Launcher → Option 1 |
| **Manual Setup** | Step-by-step guide | See [MANUAL_SETUP.md](app/docs/MANUAL_SETUP.md) |

### 🚨 Common Issues & Solutions

#### Python Issues
| Problem | Solution |
|---------|----------|
| Python not found | Install from [python.org](https://python.org) |
| Wrong Python version | Install Python 3.7+ |
| Permission denied | Run as administrator or use `--user` flag |
| PATH issues | Add Python to system PATH |

#### GUI Issues
| Problem | Solution |
|---------|----------|
| GUI won't start | Install tkinter: `brew install python-tk` (macOS) |
| tkinter missing | Reinstall Python with tkinter support |
| SSH session | Use command-line launcher instead |
| Headless system | Use Quick Start option |

#### Network Issues
| Problem | Solution |
|---------|----------|
| pip install fails | Use automated setup (handles corporate networks) |
| SSL certificate errors | Setup configures trusted hosts automatically |
| Corporate firewall | Run Setup/Repair to configure pip |
| Proxy issues | Contact IT or see [TROUBLESHOOTING.md](app/docs/TROUBLESHOOTING.md) |

#### Virtual Environment Issues
| Problem | Solution |
|---------|----------|
| venv creation fails | Use automated repair: `python3 setup_repair.py` |
| Broken environment | Setup/Repair will recreate automatically |
| Missing Python executable | Automated repair detects and fixes |
| Permission issues | Run as administrator or check file permissions |

### 📚 Detailed Troubleshooting

For comprehensive troubleshooting guides:
- **[TROUBLESHOOTING.md](app/docs/TROUBLESHOOTING.md)** - Detailed problem-solving guide
- **[MANUAL_SETUP.md](app/docs/MANUAL_SETUP.md)** - Step-by-step manual installation
- **Built-in Help** - Run launcher and choose "Help & Troubleshooting"

## 🔒 Security & Privacy

### Data Security
- **Local Storage Only** - All data stays on your machine
- **No Cloud Dependencies** - Works completely offline after setup
- **User Isolation** - Each user gets their own data files
- **No Authentication Required** - Single-user application design
- **No Network Access** - Application doesn't communicate externally

### Privacy Features
- **Automatic User Detection** - Uses system username
- **Local JSON Storage** - Human-readable data format
- **No Telemetry** - No usage data collection
- **No External APIs** - No third-party service dependencies

## 🚀 Advanced Usage

### Command Line Options
```bash
# Direct application start
python3 scripts/run.py

# Debug mode
python3 scripts/run.py --debug

# Custom port
FLASK_RUN_PORT=8080 python3 scripts/run.py

# Using Flask directly
flask run --port 8080 --debug
```

### Environment Variables
Create a `.env` file for custom configuration:
```bash
SECRET_KEY=your-secret-key-here
FLASK_RUN_PORT=8080
FLASK_DEBUG=1
```

### Corporate Deployment
```bash
# Automated deployment script
python3 setup_repair.py

# Verify installation
python3 -c "from app import app; print('Deployment successful')"

# Start application
python3 scripts/run.py
```

### SharePoint Multi-User Deployment
AdhocLog includes advanced virtual environment management for SharePoint deployments:

#### **Automatic Multi-User Support**
- **User Isolation**: Each user gets their own virtual environment
- **Platform Compatibility**: Prevents cross-platform conflicts (Windows/Mac/Linux)
- **Zero Configuration**: Automatic setup on first run
- **SharePoint Optimized**: Virtual environments excluded from sync

#### **Virtual Environment Management**
```bash
# Automatic setup (runs during normal launch)
./launch_app.sh  # or launch_app.bat on Windows

# Manual virtual environment setup
python3 utils/setup_venv.py

# Test virtual environment functionality
python3 test_venv_management.py
```

#### **SharePoint Deployment Features**
- ✅ **Multiple Users**: Simultaneous access without conflicts
- ✅ **Cross-Platform**: Windows, macOS, Linux support
- ✅ **Corporate Networks**: Works behind firewalls/proxies
- ✅ **Sync Optimization**: Virtual environments excluded from sync
- ✅ **Automatic Detection**: OneDrive, Teams, SharePoint sites
- ✅ **Graceful Fallbacks**: System Python if virtual env fails

#### **Virtual Environment Naming**
```
venvs/user_{username}_{platform}_{architecture}/
```
Examples:
- `venvs/user_john.doe_Windows_x64/`
- `venvs/user_jane.smith_Darwin_ARM64/`
- `venvs/user_admin_Linux_x64/`

## 🛠️ Development & Customization

### New Package-Based Architecture
The application has been refactored into a clean, organized package structure:

- **`app/` package**: Contains all core application logic, templates, and static assets
- **`utils/` package**: Contains utility scripts for setup, data management, and maintenance
- **`tests/` directory**: Comprehensive test suite with AI, chatbot, and temporal testing
- **Proper imports**: All modules use relative imports within packages

### Adding New Classifications
Edit `app/config.py` and update the `CLASSIFICATION_MAPPING`:
```python
CLASSIFICATION_MAPPING = {
    'Bug Fix': 'Issue',
    'New Classification': 'New Category',
    # ... existing mappings
}
```

### Customizing the UI
- **Templates**: Modify files in `app/templates/` directory
- **Styling**: Uses Bootstrap 5, add custom CSS to `app/static/css/`
- **JavaScript**: Add custom scripts to `app/static/js/`
- **Components**: Reusable components like pagination are in separate template files

### Adding AI Features
- **AI Engine**: Extend `app/ai_engine.py` for new analysis capabilities
- **Chatbot**: Enhance `app/chatbot_engine.py` for new conversation features
- **Analytics**: Add tracking features in `app/analytics.py` (local storage only)

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd adhoc-log-app

# Setup development environment
python3 utils/setup_repair.py

# Run in debug mode
python3 scripts/run.py --debug

# Test the new package structure
python3 -c "from app import app; print('Import successful')"
```

### Package Import Examples
```python
# Main application
from app import app

# Individual components
from app.data_manager import DataManager
from app.ai_engine import AIEngine
from app.chatbot_engine import ChatbotEngine
from app.analytics import Analytics
from app.config import Config

# Utilities
from utils.create_sample_data import create_sample_data
from utils.setup_repair import setup_environment
```

### Testing the New Structure
```bash
# Run comprehensive tests
python3 -m pytest tests/

# Test specific components
python3 tests/test_ai_comprehensive.py
python3 tests/test_chatbot.py
python3 tests/test_all_features.py

# Test package imports
python3 -c "from app.ai_engine import AIEngine; print('AI Engine import successful')"
python3 -c "from app.chatbot_engine import ChatbotEngine; print('Chatbot import successful')"
```

## 📊 Data Management

### Data Format
Tasks are stored as JSON with this structure:
```json
{
  "id": 1,
  "date": "2024-01-15",
  "team_member": "username",
  "title": "Task title",
  "classification": "Bug Fix",
  "category": "Issue",
  "description": "Task description",
  "est_time": 45
}
```

### Backup & Migration
```bash
# Backup data
cp -r data/ backup_$(date +%Y%m%d)/

# Migrate to new system
# Copy data/ directory to new installation
```

## 🆘 Support & Help

### Getting Help
1. **Built-in Help** - Use launcher Help & Troubleshooting option
2. **Automated Diagnostics** - Run `python3 setup_repair.py`
3. **Manual Guides** - Check [MANUAL_SETUP.md](app/docs/MANUAL_SETUP.md) and [TROUBLESHOOTING.md](app/docs/TROUBLESHOOTING.md)
4. **System Administrator** - Provide diagnostic output for support

### Reporting Issues
When reporting problems, include:
- Operating system and version
- Python version (`python3 --version`)
- Error messages (full text)
- Output from diagnostic tools

## 📝 License

This project is for internal organizational use. Modify and distribute as needed within your organization.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Test thoroughly on multiple platforms
5. Update documentation if needed
6. Submit a pull request

### Development Guidelines
- **Cross-Platform Compatibility** - Test on Windows, macOS, and Linux
- **Error Handling** - Provide clear error messages and recovery options
- **Documentation** - Update README and guides for any new features
- **Backward Compatibility** - Maintain compatibility with existing data

---

**Happy Task Tracking! 📋✨**

*Built with ❤️ for teams who need reliable, cross-platform task tracking without the complexity.*
