<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Form Submission</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Debug Form Submission</h1>
    
    <div class="debug">
        <h3>Debug Output:</h3>
        <div id="debug-log"></div>
    </div>

    <form id="test-form" action="http://127.0.0.1:5000/tasks/add" method="POST">
        <div>
            <label>Title: <input type="text" name="title" value="Debug Test Task" required></label>
        </div>
        <div>
            <label>Classification: <input type="text" name="classification" value="Planning" required></label>
        </div>
        <div>
            <label>Description: <input type="text" name="description" value="Debug test"></label>
        </div>
        <div>
            <label>Est Time: <input type="number" name="est_time" value="30"></label>
        </div>
        <div>
            <label>Date: <input type="date" name="date" value="2025-08-01"></label>
        </div>
        <button type="submit" id="submit-btn">Submit Test Form</button>
    </form>

    <script>
        function debugLog(message) {
            const logDiv = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += '<div>' + timestamp + ': ' + message + '</div>';
            console.log(timestamp + ': ' + message);
        }

        // Simulate the AddTaskManager handleFormSubmit logic
        function handleFormSubmit(event) {
            debugLog('Form submit event triggered');
            
            const form = event.target;
            const submitButton = form.querySelector('button[type="submit"]');
            
            // Check form validity first
            if (!form.checkValidity()) {
                debugLog('Form validation failed');
                event.preventDefault();
                event.stopPropagation();
                form.classList.add('was-validated');
                return false;
            }

            debugLog('Form validation passed, allowing submission');

            // Set processing state immediately for valid forms
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = 'Adding Task...';
                debugLog('Button state changed to processing');
            }

            form.classList.add('was-validated');

            // Safety timeout to reset button state (in case of server errors)
            setTimeout(() => {
                if (submitButton && submitButton.disabled) {
                    debugLog('Form submission timeout - button reset');
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Submit Test Form';
                }
            }, 10000);

            // Allow form to submit naturally - DO NOT prevent default
            debugLog('Returning true - form should submit');
            return true;
        }

        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded');
            
            const form = document.getElementById('test-form');
            if (form) {
                debugLog('Form found, adding event listener');
                form.addEventListener('submit', handleFormSubmit);
            } else {
                debugLog('ERROR: Form not found!');
            }
        });
    </script>
</body>
</html>
