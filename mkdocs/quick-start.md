# Quick Start Guide

Get up and running with <PERSON>hoc<PERSON><PERSON> in just a few minutes! This guide will walk you through the essential steps to start tracking your tasks effectively.

## 🚀 Launch AdhocLog

### Step 1: Start the Application

=== "Windows"

    1. Navigate to your AdhocLog folder
    2. Double-click `launch_app.bat` or `run.bat`
    3. Choose your preferred launch option:
       - **GUI Launcher** (recommended for beginners)
       - **Quick Start** (direct launch)

=== "macOS/Linux"

    1. Open Terminal in your AdhocLog folder
    2. Run: `./launch_app.sh`
    3. Choose your preferred launch option

### Step 2: Access the Web Interface

1. **Wait** for the startup message showing the URL
2. **Open your browser** to the displayed address (usually `http://localhost:8000`)
3. **Bookmark** the URL for easy access

!!! tip "Multiple Ports"
    If port 8000 is busy, AdhocLog will automatically find an available port. Check the terminal output for the correct URL.

## 📝 Your First Task

### Creating a Task

1. **Click** the "Add New Task" button on the main page
2. **Fill in** the task details:
   - **Task Name**: Brief description of what you're working on
   - **Classification**: Choose a category (or create a new one)
   - **Start Time**: Usually auto-filled with current time
   - **Notes**: Additional details or context

3. **Click** "Add Task" to save

### Example Task
```
Task Name: Review quarterly reports
Classification: Analysis
Start Time: 2025-01-31 09:00
Notes: Focus on Q4 performance metrics and budget variance
```

### Working on Tasks

1. **Start working** on your task
2. **Update progress** by editing the task if needed
3. **Add notes** as you work to capture important details
4. **Set end time** when you finish

## 🤖 Explore AI Features

### AI Analysis

1. **Navigate** to the "Statistics" or "Analytics" page
2. **Click** "Generate AI Analysis"
3. **Review** the insights about your productivity patterns
4. **Use** the recommendations to improve your workflow

### AI Chatbot

1. **Look** for the chatbot interface (usually in the sidebar or bottom)
2. **Ask questions** like:
   - "What tasks should I prioritize today?"
   - "How much time did I spend on analysis this week?"
   - "Give me productivity tips"
3. **Follow** the chatbot's suggestions

## 📊 View Your Progress

### Dashboard Overview

The main dashboard shows:

- **Recent tasks** and their status
- **Time spent** on different activities
- **Productivity metrics** and trends
- **Quick actions** for common operations

### Analytics Page

Visit the analytics page to see:

- **Time distribution** across different task types
- **Productivity trends** over time
- **Task completion rates**
- **AI-generated insights**

## 🔧 Basic Configuration

### Customizing Classifications

1. **Create tasks** with different classifications
2. **Use consistent naming** for better analytics
3. **Common classifications**:
   - Meetings
   - Analysis
   - Development
   - Documentation
   - Administration

### Setting Preferences

1. **Access** the settings (usually in the navigation menu)
2. **Configure**:
   - Default time formats
   - Auto-save intervals
   - Notification preferences
   - Export formats

## 📤 Export Your Data

### Quick Export

1. **Go** to the main tasks page
2. **Click** "Export" or "Download"
3. **Choose** your preferred format:
   - CSV for spreadsheets
   - JSON for data processing
   - PDF for reports

### Scheduled Exports

Set up automatic exports for regular reporting:

1. **Configure** export schedules in settings
2. **Choose** export frequency (daily, weekly, monthly)
3. **Select** destination folder

## 🎯 Best Practices

### Task Naming

- **Be specific**: "Review Q4 budget" vs "Review stuff"
- **Use action verbs**: "Analyze", "Create", "Review", "Update"
- **Include context**: "Client meeting - Project Alpha"

### Time Tracking

- **Start tasks** when you begin working
- **Update regularly** to maintain accuracy
- **Use notes** to capture important details
- **End tasks** promptly when finished

### Classifications

- **Be consistent** with naming conventions
- **Use broad categories** that apply to multiple tasks
- **Create subcategories** using notes if needed

## 🔄 Daily Workflow

### Morning Routine

1. **Open** AdhocLog
2. **Review** yesterday's completed tasks
3. **Plan** today's priorities
4. **Create** initial tasks for the day

### During Work

1. **Start** each task when you begin
2. **Add notes** as you work
3. **Update** progress regularly
4. **Use** the AI chatbot for guidance

### End of Day

1. **Complete** any unfinished tasks
2. **Review** the day's productivity
3. **Check** AI insights
4. **Plan** for tomorrow

## 🆘 Quick Help

### Common Actions

| Action | How To |
|--------|--------|
| Add Task | Click "Add New Task" button |
| Edit Task | Click on task name or edit icon |
| Delete Task | Use delete button (⚠️ permanent) |
| Export Data | Use Export button on tasks page |
| Get AI Help | Use the chatbot interface |

### Keyboard Shortcuts

- **Ctrl+N** (Cmd+N): New task
- **Ctrl+S** (Cmd+S): Save current task
- **Ctrl+E** (Cmd+E): Export data
- **F5**: Refresh page

## 🎉 Next Steps

Now that you're up and running:

1. **Explore** the [User Guide](user-guide/basic-usage.md) for detailed features
2. **Learn** about [AI Features](ai-features/analysis.md) for advanced insights
3. **Deploy** to SharePoint environments by ensuring proper file permissions and access
4. **Check out** [Analytics](user-guide/analytics.md) for productivity insights

## 💡 Tips for Success

- **Start simple**: Begin with basic task tracking
- **Be consistent**: Use AdhocLog daily for best results
- **Leverage AI**: Use the chatbot and analysis features
- **Export regularly**: Keep backups of your data
- **Customize**: Adapt the system to your workflow

Happy task tracking! 🚀
