# Configuration Guide

Learn how to configure <PERSON>ho<PERSON><PERSON>og to match your specific needs and environment. This guide covers all configuration options from basic settings to advanced enterprise deployment.

## 🔧 Configuration Overview

AdhocLog can be configured through:

- **Environment variables** - Runtime configuration
- **Configuration files** - Application settings
- **Command-line options** - Launch parameters
- **Web interface** - User preferences

## 🌍 Environment Variables

### Core Settings

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `FLASK_RUN_PORT` | Web server port | `8000` | `5000` |
| `FLASK_RUN_HOST` | Web server host | `127.0.0.1` | `0.0.0.0` |
| `FLASK_DEBUG` | Debug mode | `False` | `True` |
| `FLASK_ENV` | Environment mode | `production` | `development` |

### AdhocLog Specific

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `ADHOCLOG_DATA_DIR` | Data storage directory | `data` | `/path/to/data` |
| `ADHOCLOG_USER_DATA_DIR` | User-specific data directory | `data` | `data/user_john` |
| `ADHOCLOG_SHAREPOINT_MODE` | Enable SharePoint mode | `0` | `1` |
| `ADHOCLOG_BACKUP_DIR` | Backup storage directory | `backups` | `/backup/adhoclog` |

### Python Environment

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `PYTHONDONTWRITEBYTECODE` | Disable .pyc files | `0` | `1` |
| `PYTHONPYCACHEPREFIX` | Cache directory | None | `/tmp/pycache` |
| `VIRTUAL_ENV` | Virtual environment path | None | `/path/to/venv` |

### Setting Environment Variables

=== "Windows"

    **Temporary (current session):**
    ```cmd
    set FLASK_RUN_PORT=5000
    set ADHOCLOG_SHAREPOINT_MODE=1
    ```

    **Permanent (system-wide):**
    ```cmd
    setx FLASK_RUN_PORT 5000
    setx ADHOCLOG_SHAREPOINT_MODE 1
    ```

=== "macOS/Linux"

    **Temporary (current session):**
    ```bash
    export FLASK_RUN_PORT=5000
    export ADHOCLOG_SHAREPOINT_MODE=1
    ```

    **Permanent (user profile):**
    ```bash
    echo 'export FLASK_RUN_PORT=5000' >> ~/.bashrc
    echo 'export ADHOCLOG_SHAREPOINT_MODE=1' >> ~/.bashrc
    source ~/.bashrc
    ```

## 📁 Configuration Files

### Main Configuration (config.py)

The main configuration file contains application settings:

```python
class Config:
    # Application settings
    SECRET_KEY = 'your-secret-key-here'
    DEBUG = False

    # Data settings
    DATA_DIR = 'data'
    BACKUP_DIR = 'backups'
    MAX_BACKUP_COUNT = 10

    # AI settings
    AI_ENABLED = True
    CHATBOT_ENABLED = True

    # Export settings
    EXPORT_FORMATS = ['csv', 'json', 'xlsx']
    DEFAULT_EXPORT_FORMAT = 'csv'

    # Time settings
    TIMEZONE = 'UTC'
    DATE_FORMAT = '%Y-%m-%d'
    TIME_FORMAT = '%H:%M:%S'
```

### User Preferences

User-specific settings are stored in the data directory:

```json
{
    "theme": "light",
    "language": "en",
    "timezone": "America/New_York",
    "notifications": {
        "enabled": true,
        "sound": false,
        "desktop": true
    },
    "export": {
        "default_format": "csv",
        "include_notes": true,
        "date_range": "last_30_days"
    }
}
```

## 🌐 SharePoint Configuration

### Automatic Configuration

For SharePoint/OneDrive environments, AdhocLog automatically configures:

```bash
# Detected automatically
SHAREPOINT_MODE=true
SHAREPOINT_TYPE="OneDrive Business"
USER_DATA_DIR="data/user_${USERNAME}"
PYTHONDONTWRITEBYTECODE=1
```

### Manual SharePoint Setup

For manual SharePoint configuration:

1. **Set environment variables:**
   ```bash
   export ADHOCLOG_SHAREPOINT_MODE=1
   export ADHOCLOG_USER_DATA_DIR="data/user_$(whoami)"
   export PYTHONDONTWRITEBYTECODE=1
   ```

2. **Create user data directory:**
   ```bash
   mkdir -p "data/user_$(whoami)"
   ```

3. **Configure virtual environment:**
   ```bash
   export VENV_DIR="$HOME/.venvs/adhoc-log-app"
   ```

### Multi-User Configuration

For multi-user SharePoint deployments:

```yaml
# sharepoint-config.yml
users:
  isolation: true
  data_prefix: "user_"
  venv_location: "home_directory"

cache:
  isolation: true
  location: "temp"
  cleanup: true

permissions:
  read_others: false
  write_others: false
  admin_access: ["admin_user1", "admin_user2"]
```

## 🔒 Security Configuration

### Authentication

```python
# config.py
class SecurityConfig:
    # Session security
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

    # CSRF protection
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600

    # Content security
    CONTENT_SECURITY_POLICY = {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline'",
        'style-src': "'self' 'unsafe-inline'"
    }
```

### Data Protection

```python
# Data encryption settings
ENCRYPTION_ENABLED = True
ENCRYPTION_KEY = 'your-encryption-key'
BACKUP_ENCRYPTION = True

# Data retention
DATA_RETENTION_DAYS = 365
AUTO_CLEANUP = True
ANONYMIZE_OLD_DATA = True
```

## 🎨 UI Configuration

### Theme Settings

```json
{
    "theme": {
        "name": "default",
        "dark_mode": false,
        "primary_color": "#007bff",
        "secondary_color": "#6c757d",
        "font_family": "Arial, sans-serif",
        "font_size": "14px"
    },
    "layout": {
        "sidebar_collapsed": false,
        "show_breadcrumbs": true,
        "items_per_page": 25,
        "date_format": "MM/DD/YYYY",
        "time_format": "12h"
    }
}
```

### Customization

```css
/* custom.css */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}

.custom-header {
    background-color: var(--primary-color);
    color: white;
}
```

## 🤖 AI Configuration

### AI Engine Settings

```python
# ai_config.py
AI_CONFIG = {
    'analysis': {
        'enabled': True,
        'min_tasks_for_analysis': 5,
        'analysis_depth': 'detailed',
        'include_predictions': True
    },
    'chatbot': {
        'enabled': True,
        'response_style': 'professional',
        'max_response_length': 500,
        'context_memory': 10
    },
    'suggestions': {
        'enabled': True,
        'frequency': 'daily',
        'types': ['productivity', 'optimization', 'insights']
    }
}
```

### Performance Tuning

```python
# Performance settings
PERFORMANCE_CONFIG = {
    'cache_size': 100,
    'analysis_timeout': 30,
    'batch_size': 50,
    'async_processing': True,
    'memory_limit': '512MB'
}
```

## 📊 Analytics Configuration

### Metrics Collection

```json
{
    "analytics": {
        "enabled": true,
        "collection_interval": "1h",
        "metrics": [
            "task_completion_rate",
            "time_distribution",
            "productivity_score",
            "category_analysis"
        ],
        "retention_period": "1y",
        "anonymize_data": true
    }
}
```

### Reporting

```python
REPORTING_CONFIG = {
    'auto_reports': True,
    'report_frequency': 'weekly',
    'report_types': ['summary', 'detailed', 'trends'],
    'email_reports': False,
    'export_formats': ['pdf', 'csv', 'json']
}
```

## 🔄 Backup Configuration

### Automatic Backups

```python
BACKUP_CONFIG = {
    'enabled': True,
    'frequency': 'daily',
    'retention_count': 30,
    'compression': True,
    'encryption': True,
    'location': 'backups/',
    'include_logs': False
}
```

### Backup Schedule

```yaml
# backup-schedule.yml
schedules:
  - name: "daily"
    frequency: "0 2 * * *"  # 2 AM daily
    type: "incremental"
    retention: 7

  - name: "weekly"
    frequency: "0 3 * * 0"  # 3 AM Sunday
    type: "full"
    retention: 4

  - name: "monthly"
    frequency: "0 4 1 * *"  # 4 AM 1st of month
    type: "archive"
    retention: 12
```

## 🚀 Performance Configuration

### Optimization Settings

```python
PERFORMANCE_CONFIG = {
    # Database optimization
    'db_pool_size': 10,
    'db_timeout': 30,
    'query_cache_size': 100,

    # Web server optimization
    'worker_processes': 4,
    'max_requests': 1000,
    'request_timeout': 30,

    # Memory management
    'memory_limit': '1GB',
    'gc_threshold': 100,
    'cache_size': '256MB'
}
```

## 🔍 Logging Configuration

### Log Levels

```python
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        }
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler'
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'standard',
            'class': 'logging.FileHandler',
            'filename': 'logs/adhoclog.log'
        }
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

## 🔧 Advanced Configuration

### Custom Plugins

```python
# plugins/custom_plugin.py
class CustomPlugin:
    def __init__(self, config):
        self.config = config

    def process_task(self, task):
        # Custom task processing logic
        pass

    def generate_report(self, data):
        # Custom reporting logic
        pass
```

### Integration Settings

```yaml
# integrations.yml
integrations:
  slack:
    enabled: false
    webhook_url: ""
    channels: ["#productivity"]

  teams:
    enabled: false
    webhook_url: ""

  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
```

## 📝 Configuration Best Practices

### Security
- **Never commit** sensitive configuration to version control
- **Use environment variables** for secrets
- **Encrypt** configuration files containing sensitive data
- **Regularly rotate** encryption keys and passwords

### Performance
- **Monitor** resource usage and adjust limits accordingly
- **Use caching** for frequently accessed data
- **Optimize** database queries and indexes
- **Configure** appropriate timeout values

### Maintenance
- **Document** all configuration changes
- **Test** configuration changes in development first
- **Backup** configuration files before changes
- **Version control** configuration templates

## 🆘 Troubleshooting Configuration

### Common Issues

#### Configuration Not Loading
- Check file permissions
- Verify file syntax (JSON/YAML)
- Check environment variable names
- Review log files for errors

#### Performance Issues
- Increase memory limits
- Adjust cache sizes
- Optimize database settings
- Check network configuration

#### Security Warnings
- Update encryption settings
- Review permission settings
- Check HTTPS configuration
- Validate input sanitization

For additional help, contact your system administrator or review the installation guide for common setup issues.
