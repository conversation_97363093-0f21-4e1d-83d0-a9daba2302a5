# Basic Usage

Learn the fundamentals of using AdhocLog for effective task tracking and productivity management.

## 📋 Task Management Basics

### Creating Your First Task

1. **Navigate** to the main dashboard
2. **Click** "Add New Task" or use the "+" button
3. **Fill in** the required information:
   - **Task Name**: Clear, descriptive title
   - **Classification**: Category or type of work
   - **Start Time**: When you began (auto-filled)
   - **Notes**: Additional context or details

### Task Information Fields

| Field | Description | Required | Example |
|-------|-------------|----------|---------|
| Title | Brief description of the task | Yes | "Review quarterly budget" |
| Classification | Category or type of work | Yes | "Planning", "Execution", "Business Support Activities" |
| Description | Detailed description of actions taken | Yes | "Analyzed Q4 budget variance and prepared summary report" |
| Estimated Time | Expected duration in minutes | Yes | 60 (for 1 hour) |
| Date | Date when task was performed | No | Auto-filled with today's date |

### Working with Tasks

#### Creating a Task

1. **Navigate** to the "Add Task" page
2. **Fill in required fields**:
   - Title: Brief description of what you're doing
   - Classification: Select from predefined business categories
   - Description: Detailed explanation of actions taken
   - Estimated Time: Duration in minutes
3. **Submit** to save the task

#### Task Classifications

AdhocLog uses five predefined business classifications:

- **Planning**: Strategy, design, architecture, and planning activities
- **Offline Processing**: Data analysis, reporting, and document processing
- **Execution**: Implementation, development, and hands-on work
- **Business Support Activities**: Meetings, coordination, and administrative tasks
- **Operational Project Involvement**: Project management and monitoring activities

#### Editing Tasks

- **Click** on any task title to edit details
- **Update** any field as needed
- **Save** changes to preserve modifications

## 🏷️ Classifications and Categories

### Using Classifications Effectively

Classifications help organize and analyze your work patterns:

- **Be consistent** with naming conventions
- **Use broad categories** that apply to multiple tasks
- **Create meaningful groupings** for better analytics

### Time Estimation Guidelines

When entering estimated time in minutes, consider these guidelines:

- **Simple tasks**: 15-30 minutes (email responses, quick updates)
- **Standard tasks**: 30-90 minutes (analysis, documentation, meetings)
- **Complex tasks**: 90-240 minutes (development, detailed analysis)
- **Large projects**: Break into smaller tasks of 60-120 minutes each

### Best Practices

1. **Type** a new classification name when creating a task
2. **Use** consistent naming for similar tasks
3. **Review** existing classifications to avoid duplicates

## ⏰ Time Tracking

### Automatic Time Tracking

AdhocLog automatically tracks time for each task:

- **Start time** recorded when task is created
- **Duration** calculated when task is completed
- **Running time** shown for active tasks

### Manual Time Adjustments

If you need to adjust times:

1. **Edit** the task
2. **Modify** start or end times
3. **Save** changes to update duration

### Time Tracking Best Practices

- **Start tasks** when you actually begin working
- **End tasks** promptly when finished
- **Use notes** to explain any time gaps
- **Be honest** about actual time spent

## 📝 Notes and Documentation

### Effective Note-Taking

Use the notes field to capture:

- **Key decisions** made during the task
- **Important findings** or discoveries
- **Next steps** or follow-up actions
- **Resources used** or people consulted
- **Challenges encountered** and solutions

### Note-Taking Tips

- **Be specific** rather than vague
- **Use bullet points** for easy reading
- **Include relevant details** for future reference
- **Update notes** throughout the task

### Example Notes

```
✅ Completed Q4 budget analysis
📊 Key findings:
  - 15% variance in marketing spend
  - IT costs under budget by 8%
  - Travel expenses exceeded by 12%
🔄 Next steps:
  - Schedule meeting with marketing team
  - Review IT cost savings opportunities
  - Update travel policy recommendations
📎 Resources: Q4_Budget_Report.xlsx, expense_data.csv
```

## 🔍 Finding and Organizing Tasks

### Task List Views

The main task list provides several views:

- **All Tasks** - Complete list of all tasks
- **Recent** - Most recently created or updated
- **Active** - Currently in progress
- **Completed** - Finished tasks
- **By Classification** - Grouped by category

### Searching Tasks

Use the search functionality to find specific tasks:

- **Search by name** - Find tasks with specific keywords
- **Filter by classification** - Show only certain categories
- **Date range** - Find tasks from specific time periods
- **Status filter** - Active, completed, or all tasks

### Sorting Options

Sort tasks by:
- **Date** (newest or oldest first)
- **Duration** (longest or shortest first)
- **Classification** (alphabetical)
- **Status** (active, completed)

## 📊 Basic Analytics

### Task Overview

The dashboard provides quick insights:

- **Total tasks** created
- **Time spent** today/this week
- **Most common** classifications
- **Productivity trends**

### Time Distribution

View how your time is distributed across:
- **Classifications** - Which types of work take most time
- **Days of week** - When you're most productive
- **Time of day** - Peak productivity hours

### Completion Rates

Track your task completion patterns:
- **Tasks completed** vs. started
- **Average task duration** by classification
- **Productivity trends** over time

## 🔄 Daily Workflow

### Morning Setup

1. **Review** yesterday's completed tasks
2. **Plan** today's priorities
3. **Create** initial tasks for the day
4. **Check** any pending items

### During Work

1. **Start** each task when you begin
2. **Add notes** as you work
3. **Update** task details if scope changes
4. **Complete** tasks when finished

### End of Day Review

1. **Complete** any unfinished tasks
2. **Review** the day's accomplishments
3. **Add final notes** to tasks
4. **Plan** for tomorrow

## 💡 Tips for Success

### Getting Started
- **Start simple** - Don't overcomplicate initially
- **Be consistent** - Use AdhocLog daily for best results
- **Experiment** with classifications to find what works
- **Use notes** liberally to capture context

### Building Good Habits
- **Track everything** - Even small tasks add up
- **Be honest** about time spent
- **Review regularly** to identify patterns
- **Adjust** your approach based on insights

### Common Mistakes to Avoid
- **Don't** create too many classifications initially
- **Don't** forget to end tasks when finished
- **Don't** leave notes empty - future you will thank you
- **Don't** batch-enter tasks - real-time tracking is more accurate

## 🆘 Getting Help

### Quick Reference
- **F1** - Help documentation
- **Ctrl+N** - New task
- **Ctrl+S** - Save current task
- **Ctrl+E** - Export data

### Common Questions
- **How do I edit a task?** Click on the task name
- **How do I delete a task?** Use the delete button (⚠️ permanent)
- **How do I change classifications?** Edit the task and update the field
- **How do I export my data?** Use the Export button on the main page

### Need More Help?
- Use the AI chatbot for quick questions
- Review the [Task Management](task-management.md) guide for advanced features
- Check the [Analytics](analytics.md) section for productivity insights
- Contact your system administrator for technical issues
