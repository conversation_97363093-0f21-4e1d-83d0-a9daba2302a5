# Import and Export

AdhocLog provides comprehensive data import and export capabilities to help you integrate with other systems, backup your data, and perform external analysis.

## Export Features

### CSV Export

Export your task data in CSV format for analysis in spreadsheet applications or integration with other systems.

**Available Data**
- All task fields (ID, date, title, classification, description, estimated time)
- User information and timestamps
- Category mappings
- Archive status

**Export Process**
1. **Navigate** to the Analytics or Export section
2. **Select** date range (optional)
3. **Choose** export format (CSV)
4. **Click** Export button
5. **Download** the generated file

**File Format**
```csv
id,date,team_member,title,classification,category,description,est_time
1,2025-01-15,username,"Review budget","Planning","Adhoc","Analyzed Q4 budget variance",60
2,2025-01-15,username,"Team meeting","Business Support Activities","Business Support Activities","Weekly team sync",30
```

### Filtered Exports

**By Date Range**
- Export tasks from specific time periods
- Useful for monthly/quarterly reporting
- Supports custom date selection

**By Classification**
- Export only specific types of work
- Analyze particular work categories
- Generate focused reports

**By Status**
- Active tasks only
- Archived tasks only
- All tasks (default)

### Report Generation

**Summary Reports**
- Pre-formatted productivity summaries
- Key metrics and insights
- Professional presentation format

**Time Tracking Reports**
- Detailed time allocation by classification
- Project-specific breakdowns
- Compliance and billing documentation

**Analytics Reports**
- Productivity trends and patterns
- Efficiency metrics
- Performance indicators

## Import Capabilities

### CSV Import

Import task data from external systems or backup files.

**Supported Format**
The import file must follow the standard CSV format with these required columns:
- `title` (required)
- `classification` (required)
- `description` (required)
- `est_time` (required, in minutes)
- `date` (optional, defaults to today)

**Sample Import File**
```csv
title,classification,description,est_time,date
"Budget Analysis","Planning","Quarterly budget review and variance analysis",90,2025-01-15
"Team Meeting","Business Support Activities","Weekly team synchronization meeting",30,2025-01-15
"Report Generation","Offline Processing","Created monthly performance report",60,2025-01-15
```

**Import Process**
1. **Prepare** your CSV file with required columns
2. **Navigate** to the Import section
3. **Select** your CSV file
4. **Review** the preview of data to be imported
5. **Confirm** the import operation
6. **Verify** imported tasks in the task list

### Data Validation

**Required Field Checking**
- Ensures all required fields are present
- Validates data types and formats
- Reports any errors before import

**Classification Validation**
- Verifies classifications match system options
- Suggests corrections for invalid entries
- Maintains data consistency

**Time Format Validation**
- Ensures estimated time is in minutes
- Validates numeric format
- Provides error messages for invalid entries

## Backup and Restore

### Creating Backups

**Manual Backup**
1. **Export** all tasks using CSV export
2. **Save** the file with a descriptive name (e.g., "adhoclog_backup_2025-01-15.csv")
3. **Store** in a secure location

**Automated Backup**
- AdhocLog automatically maintains backup files
- Located in the data directory
- Includes both active and archived tasks

**Backup Best Practices**
- Create regular backups (weekly/monthly)
- Store backups in multiple locations
- Test restore procedures periodically
- Document backup procedures

### Restoring Data

**From CSV Backup**
1. **Prepare** the backup CSV file
2. **Clear** existing data if performing full restore
3. **Import** the backup file using standard import process
4. **Verify** data integrity after restore

**Partial Restore**
- Import specific date ranges
- Restore only certain classifications
- Merge with existing data

## Data Migration

### Moving Between Systems

**Exporting for Migration**
1. **Export** all data using comprehensive CSV export
2. **Include** archived tasks if needed
3. **Document** any custom configurations
4. **Verify** export completeness

**Importing to New System**
1. **Install** AdhocLog on new system
2. **Configure** classifications to match source system
3. **Import** data using CSV import
4. **Verify** data integrity and completeness

### System Integration

**External Analytics Tools**
- Export data for analysis in BI tools
- Integration with reporting systems
- Custom dashboard creation

**Project Management Systems**
- Export task data for project tracking
- Integration with enterprise PM tools
- Resource allocation analysis

**Time Tracking Systems**
- Export for payroll and billing
- Integration with HR systems
- Compliance reporting

## Data Formats

### CSV Specifications

**Character Encoding**: UTF-8
**Delimiter**: Comma (,)
**Text Qualifier**: Double quotes (")
**Date Format**: YYYY-MM-DD
**Time Format**: Minutes (integer)

**Special Characters**
- Commas in text fields must be quoted
- Line breaks in descriptions should be escaped
- Unicode characters are supported

### Field Definitions

**Required Fields**
- `title`: Task title (string, max 255 characters)
- `classification`: Must match system classifications
- `description`: Task description (string, unlimited)
- `est_time`: Estimated time in minutes (integer, 1-999)

**Optional Fields**
- `date`: Task date (YYYY-MM-DD format, defaults to today)
- `team_member`: User identifier (auto-detected if not provided)
- `category`: Auto-mapped from classification

## Troubleshooting

### Export Issues

**File Not Downloading**
- Check browser download settings
- Verify popup blockers are disabled
- Try different browser if needed

**Incomplete Data**
- Verify date range selection
- Check filter settings
- Ensure all required data exists

**Format Problems**
- Verify CSV format compatibility
- Check character encoding
- Test with small data sample

### Import Issues

**File Format Errors**
- Verify CSV format and structure
- Check required columns are present
- Validate data types and formats

**Data Validation Failures**
- Review error messages carefully
- Correct invalid classifications
- Fix missing required fields

**Duplicate Data**
- Check for existing tasks with same details
- Consider using date filters
- Review import preview carefully

### Performance Considerations

**Large Data Sets**
- Break large imports into smaller batches
- Allow extra time for processing
- Monitor system resources during import

**Network Issues**
- Ensure stable internet connection
- Retry failed operations
- Consider local backup options

## Best Practices

### Regular Exports

**Scheduled Backups**
- Export data weekly or monthly
- Automate where possible
- Store in secure, accessible location

**Version Control**
- Use descriptive file names with dates
- Maintain multiple backup versions
- Document any data changes

### Data Quality

**Clean Data**
- Review data before export
- Correct any inconsistencies
- Standardize classifications and descriptions

**Validation**
- Test import/export procedures regularly
- Verify data integrity after operations
- Maintain documentation of processes

### Security

**Data Protection**
- Encrypt sensitive backup files
- Use secure storage locations
- Follow organizational data policies

**Access Control**
- Limit export capabilities to authorized users
- Monitor data access and usage
- Maintain audit trails where required
