# Installation Guide

This guide will help you install AdhocLog on your system. Choose the installation method that best fits your environment.

## 📋 Prerequisites

Before installing Adhoc<PERSON>og, ensure you have:

- **Python 3.7 or higher** installed on your system
- **Internet connection** for downloading dependencies
- **Administrative privileges** (if installing system-wide)

### Checking Python Installation

=== "Windows"

    Open Command Prompt and run:
    ```cmd
    python --version
    ```
    or
    ```cmd
    py -3 --version
    ```

=== "macOS"

    Open Terminal and run:
    ```bash
    python3 --version
    ```

=== "Linux"

    Open Terminal and run:
    ```bash
    python3 --version
    ```

!!! tip "Python Installation"
    If Python is not installed, download it from [python.org](https://www.python.org/downloads/) and make sure to check "Add Python to PATH" during installation.

## 🚀 Installation Methods

### Method 1: Using the Launcher Scripts (Recommended)

The easiest way to install and run AdhocLog is using the provided launcher scripts.

=== "Windows"

    1. **Download** the AdhocLog files to your desired location
    2. **Navigate** to the AdhocLog folder
    3. **Double-click** `launch_app.bat` or `run.bat`
    4. **Follow** the on-screen prompts

    The launcher will automatically:
    - Detect your environment (SharePoint/OneDrive or local)
    - Create a virtual environment
    - Install required dependencies
    - Start the application

=== "macOS/Linux"

    1. **Download** the AdhocLog files to your desired location
    2. **Open Terminal** in the AdhocLog folder
    3. **Run** the launcher:
       ```bash
       ./launch_app.sh
       ```
    4. **Follow** the on-screen prompts

### Method 2: Manual Installation

For advanced users who prefer manual control:

1. **Clone or download** the AdhocLog repository
2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   ```
3. **Activate the virtual environment**:

   === "Windows"
       ```cmd
       venv\Scripts\activate
       ```

   === "macOS/Linux"
       ```bash
       source venv/bin/activate
       ```

4. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
5. **Start the application**:
   ```bash
   python scripts/run.py
   ```

## 🌐 SharePoint/OneDrive Installation

For corporate environments using SharePoint or OneDrive:

### Automatic Detection

The launcher scripts automatically detect SharePoint/OneDrive environments and configure:

- **User-specific virtual environments** in your home directory
- **Data isolation** to prevent user conflicts
- **Cache management** for optimal performance
- **Corporate network compatibility**

### Manual SharePoint Setup

If automatic detection fails:

1. **Set environment variables**:
   ```cmd
   set ADHOCLOG_SHAREPOINT_MODE=1
   set ADHOCLOG_USER_DATA_DIR=data\user_%USERNAME%
   ```

2. **Run the launcher** as normal

For SharePoint deployment, ensure the application files are accessible from the SharePoint environment and that users have appropriate permissions to execute the batch files.

## 🔧 Configuration

### Basic Configuration

AdhocLog works out of the box with default settings. For custom configuration:

1. **Copy** `config.py` to create your custom settings
2. **Modify** settings as needed
3. **Restart** the application

### Environment Variables

You can configure AdhocLog using environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_RUN_PORT` | Port for the web server | `8000` |
| `ADHOCLOG_SHAREPOINT_MODE` | Enable SharePoint mode | `0` |
| `ADHOCLOG_USER_DATA_DIR` | Custom data directory | `data` |
| `PYTHONDONTWRITEBYTECODE` | Disable Python cache | `0` |

## ✅ Verification

After installation, verify AdhocLog is working:

1. **Open your browser** to the displayed URL (usually `http://localhost:8000`)
2. **Create a test task** to ensure functionality
3. **Check the AI features** by using the chatbot or analysis tools

## 🔄 Updating

To update AdhocLog:

1. **Download** the latest version
2. **Replace** the old files (keep your `data` folder)
3. **Run** the launcher to update dependencies
4. **Restart** the application

## 🗑️ Uninstallation

To remove AdhocLog:

1. **Delete** the AdhocLog folder
2. **Remove** virtual environments (if created manually):
   - Windows: `%USERPROFILE%\.venvs\adhoc-log-app`
   - macOS/Linux: `~/.venvs/adhoc-log-app`
3. **Clean up** any custom data directories

## 🆘 Troubleshooting

### Common Issues

#### Python Not Found
- **Solution**: Install Python from [python.org](https://www.python.org/downloads/)
- **Windows**: Make sure "Add Python to PATH" was checked during installation

#### Permission Denied
- **Solution**: Run as administrator (Windows) or use `sudo` (macOS/Linux)
- **Alternative**: Install in user directory instead of system-wide

#### Network/Firewall Issues
- **Solution**: Configure corporate firewall to allow Python/pip
- **Alternative**: Use the offline installation method

#### Virtual Environment Issues
- **Solution**: Delete the `venv` folder and run the launcher again
- **Alternative**: Use system Python installation

For additional troubleshooting help, check the common issues section above or contact your system administrator.

## 📞 Getting Help

If you encounter issues:

1. **Review** the common issues section above
2. **Run** the diagnostic tools included with AdhocLog
3. **Contact** your system administrator
4. **Report** bugs on [GitHub Issues](https://github.com/jvbalcita/adhoc-log-app/issues)
