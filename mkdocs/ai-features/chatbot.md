# AI Chatbot - AdBot

AdBot is AdhocLog's intelligent conversational assistant that helps you manage tasks, analyze productivity, and get insights through natural language interactions.

## Getting Started

### Accessing AdBot

The chatbot is available throughout the AdhocLog interface:

1. **Look** for the chatbot icon (robot symbol) in the interface
2. **Click** to open the chat window
3. **Type** your question or request
4. **Press Enter** or click Send to interact

### First Interaction

When you first open AdBot, you'll see:

- **Welcome message** introducing <PERSON><PERSON><PERSON>'s capabilities
- **Conversation suggestions** to get you started
- **Input field** for typing your questions
- **Send button** to submit your messages

## Core Capabilities

### Task Management

**Creating Tasks**
- "Create a task for budget analysis"
- "Add a meeting task for 30 minutes"
- "I need to track time for report writing"

**Viewing Tasks**
- "Show me today's tasks"
- "What tasks do I have this week?"
- "List my planning tasks"

**Task Queries**
- "How many tasks did I complete yesterday?"
- "What's my longest task today?"
- "Show me tasks from last Monday"

### Time and Schedule Management

**Time Tracking**
- "How much time do I have scheduled today?"
- "What's my total time for this week?"
- "Show me my time breakdown by classification"

**Schedule Analysis**
- "When am I most productive?"
- "What's my average task duration?"
- "How much time do I spend in meetings?"

### Productivity Insights

**Analytics Queries**
- "Show me my productivity stats"
- "What's my completion rate this month?"
- "Which classification takes most of my time?"

**Pattern Recognition**
- "What are my common task patterns?"
- "When do I usually do planning tasks?"
- "What tasks often follow meetings?"

## Natural Language Understanding

### Supported Query Types

**Date References**
- "today", "yesterday", "tomorrow"
- "this week", "last week", "next week"
- "Monday", "last Friday", "next Tuesday"
- Specific dates: "January 15", "2025-01-15"

**Time Expressions**
- "morning", "afternoon", "evening"
- "this hour", "last 2 hours"
- Specific times: "9 AM", "2:30 PM"

**Classification References**
- "planning tasks", "execution work"
- "meetings", "business support"
- "offline processing", "operational tasks"

**Status and Filters**
- "completed tasks", "active tasks"
- "long tasks", "short tasks"
- "high priority", "urgent tasks"

### Intent Recognition

AdBot understands various ways to express the same request:

**Task Creation**
- "Create a task", "Add a new task", "I need to track"
- "Log time for", "Record work on", "Start a task"

**Information Requests**
- "Show me", "List", "What are", "How many"
- "Tell me about", "Display", "Find"

**Analysis Requests**
- "Analyze", "Break down", "Summary of"
- "Trends", "Patterns", "Statistics"

## Conversation Features

### Context Awareness

AdBot maintains conversation context:

**Follow-up Questions**
- User: "Show me today's tasks"
- AdBot: [Lists tasks]
- User: "How long will these take?"
- AdBot: [Calculates total time from previous context]

**Reference Resolution**
- "Show me more details about that task"
- "What about yesterday?"
- "Can you break that down further?"

### Smart Suggestions

**Proactive Recommendations**
- Task creation suggestions based on patterns
- Productivity tips based on your data
- Optimization recommendations

**Conversation Starters**
- Common queries you might want to ask
- Helpful commands for new users
- Quick access to frequent operations

## Advanced Features

### Multi-Task Operations

**Bulk Queries**
- "Show me all planning tasks from last week"
- "List every meeting longer than 30 minutes"
- "Find tasks with 'budget' in the title"

**Comparative Analysis**
- "Compare this week to last week"
- "How does my Monday compare to Friday?"
- "Show me trends over the last month"

### Intelligent Responses

**Data Visualization**
- Text-based charts and summaries
- Formatted tables for easy reading
- Highlighted key insights

**Actionable Suggestions**
- "Based on your patterns, consider..."
- "You might want to..."
- "Here's what I noticed..."

## Response Types

### Informational Responses

**Task Lists**
```
📋 Today's Tasks:
• Budget Analysis (Planning) - 60 min
• Team Meeting (Business Support) - 30 min
• Report Writing (Offline Processing) - 90 min

Total: 3 tasks, 180 minutes
```

**Analytics Summaries**
```
📊 This Week's Productivity:
• Tasks Completed: 15
• Total Time: 12.5 hours
• Top Classification: Execution (40%)
• Completion Rate: 94%
```

### Interactive Responses

**Follow-up Questions**
- "Would you like me to break this down by day?"
- "Should I show you the details for any specific task?"
- "Do you want to see trends over a longer period?"

**Action Suggestions**
- "I can help you create a similar task"
- "Would you like to set a reminder?"
- "Should I analyze this pattern further?"

## Best Practices

### Effective Communication

**Be Specific**
- "Show me planning tasks from last Tuesday" vs. "Show me tasks"
- "How much time did I spend on meetings this week?" vs. "Time spent?"

**Use Natural Language**
- "What did I work on yesterday?" (natural)
- "SELECT * FROM tasks WHERE date = yesterday" (too technical)

**Ask Follow-up Questions**
- Build on previous responses
- Drill down into interesting insights
- Explore patterns and trends

### Getting Better Results

**Provide Context**
- "I'm planning next week, show me typical Monday tasks"
- "For my performance review, what's my completion rate?"

**Use Conversational Flow**
- Start broad, then get specific
- Ask for clarification when needed
- Build on previous answers

## Privacy and Security

### Data Handling

**Local Processing**
- All conversations processed locally
- No external data transmission
- Complete privacy protection

**Conversation History**
- Stored locally for context
- Can be cleared if desired
- No cloud storage or backup

### Security Features

**Access Control**
- Same security as main application
- User-specific data access only
- No cross-user information sharing

## Troubleshooting

### Common Issues

**AdBot Not Responding**
- Check internet connection (for interface loading)
- Refresh the page
- Clear browser cache if needed

**Unclear Responses**
- Rephrase your question more specifically
- Provide additional context
- Break complex requests into simpler parts

**Missing Data in Responses**
- Verify you have tasks in the requested time period
- Check if filters are too restrictive
- Ensure task data is properly saved

### Getting Help

**Built-in Help**
- Type "help" for available commands
- Ask "What can you do?" for capabilities overview
- Use "examples" to see sample queries

**Conversation Tips**
- Start with simple questions
- Build complexity gradually
- Use the suggestion prompts when available

## Example Conversations

### Daily Planning
```
User: What do I have planned for today?
AdBot: You have 4 tasks scheduled:
• Morning standup (30 min)
• Code review (60 min) 
• Documentation update (45 min)
• Client call (30 min)
Total: 165 minutes

User: When am I usually most productive?
AdBot: Based on your patterns, you're most productive between 9-11 AM and 2-4 PM. Consider scheduling complex tasks during these times.
```

### Weekly Review
```
User: How was my productivity this week?
AdBot: Great week! You completed 18 of 20 tasks (90% completion rate). You spent 32 hours total, with most time on Execution tasks (45%). Your estimation accuracy improved to 85%.

User: What should I focus on next week?
AdBot: I notice you have fewer Planning tasks lately. Consider dedicating time early in the week for strategic work, as this typically improves your execution efficiency.
```
