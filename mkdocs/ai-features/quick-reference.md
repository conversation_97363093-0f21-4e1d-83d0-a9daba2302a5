# AI Features Quick Reference

This page provides a quick reference for all AI-powered features in AdhocLog, including commands, capabilities, and usage examples.

## Smart Classification

### Automatic Categorization

**How to Use**: Enter task title and description, AI suggests classification

**Classifications Available**:
- **Planning**: Strategy, design, architecture
- **Offline Processing**: Data analysis, reporting  
- **Execution**: Implementation, development
- **Business Support Activities**: Meetings, coordination
- **Operational Project Involvement**: Project management, monitoring

**Confidence Levels**:
- 🟢 **High (80-100%)**: Strong recommendation, likely accurate
- 🟡 **Medium (60-79%)**: Good suggestion, review recommended  
- 🔴 **Low (0-59%)**: Uncertain, manual classification needed

### Keywords by Classification

**Planning**
- strategy, design, architecture, plan, blueprint, roadmap
- "Design system architecture", "Strategic planning session"

**Offline Processing**  
- analysis, report, data, process, document, research
- "Generate monthly report", "Analyze sales data"

**Execution**
- implement, build, develop, create, execute, code
- "Develop new feature", "Implement solution"

**Business Support Activities**
- meeting, coordinate, admin, support, communicate, email
- "Team meeting", "Coordinate project resources"

**Operational Project Involvement**
- monitor, manage, oversee, track, project, status
- "Monitor system performance", "Project status review"

## Duration Prediction

### Time Estimation

**How to Use**: AI analyzes task content and suggests duration in minutes

**Estimation Categories**:
- **Quick (15-30 min)**: Simple updates, brief communications
- **Standard (30-90 min)**: Meetings, basic analysis, routine work  
- **Complex (90-240 min)**: Detailed analysis, development, planning

**Factors Considered**:
- Task complexity keywords
- Historical similar tasks
- Your estimation patterns
- Scope indicators

### Accuracy Improvement

**Learning Process**:
1. AI tracks your actual vs. estimated time
2. Identifies patterns in your work
3. Adjusts future predictions
4. Provides personalized estimates

## Priority Detection

### Urgency Assessment

**How to Use**: AI automatically detects priority from task content

**Priority Levels**:
- **High (0.8-1.0)**: Urgent action required
- **Medium (0.4-0.7)**: Standard importance
- **Low (0.0-0.3)**: When time permits

**High Priority Keywords**:
- urgent, asap, critical, emergency, immediate
- "due today", "by end of day", "system down"

**Medium Priority Keywords**:
- important, soon, needed, deadline
- "this week", "by Friday", "upcoming"

**Low Priority Keywords**:
- when possible, eventually, nice to have
- "next month", "future", "someday"

## Similar Task Matching

### Finding Related Tasks

**How to Use**: AI automatically finds similar tasks from your history

**Matching Criteria**:
- Semantic similarity (meaning, not just words)
- Classification alignment
- Context and description analysis
- Temporal patterns

**Benefits**:
- Learn from past experiences
- Reuse successful approaches  
- Improve time estimates
- Identify recurring patterns

**Example Matches**:
- Input: "Quarterly budget analysis"
- Matches: "Monthly budget review", "Annual financial analysis"

## AdBot Chatbot Commands

### Task Management

**Creating Tasks**:
- "Create a task for [description]"
- "Add a meeting task for 30 minutes"
- "I need to track time for [activity]"

**Viewing Tasks**:
- "Show me today's tasks"
- "What tasks do I have this week?"
- "List my planning tasks"
- "Find tasks with [keyword]"

**Task Queries**:
- "How many tasks did I complete yesterday?"
- "What's my longest task today?"
- "Show me tasks from last [day/week]"

### Time and Analytics

**Time Tracking**:
- "How much time do I have scheduled today?"
- "What's my total time for this week?"
- "Show me my time breakdown by classification"

**Productivity Insights**:
- "Show me my productivity stats"
- "What's my completion rate this month?"
- "Which classification takes most of my time?"

**Pattern Analysis**:
- "What are my common task patterns?"
- "When do I usually do planning tasks?"
- "What tasks often follow meetings?"

### Date and Time References

**Relative Dates**:
- today, yesterday, tomorrow
- this week, last week, next week
- this month, last month

**Specific Days**:
- Monday, Tuesday, etc.
- last Friday, next Tuesday
- January 15, 2025-01-15

**Time Periods**:
- morning, afternoon, evening
- this hour, last 2 hours
- 9 AM, 2:30 PM

## Pattern Recognition

### Workflow Analysis

**Task Sequences**: Common task combinations and follow-ups
**Time Patterns**: Peak productivity hours and optimal scheduling
**Efficiency Insights**: Tasks that consistently over/under-run

### Predictive Suggestions

**Next Task Predictions**: Likely follow-up tasks based on current work
**Optimization Recommendations**: Better scheduling and process improvements
**Productivity Tips**: Personalized suggestions based on your patterns

## Quick Commands

### Essential AdBot Commands

| Command | Purpose | Example |
|---------|---------|---------|
| `help` | Show available commands | "help" |
| `today` | Show today's tasks | "What's on my schedule today?" |
| `stats` | Show productivity statistics | "Show me my stats" |
| `time` | Show time analysis | "How much time this week?" |
| `create` | Create new task | "Create a task for budget review" |

### Useful Phrases

**Getting Information**:
- "Show me...", "List...", "What are..."
- "How many...", "How much...", "When..."
- "Tell me about...", "Find...", "Display..."

**Creating Tasks**:
- "Create a task for..."
- "Add a new task..."
- "I need to track..."
- "Log time for..."

**Analysis Requests**:
- "Analyze my...", "Break down..."
- "What patterns...", "Show trends..."
- "Compare...", "Summary of..."

## Best Practices

### Maximizing AI Benefits

**Task Descriptions**:
- Use specific, descriptive language
- Include key requirements and constraints
- Mention complexity and scope

**Consistent Usage**:
- Use AI suggestions as starting points
- Maintain consistency in classifications
- Track accuracy and adjust as needed

**Regular Interaction**:
- Use AdBot for daily task queries
- Ask for productivity insights regularly
- Explore patterns and trends

### Common Mistakes to Avoid

**Vague Descriptions**:
- ❌ "Work on stuff"
- ✅ "Analyze Q4 budget variance report"

**Inconsistent Classifications**:
- ❌ Mixing personal preferences with AI suggestions
- ✅ Following consistent classification logic

**Ignoring AI Feedback**:
- ❌ Not tracking actual vs. estimated time
- ✅ Using accuracy feedback to improve estimates

## Troubleshooting

### AI Not Working as Expected

**Improve Input Quality**:
- Provide more detailed descriptions
- Use specific, clear language
- Include context about complexity

**Check Data Quality**:
- Ensure sufficient task history
- Verify classification consistency
- Update incorrect historical data

### AdBot Issues

**Not Responding**:
- Refresh the page
- Check browser compatibility
- Clear cache if needed

**Unclear Responses**:
- Rephrase questions more specifically
- Provide additional context
- Break complex requests into parts

### Performance Issues

**Slow AI Analysis**:
- Large task history may slow processing
- Consider archiving old tasks
- Monitor system resources

**Memory Usage**:
- Close unnecessary applications
- Ensure adequate system memory
- Consider system upgrades

## Privacy and Security

### Data Protection

**Local Processing**: All AI analysis performed locally
**No External Calls**: No cloud processing of sensitive data
**Complete Privacy**: Data stays within your environment
**Offline Capability**: All features work without internet

### Security Features

**Access Control**: Same security as main application
**User Isolation**: Only your data is accessible
**No Data Sharing**: No cross-user information access
