<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All Forms</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Comprehensive Form Test</h1>
    
    <div class="log">
        <h3>Test Log:</h3>
        <div id="test-log"></div>
    </div>

    <div class="test-section">
        <h2>Test 1: Add Task Form</h2>
        <form action="http://127.0.0.1:5000/tasks/add" method="POST">
            <div>
                <label>Title: <input type="text" name="title" value="Test Add Task" required></label>
            </div>
            <div>
                <label>Classification: <input type="text" name="classification" value="Planning" required></label>
            </div>
            <div>
                <label>Description: <input type="text" name="description" value="Testing add functionality"></label>
            </div>
            <div>
                <label>Est Time: <input type="number" name="est_time" value="30"></label>
            </div>
            <div>
                <label>Date: <input type="date" name="date" value="2025-08-01"></label>
            </div>
            <button type="submit">Add Task</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Test 2: Edit Task Form (Task ID 1)</h2>
        <form action="http://127.0.0.1:5000/tasks/edit/1" method="POST">
            <div>
                <label>Title: <input type="text" name="title" value="Updated Test Task" required></label>
            </div>
            <div>
                <label>Classification: <input type="text" name="classification" value="Planning" required></label>
            </div>
            <div>
                <label>Description: <input type="text" name="description" value="Testing edit functionality"></label>
            </div>
            <div>
                <label>Est Time: <input type="number" name="est_time" value="45"></label>
            </div>
            <div>
                <label>Date: <input type="date" name="date" value="2025-08-01"></label>
            </div>
            <button type="submit">Update Task</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Test 3: Archive Task Form (Task ID 1)</h2>
        <form action="http://127.0.0.1:5000/tasks/delete/1" method="POST">
            <button type="submit">Archive Task</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Instructions:</h2>
        <ol>
            <li><strong>Test Add Task:</strong> Click "Add Task" - should redirect to tasks page with new task</li>
            <li><strong>Test Edit Task:</strong> Click "Update Task" - should redirect to tasks page with updated task</li>
            <li><strong>Test Archive Task:</strong> Click "Archive Task" - should redirect to tasks page with task archived</li>
        </ol>
        <p><strong>Expected Behavior:</strong></p>
        <ul>
            <li>All forms should submit successfully</li>
            <li>Page should redirect after submission</li>
            <li>No buttons should get stuck in "processing" state</li>
            <li>No timeout errors in console</li>
        </ul>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += '<div>' + timestamp + ': ' + message + '</div>';
            console.log(timestamp + ': ' + message);
        }

        // Monitor all form submissions
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page loaded');
            
            const forms = document.querySelectorAll('form');
            forms.forEach((form, index) => {
                form.addEventListener('submit', function(event) {
                    log(`Form ${index + 1} submitted - action: ${form.action}`);
                });
            });
        });

        // Monitor page unload (when redirecting)
        window.addEventListener('beforeunload', function() {
            log('Page is redirecting - this is expected behavior');
        });
    </script>
</body>
</html>
