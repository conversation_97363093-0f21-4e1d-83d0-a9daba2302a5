"""
Documentation builder utility for AdhocLog
Builds MkDocs documentation and integrates it with the Flask application
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def get_project_root():
    """Get the project root directory"""
    # Go up from app/utils/ to project root
    return Path(__file__).parent.parent.parent

def check_mkdocs_available():
    """Check if MkDocs is available in the current environment"""
    try:
        import mkdocs
        return True
    except ImportError:
        return False

def build_documentation():
    """Build the MkDocs documentation"""
    project_root = get_project_root()
    mkdocs_config = project_root / 'mkdocs.yml'
    
    if not mkdocs_config.exists():
        logger.error("MkDocs configuration file not found")
        return False
    
    if not check_mkdocs_available():
        logger.error("MkDocs not available in current environment")
        return False
    
    logger.info("Building MkDocs documentation...")
    
    try:
        # Change to project root directory
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        # Build the documentation using mkdocs module
        from mkdocs.commands.build import build
        from mkdocs.config import load_config
        
        # Load MkDocs configuration
        config = load_config(config_file='mkdocs.yml')
        
        # Build the documentation
        build(config)
        
        logger.info("Documentation built successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error building documentation: {e}")
        return False
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def build_documentation_subprocess():
    """Build documentation using subprocess (fallback method)"""
    project_root = get_project_root()
    mkdocs_config = project_root / 'mkdocs.yml'
    
    if not mkdocs_config.exists():
        logger.error("MkDocs configuration file not found")
        return False
    
    logger.info("Building MkDocs documentation using subprocess...")
    
    try:
        # Change to project root directory
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        # Try to build using mkdocs command
        result = subprocess.run(
            [sys.executable, '-m', 'mkdocs', 'build'],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            logger.info("Documentation built successfully using subprocess")
            return True
        else:
            logger.error(f"MkDocs build failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("Documentation build timed out")
        return False
    except FileNotFoundError:
        logger.error("MkDocs command not found")
        return False
    except Exception as e:
        logger.error(f"Error building documentation with subprocess: {e}")
        return False
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def ensure_documentation_built():
    """Ensure documentation is built, build if necessary"""
    project_root = get_project_root()
    docs_site_dir = project_root / 'docs_site'
    
    # Check if documentation already exists
    if docs_site_dir.exists() and (docs_site_dir / 'index.html').exists():
        logger.info("Documentation already exists")
        return True
    
    logger.info("Documentation not found, building...")
    
    # Try primary build method first
    if build_documentation():
        return True
    
    # Fallback to subprocess method
    logger.info("Primary build method failed, trying subprocess...")
    return build_documentation_subprocess()

def get_documentation_status():
    """Get the status of the documentation"""
    project_root = get_project_root()
    docs_site_dir = project_root / 'docs_site'
    mkdocs_config = project_root / 'mkdocs.yml'
    
    status = {
        'config_exists': mkdocs_config.exists(),
        'docs_built': docs_site_dir.exists() and (docs_site_dir / 'index.html').exists(),
        'mkdocs_available': check_mkdocs_available(),
        'docs_directory': str(docs_site_dir),
        'config_file': str(mkdocs_config)
    }
    
    return status

def initialize_documentation():
    """Initialize documentation system during application startup"""
    logger.info("Initializing documentation system...")
    
    status = get_documentation_status()
    
    if not status['config_exists']:
        logger.warning("MkDocs configuration not found, skipping documentation build")
        return False
    
    if not status['mkdocs_available']:
        logger.warning("MkDocs not available, skipping documentation build")
        return False
    
    if status['docs_built']:
        logger.info("Documentation already built")
        return True
    
    logger.info("Building documentation for first time...")
    return ensure_documentation_built()
