{% extends "base.html" %}

{% block title %}Dashboard - AdhocLog{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4">
            <div>
                <h1 class="mb-2 d-flex align-items-center">
                    <i class="bi bi-speedometer2 me-3 text-primary"></i> Dashboard
                </h1>
                <p class="text-muted mb-0">Welcome back, {{ current_user }}! Here's your productivity overview.</p>
            </div>
            <div class="mt-3 mt-md-0">
                <a href="{{ url_for('add_task') }}" class="btn btn-primary btn-lg shadow-sm">
                    <i class="bi bi-plus-lg me-2"></i> Add Task
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Stats Cards -->
<div class="row mb-5">
    <div class="col-12 col-sm-6 col-lg-3 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body text-center position-relative d-flex flex-column justify-content-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="stats-icon me-3">
                        <i class="bi bi-check-circle-fill fs-1 opacity-75"></i>
                    </div>
                    <div class="text-start">
                        <h2 class="card-title mb-0 display-5 fw-bold">{{ total_tasks_today }}</h2>
                        <p class="card-text mb-0 opacity-90 small">Tasks Today</p>
                    </div>
                </div>
                <div class="mt-auto">
                    <small class="opacity-75">
                        <i class="bi bi-calendar-day me-1"></i>
                        {{ "today" | title }}
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
            <div class="card-body text-center position-relative d-flex flex-column justify-content-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="stats-icon me-3">
                        <i class="bi bi-clock-fill fs-1 opacity-75"></i>
                    </div>
                    <div class="text-start">
                        <h2 class="card-title mb-0 display-5 fw-bold">{{ total_time_today }}</h2>
                        <p class="card-text mb-0 opacity-90 small">Minutes Today</p>
                    </div>
                </div>
                <div class="mt-auto">
                    <small class="opacity-75">
                        <i class="bi bi-stopwatch me-1"></i>
                        Productive Time
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
            <div class="card-body text-center position-relative d-flex flex-column justify-content-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="stats-icon me-3">
                        <i class="bi bi-calendar-month fs-1 opacity-75"></i>
                    </div>
                    <div class="text-start">
                        <h2 class="card-title mb-0 display-5 fw-bold">{{ total_tasks_month }}</h2>
                        <p class="card-text mb-0 opacity-90 small">Tasks This Month</p>
                    </div>
                </div>
                <div class="mt-auto">
                    <small class="opacity-75">
                        <i class="bi bi-calendar3 me-1"></i>
                        Monthly Progress
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 col-sm-6 col-lg-3 mb-4">
        <div class="card stats-card h-100" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">
            <div class="card-body text-center position-relative d-flex flex-column justify-content-center">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="stats-icon me-3">
                        <i class="bi bi-stopwatch fs-1 opacity-75"></i>
                    </div>
                    <div class="text-start">
                        <h2 class="card-title mb-0 display-5 fw-bold">{{ total_time_month }}</h2>
                        <p class="card-text mb-0 opacity-90 small">Minutes This Month</p>
                    </div>
                </div>
                <div class="mt-auto">
                    <small class="opacity-75">
                        <i class="bi bi-graph-up me-1"></i>
                        Total Effort
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Tasks Section -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                    <i class="bi bi-calendar-day text-primary me-2"></i> Today's Tasks
                    {% if today_tasks %}
                        <span class="badge bg-primary ms-2">{{ today_tasks|length }}</span>
                    {% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <a href="{{ url_for('add_task') }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus me-1"></i> Add Task
                    </a>
                    <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-list me-1"></i> View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if today_tasks %}
                    <div class="row g-4">
                        {% for task in today_tasks %}
                        <div class="col-12 col-md-6 col-xl-4">
                            <div class="card task-card h-100 border-0 shadow-sm">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0 flex-grow-1 me-2">{{ task.title }}</h6>
                                        <div class="d-flex gap-1">
                                            <span class="badge bg-secondary bg-opacity-75 small">{{ task.classification }}</span>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <span class="badge bg-info bg-opacity-75 small">{{ task.category }}</span>
                                    </div>
                                    <p class="card-text flex-grow-1 text-muted small">
                                        {{ task.description[:120] }}{% if task.description|length > 120 %}...{% endif %}
                                    </p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted d-flex align-items-center">
                                                <i class="bi bi-clock me-1"></i> {{ task.est_time }} min
                                            </small>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ url_for('edit_task', task_id=task.id) }}"
                                                   class="btn btn-outline-primary btn-sm"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="Edit Task">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="bi bi-calendar-x display-1 text-muted opacity-50"></i>
                        </div>
                        <h4 class="text-muted mb-3">No tasks for today yet</h4>
                        <p class="text-muted mb-4">Start your productive day by adding your first task!</p>
                        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-center">
                            <a href="{{ url_for('add_task') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-plus-lg me-2"></i> Add Your First Task
                            </a>
                            <button class="btn btn-outline-secondary btn-lg" onclick="AIChatbotUtils.openWithTaskCreation(); return false;">
                                <i class="bi bi-robot me-2"></i> Ask AI to Help
                            </button>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks Section -->
{% if recent_tasks %}
<div class="row mb-5">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <h5 class="mb-2 mb-md-0 d-flex align-items-center">
                    <i class="bi bi-clock-history text-primary me-2"></i> Recent Tasks
                    <small class="text-muted ms-2">(Last 7 Days)</small>
                </h5>
                <a href="{{ url_for('task_list') }}" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-arrow-right me-1"></i> View All Tasks
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-0 ps-4">Date</th>
                                <th class="border-0">Title</th>
                                <th class="border-0 d-none d-md-table-cell">Classification</th>
                                <th class="border-0 text-end">Time</th>
                                <th class="border-0 text-end pe-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in recent_tasks %}
                            <tr class="border-bottom">
                                <td class="ps-4 align-middle">
                                    <small class="text-muted fw-medium">{{ task.date }}</small>
                                </td>
                                <td class="align-middle">
                                    <div class="fw-medium">{{ task.title }}</div>
                                    <div class="d-md-none">
                                        <small class="text-muted">
                                            <span class="badge bg-secondary bg-opacity-75 small">{{ task.classification }}</span>
                                        </small>
                                    </div>
                                </td>
                                <td class="align-middle d-none d-md-table-cell">
                                    <span class="badge bg-secondary bg-opacity-75">{{ task.classification }}</span>
                                </td>
                                <td class="align-middle text-end">
                                    <span class="fw-medium">{{ task.est_time }}</span>
                                    <small class="text-muted ms-1">min</small>
                                </td>
                                <td class="align-middle text-end pe-4">
                                    <a href="{{ url_for('edit_task', task_id=task.id) }}"
                                       class="btn btn-sm btn-outline-primary"
                                       data-bs-toggle="tooltip"
                                       data-bs-placement="top"
                                       title="Edit Task">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
