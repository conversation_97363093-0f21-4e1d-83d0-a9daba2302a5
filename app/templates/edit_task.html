{% extends "base.html" %}

{% block title %}Edit Task - AdhocLog{% endblock %}

{% block content %}
<!-- Include our custom CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/task_form.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-features.css') }}">

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil"></i> Edit Task</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-form"></i> Task Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row g-3">
                        <!-- Title -->
                        <div class="col-12">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ task.get('title', '') }}" required
                                   placeholder="Enter task title...">
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="col-md-6">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="{{ task.get('date', '') }}">
                        </div>

                        <!-- Estimated Time -->
                        <div class="col-md-6">
                            <label for="est_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="est_time" name="est_time"
                                   value="{{ task.get('est_time', 30) }}" min="1" max="999"
                                   placeholder="30">
                        </div>

                        <!-- Classification -->
                        <div class="col-md-6">
                            <label for="classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if task.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>

                        <!-- Category (Auto-filled) -->
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="{{ task.get('category', '') }}" readonly>
                            <div class="form-text">Auto-filled based on classification</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Description / Actions Taken</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Describe what you did or plan to do...">{{ task.get('description', '') }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="form-actions d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Update Task
                                </button>
                                <a href="{{ url_for('task_list') }}" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Classification Guide -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Classification Guide</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Classification</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Planning</strong></td>
                                <td>
                                    <small>
                                        • Team meetings<br>
                                        • Brainstorming sessions<br>
                                        • Coordination with other teams
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Offline Processing</strong></td>
                                <td>
                                    <small>
                                        • Research and investigation<br>
                                        • Creating Excel reports<br>
                                        • Drafting documentation<br>
                                        • Preparing presentations<br>
                                        • Scripting/Coding<br>
                                        • Training/Certification
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Execution</strong></td>
                                <td>
                                    <small>
                                        • Presenting to managers<br>
                                        • Health checks<br>
                                        • Client System/Technology Operations<br>
                                        • Attendance/PTO Reminder<br>
                                        • Client Engineering Daily Case Management
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Business Support Activities</strong></td>
                                <td>
                                    <small>
                                        • Attend Townhall and other activities<br>
                                        • 1-on-1 Meeting/Catch-Up<br>
                                        • Daily Standup<br>
                                        • Technical Ramblings<br>
                                        • Client Engineering Team Weekly Meeting<br>
                                        • Audience of a Presentation
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Operational Project Involvement</strong></td>
                                <td>
                                    <small>
                                        • Assisting colleagues with difficult issues<br>
                                        • Participating in high-priority issue/SRT<br>
                                        • Operation Request from US team<br>
                                        • Involvement on US Projects<br>
                                        • Change Management
                                    </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Refactored Task Form JavaScript Modules -->
<!-- Core module (must be loaded first) -->
<script src="{{ url_for('static', filename='js/task_form_core.js') }}"></script>
<!-- Feature modules -->
<script src="{{ url_for('static', filename='js/task_template_manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_keyboard_shortcuts.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_smart_suggestions.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_ai_enhancements.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_analytics_tracker.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_form_validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_ui_notifications.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_bulk_operations.js') }}"></script>

<script>
// Initialize the refactored task form system when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Edit Task DOM Content Loaded - Initializing refactored task form system');

    try {
        // Initialize core module
        if (window.TaskFormCore) {
            window.taskFormCore = new TaskFormCore();
            console.log('TaskFormCore initialized');

            // Initialize all feature modules
            const templateManager = new TaskTemplateManager();
            const keyboardShortcuts = new TaskKeyboardShortcuts();
            const smartSuggestions = new TaskSmartSuggestions();
            const aiEnhancements = new TaskAIEnhancements();
            const analyticsTracker = new TaskAnalyticsTracker();
            const formValidation = new TaskFormValidation();
            const uiNotifications = new TaskUINotifications();
            const bulkOperations = new TaskBulkOperations();

            // Inject dependencies into core module
            window.taskFormCore.injectTemplateManager(templateManager);
            window.taskFormCore.injectKeyboardShortcuts(keyboardShortcuts);
            window.taskFormCore.injectSmartSuggestions(smartSuggestions);
            window.taskFormCore.injectAIEnhancements(aiEnhancements);
            window.taskFormCore.injectAnalyticsTracker(analyticsTracker);
            window.taskFormCore.injectFormValidation(formValidation);
            window.taskFormCore.injectUINotifications(uiNotifications);
            window.taskFormCore.injectBulkOperations(bulkOperations);

            console.log('All task form modules initialized and injected successfully');

            // Create global aliases for backward compatibility
            window.taskFormManager = window.taskFormCore;
            window.taskForm = window.taskFormCore;

        } else {
            console.error('TaskFormCore not found - check if task_form_core.js loaded correctly');
        }
    } catch (error) {
        console.error('Error initializing refactored task form system:', error);
    }
});
</script>
{% endblock %}
