<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AdhocLog{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/favicon-96x96.png') }}" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}" />
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/apple-touch-icon.png') }}" />
    <meta name="apple-mobile-web-app-title" content="AdhocLog" />
    <link rel="manifest" href="{{ url_for('static', filename='images/site.webmanifest') }}" />

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Navigation & Responsive Styles -->
    <link href="{{ url_for('static', filename='css/navigation_responsive.css') }}" rel="stylesheet">
    <!-- AI Chatbot Styles -->
    <link href="{{ url_for('static', filename='css/ai_chatbot.css') }}" rel="stylesheet">

    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --primary-gradient-hover: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --nav-shadow: 0 4px 20px -2px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --z-navbar: 1030;
        }

        body {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            /* No padding-top needed for sticky navbar */
        }

        /* Enhanced Navigation Styles */
        .navbar {
            background: var(--primary-gradient) !important;
            box-shadow: var(--nav-shadow);
            border: none;
            backdrop-filter: blur(10px);
            transition: var(--transition);
            z-index: var(--z-navbar);
            min-height: 76px;
            top: 0; /* Ensure it sticks to the very top */
        }

        .navbar.scrolled {
            box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(15px);
        }

        /* Brand Styling */
        .navbar-brand {
            font-weight: 700;
            font-size: 1.35rem;
            letter-spacing: -0.025em;
            transition: var(--transition);
            padding: 0.75rem 0;
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
        }

        .brand-logo {
            transition: var(--transition);
            filter: brightness(1.1);
        }

        .navbar-brand:hover .brand-logo {
            transform: rotate(5deg) scale(1.05);
        }

        .brand-text {
            background: linear-gradient(45deg, #fff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Navigation Links */
        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 2px;
            padding: 0.75rem 1rem !important;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
            color: rgba(255, 255, 255, 0.95) !important;
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            font-weight: 600;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: linear-gradient(90deg, #fbbf24, #f59e0b);
            border-radius: 2px;
        }

        /* Navigation Icons */
        .nav-icon {
            font-size: 1.1rem;
            margin-right: 0.5rem;
            transition: var(--transition);
        }

        /* Icon-only navigation links (like Help) */
        .nav-link .nav-icon:only-child {
            margin-right: 0;
        }

        .nav-link:hover .nav-icon {
            transform: scale(1.1);
        }

        /* Mobile Toggler */
        .navbar-toggler {
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 0.5rem;
            transition: var(--transition);
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
        }

        .navbar-toggler:hover {
            border-color: rgba(255, 255, 255, 0.4);
            background-color: rgba(255, 255, 255, 0.1);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='m4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        /* User Avatar */
        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }

        .user-avatar i {
            font-size: 1.2rem;
        }

        /* Enhanced Dropdowns */
        .dropdown-menu {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 0.75rem 0;
            min-width: 200px;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .dropdown-header {
            font-weight: 600;
            color: #374151;
            padding: 0.5rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 0.5rem;
        }

        .dropdown-item {
            font-weight: 500;
            padding: 0.75rem 1rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            transform: translateX(4px);
            color: #1f2937;
        }

        .dropdown-item i {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 991.98px) {
            .navbar {
                position: relative;
            }

            .navbar-collapse {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border-radius: var(--border-radius);
                margin-top: 1rem;
                padding: 1rem;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .nav-link {
                padding: 0.75rem 1rem !important;
                margin: 0.25rem 0;
                border-radius: var(--border-radius);
            }

            .nav-text {
                margin-left: 0.5rem;
            }

            .navbar-nav .dropdown-menu {
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.2);
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .navbar-brand {
                font-size: 1.2rem;
            }

            .brand-logo {
                width: 24px;
                height: 24px;
            }

            .nav-icon {
                font-size: 1rem;
            }

            .dropdown-menu {
                min-width: 180px;
            }

            .navbar-collapse {
                padding: 0.75rem;
            }
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            background: white;
        }

        .card:hover {
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 1px solid #e2e8f0;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
            font-weight: 600;
            color: #334155;
        }

        .task-card {
            transition: var(--transition);
            border-left: 4px solid #3b82f6;
        }

        .task-card:hover {
            transform: translateY(-3px);
            border-left-color: #1d4ed8;
        }

        .stats-card {
            background: var(--primary-gradient);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            padding: 0.5rem 1rem;
        }

        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.5);
        }

        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 4px 14px 0 rgba(79, 172, 254, 0.39);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(79, 172, 254, 0.5);
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: var(--transition);
            padding: 0.75rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .badge {
            border-radius: 6px;
            font-weight: 500;
            padding: 0.35em 0.65em;
        }

        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .table th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e2e8f0;
            font-weight: 600;
            color: #475569;
        }

        .footer {
            margin-top: auto;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-top: 1px solid #e2e8f0;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--card-shadow);
        }

        .list-group-item {
            border-radius: 8px !important;
            border: 1px solid #e2e8f0;
            margin-bottom: 4px;
            transition: var(--transition);
        }

        .list-group-item:hover {
            background-color: #f8fafc;
            transform: translateX(4px);
        }

        .spinner-border {
            color: #3b82f6;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #1e293b;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .text-muted {
            color: #64748b !important;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Enhanced Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid px-3">
            <!-- Brand with enhanced styling -->
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('dashboard') }}">
                <img src="{{ url_for('static', filename='images/favicon.svg') }}"
                     alt="AdhocLog" width="28" height="28" class="me-2 brand-logo">
                <span class="brand-text">AdhocLog</span>
            </a>

            <!-- Mobile menu button with enhanced styling -->
            <button class="navbar-toggler border-0 p-2" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false"
                    aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main navigation items -->
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}"
                           href="{{ url_for('dashboard') }}">
                            <i class="bi bi-house-door nav-icon"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'task_list' %}active{% endif %}"
                           href="{{ url_for('task_list') }}">
                            <i class="bi bi-list-task nav-icon"></i>
                            <span class="nav-text">Tasks</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'statistics' %}active{% endif %}"
                           href="{{ url_for('statistics') }}">
                            <i class="bi bi-graph-up nav-icon"></i>
                            <span class="nav-text">Statistics</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'archive_list' %}active{% endif %}"
                           href="{{ url_for('archive_list') }}">
                            <i class="bi bi-archive nav-icon"></i>
                            <span class="nav-text">Archive</span>
                        </a>
                    </li>
                    <li class="nav-item d-lg-none">
                        <a class="nav-link {% if request.endpoint == 'docs.docs_index' or request.endpoint == 'docs.serve_docs' %}active{% endif %}"
                           href="{{ url_for('docs.docs_index') }}" target="_blank">
                            <i class="bi bi-question-circle nav-icon"></i>
                            <span class="nav-text">Help</span>
                        </a>
                    </li>
                </ul>

                <!-- Right side navigation -->
                <ul class="navbar-nav">
                    <!-- Help link for desktop -->
                    <li class="nav-item d-none d-lg-block">
                        <a class="nav-link {% if request.endpoint == 'docs.docs_index' or request.endpoint == 'docs.serve_docs' %}active{% endif %}"
                           href="{{ url_for('docs.docs_index') }}" target="_blank"
                           title="Help & Documentation">
                            <i class="bi bi-question-circle nav-icon"></i>
                        </a>
                    </li>

                    <!-- AI Assistant Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-robot nav-icon"></i>
                            <span class="nav-text d-lg-none">AdBot</span>
                            <span class="d-none d-lg-inline ms-1">AdBot</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg">
                            <li>
                                <h6 class="dropdown-header d-flex align-items-center">
                                    <i class="bi bi-robot me-2 text-primary"></i>AI Assistant
                                </h6>
                            </li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithTaskCreation(); return false;">
                                <i class="bi bi-plus-circle me-2 text-success"></i>Create Tasks
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithScheduleRequest(); return false;">
                                <i class="bi bi-calendar me-2 text-info"></i>Show Schedule
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.openWithSuggestions(); return false;">
                                <i class="bi bi-lightbulb me-2 text-warning"></i>Get Suggestions
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="AIChatbotUtils.resetChat(); return false;">
                                <i class="bi bi-arrow-clockwise me-2 text-muted"></i>Reset Chat
                            </a></li>
                        </ul>
                    </li>

                    <!-- User Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar me-2">
                                <i class="bi bi-person-circle"></i>
                            </div>
                            <span class="nav-text d-lg-none">{{ current_user or 'User' }}</span>
                            <span class="d-none d-lg-inline">{{ current_user or 'User' }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-lg">
                            <li>
                                <h6 class="dropdown-header d-flex align-items-center">
                                    <i class="bi bi-person me-2 text-primary"></i>{{ current_user or 'User' }}
                                </h6>
                            </li>
                            <li><a class="dropdown-item" href="{{ url_for('export_csv') }}">
                                <i class="bi bi-download me-2 text-primary"></i>Export Data
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-muted" href="#" onclick="return false;">
                                <i class="bi bi-info-circle me-2"></i>
                                <small>Version 1.0</small>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container-fluid px-3 mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show shadow-sm" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-{{ 'exclamation-triangle-fill' if category == 'error' else 'check-circle-fill' }} me-2 fs-5"></i>
                                    <div class="flex-grow-1">{{ message }}</div>
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container-fluid px-3 my-4 flex-grow-1">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-11 col-xl-10">
                {% block content %}{% endblock %}
            </div>
        </div>
    </main>

    <!-- Enhanced Footer -->
    <footer class="footer py-4 mt-5">
        <div class="container-fluid px-3">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <span class="text-muted d-flex align-items-center justify-content-center justify-content-md-start">
                        <img src="{{ url_for('static', filename='images/favicon.svg') }}"
                             alt="AdhocLog" width="16" height="16" class="me-2 opacity-75">
                        <strong>AdhocLog</strong> &copy; 2025 | Client Systems Engineering
                    </span>
                </div>
                <div class="col-md-6 text-center text-md-end mt-2 mt-md-0">
                    <div class="d-flex justify-content-center justify-content-md-end align-items-center">
                        <small class="text-muted me-3">
                            <i class="bi bi-shield-check me-1"></i>
                            Privacy-First AI
                        </small>
                        <small class="text-muted">
                            <i class="bi bi-speedometer me-1"></i>
                            v1.0
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Enhanced Navigation & Responsive JavaScript -->
    <script src="{{ url_for('static', filename='js/navigation_enhanced.js') }}"></script>
    <!-- Navigation Enhancement Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced navigation scroll behavior
            const navbar = document.querySelector('.navbar');
            let lastScrollTop = 0;

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 50) {
                    navbar.classList.add('navbar-scrolled');
                } else {
                    navbar.classList.remove('navbar-scrolled');
                }

                lastScrollTop = scrollTop;
            });

            // Auto-close mobile menu on link click
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            const navbarCollapse = document.querySelector('.navbar-collapse');

            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (navbarCollapse.classList.contains('show')) {
                        const bsCollapse = new bootstrap.Collapse(navbarCollapse, {
                            toggle: false
                        });
                        bsCollapse.hide();
                    }
                });
            });

            // Enhanced dropdown behavior
            const dropdownElements = document.querySelectorAll('.dropdown');
            dropdownElements.forEach(dropdown => {
                dropdown.addEventListener('show.bs.dropdown', function() {
                    this.querySelector('.dropdown-menu').style.animation = 'fadeInUp 0.2s ease';
                });
            });

            // Smooth scroll for anchor links
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    // Skip if href is just '#' or empty
                    if (!href || href === '#' || href.length <= 1) {
                        return;
                    }

                    try {
                        const target = document.querySelector(href);
                        if (target) {
                            e.preventDefault();
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    } catch (error) {
                        // Invalid selector, skip smooth scrolling
                        console.warn('Invalid selector for smooth scroll:', href);
                    }
                });
            });

            // Add loading states to buttons (excluding filter and search buttons)
            const submitButtons = document.querySelectorAll('button[type="submit"], .btn-submit');
            submitButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Skip loading state for filter/search buttons and GET forms
                    const isFilterButton = this.textContent.toLowerCase().includes('filter') ||
                                          this.textContent.toLowerCase().includes('search') ||
                                          this.classList.contains('btn-filter') ||
                                          this.classList.contains('btn-search');

                    const isGetForm = this.form && this.form.method.toLowerCase() === 'get';

                    // Skip forms with custom submit handling
                    const hasCustomSubmit = this.form && this.form.hasAttribute('data-custom-submit');

                    if (!isFilterButton && !isGetForm && !hasCustomSubmit && this.form && this.form.checkValidity()) {
                        // Store original content for restoration
                        this.dataset.originalContent = this.innerHTML;
                        this.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Processing...';
                        this.disabled = true;

                        // Set a timeout to reset the button if submission takes too long
                        const resetTimeout = setTimeout(() => {
                            if (this.innerHTML.includes('Processing...') && this.dataset.originalContent) {
                                this.innerHTML = this.dataset.originalContent;
                                this.disabled = false;
                                console.warn('Global handler: Form submission timeout - button reset');
                            }
                        }, 10000); // 10 second timeout

                        // Store timeout ID for potential cleanup
                        this.dataset.resetTimeout = resetTimeout;
                    } else if (isFilterButton || isGetForm) {
                        // Add subtle feedback for filter buttons
                        this.style.transform = 'scale(0.95)';
                        setTimeout(() => {
                            this.style.transform = 'scale(1)';
                        }, 150);
                    } else if (hasCustomSubmit) {
                        console.log('Global handler: Skipping form with custom submit handling');
                    }
                    }
                });
            });

            // Reset button states when page becomes visible again (handles modal submissions)
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    const processingButtons = document.querySelectorAll('button[type="submit"]:disabled');
                    processingButtons.forEach(button => {
                        if (button.innerHTML.includes('Processing...') && button.dataset.originalContent) {
                            // Clear any existing timeout
                            if (button.dataset.resetTimeout) {
                                clearTimeout(parseInt(button.dataset.resetTimeout));
                            }
                            button.innerHTML = button.dataset.originalContent;
                            button.disabled = false;
                        }
                    });
                }
            });

            // Reset button states on form submission errors
            window.addEventListener('error', function() {
                const processingButtons = document.querySelectorAll('button[type="submit"]:disabled');
                processingButtons.forEach(button => {
                    if (button.innerHTML.includes('Processing...') && button.dataset.originalContent) {
                        // Clear any existing timeout
                        if (button.dataset.resetTimeout) {
                            clearTimeout(parseInt(button.dataset.resetTimeout));
                        }
                        button.innerHTML = button.dataset.originalContent;
                        button.disabled = false;
                    }
                });
            });

            // Enhanced card hover effects
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Toast notification enhancements
            const toastElements = document.querySelectorAll('.toast');
            toastElements.forEach(toastEl => {
                const toast = new bootstrap.Toast(toastEl, {
                    animation: true,
                    autohide: true,
                    delay: 5000
                });
                toast.show();
            });

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Keyboard navigation enhancement
            document.addEventListener('keydown', function(e) {
                // Alt + H for Help
                if (e.altKey && e.key === 'h') {
                    e.preventDefault();
                    const helpLink = document.querySelector('a[href*="docs"]');
                    if (helpLink) helpLink.click();
                }

                // Alt + D for Dashboard
                if (e.altKey && e.key === 'd') {
                    e.preventDefault();
                    const dashboardLink = document.querySelector('a[href*="dashboard"]');
                    if (dashboardLink) dashboardLink.click();
                }
            });
        });
    </script>
    <!-- AI Chatbot -->
    <script src="{{ url_for('static', filename='js/ai_chatbot.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
