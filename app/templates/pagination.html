<!-- Enhanced Responsive Pagination Component -->
{% if pagination and pagination.total_pages > 1 %}
<nav aria-label="Page navigation" class="mt-4">
    <!-- Pagination Info & Controls -->
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-3 gap-3">
        <div class="pagination-info text-muted small">
            <i class="bi bi-info-circle me-1"></i>
            Showing {{ ((pagination.page - 1) * pagination.per_page) + 1 }} to
            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }}
            of {{ pagination.total }} entries
        </div>

        <div class="d-flex align-items-center gap-3">
            <!-- Per Page Selector -->
            <div class="d-flex align-items-center">
                <label for="per_page" class="form-label mb-0 me-2 small">Show:</label>
                <select class="form-select form-select-sm" id="per_page" onchange="changePerPage(this.value)" style="width: auto;">
                    <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                    <option value="25" {% if pagination.per_page == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
                </select>
            </div>

            <!-- Quick Jump (for larger datasets) -->
            {% if pagination.total_pages > 10 %}
            <div class="d-none d-md-flex align-items-center">
                <label for="jump_to_page" class="form-label mb-0 me-2 small">Go to:</label>
                <input type="number" class="form-control form-control-sm" id="jump_to_page"
                       style="width: 70px;" min="1" max="{{ pagination.total_pages }}"
                       placeholder="{{ pagination.page }}"
                       onkeypress="if(event.key==='Enter') jumpToPage(this.value)">
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Main Pagination -->
    <div class="pagination-wrapper">
        <!-- Mobile Pagination (Simplified) -->
        <div class="d-block d-md-none">
            <div class="d-flex justify-content-between align-items-center">
                {% if pagination.has_prev %}
                <a class="btn btn-outline-primary btn-sm" href="{{ build_pagination_url(pagination.prev_page) }}">
                    <i class="bi bi-chevron-left me-1"></i> Previous
                </a>
                {% else %}
                <span class="btn btn-outline-secondary btn-sm disabled">
                    <i class="bi bi-chevron-left me-1"></i> Previous
                </span>
                {% endif %}

                <span class="mx-3 text-muted small">
                    Page {{ pagination.page }} of {{ pagination.total_pages }}
                </span>

                {% if pagination.has_next %}
                <a class="btn btn-outline-primary btn-sm" href="{{ build_pagination_url(pagination.next_page) }}">
                    Next <i class="bi bi-chevron-right ms-1"></i>
                </a>
                {% else %}
                <span class="btn btn-outline-secondary btn-sm disabled">
                    Next <i class="bi bi-chevron-right ms-1"></i>
                </span>
                {% endif %}
            </div>
        </div>

        <!-- Desktop Pagination (Full) -->
        <ul class="pagination justify-content-center mb-0 d-none d-md-flex">
            <!-- First page -->
            {% if pagination.page > 3 %}
            <li class="page-item">
                <a class="page-link" href="{{ build_pagination_url(1) }}"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="First page">
                    <i class="bi bi-chevron-double-left"></i>
                </a>
            </li>
            {% endif %}

            <!-- Previous page -->
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ build_pagination_url(pagination.prev_page) }}"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Previous page">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="bi bi-chevron-left"></i>
                </span>
            </li>
            {% endif %}

            <!-- Page numbers with smart truncation -->
            {% set start_page = [pagination.page - 2, 1] | max %}
            {% set end_page = [pagination.page + 2, pagination.total_pages] | min %}

            <!-- Show ellipsis if we're not starting from page 1 -->
            {% if start_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ build_pagination_url(1) }}">1</a>
                </li>
                {% if start_page > 2 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endif %}

            {% for page_num in range(start_page, end_page + 1) %}
                {% if page_num == pagination.page %}
                <li class="page-item active">
                    <span class="page-link current-page">{{ page_num }}</span>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="{{ build_pagination_url(page_num) }}">{{ page_num }}</a>
                </li>
                {% endif %}
            {% endfor %}

            <!-- Show ellipsis if we're not ending at the last page -->
            {% if end_page < pagination.total_pages %}
                {% if end_page < pagination.total_pages - 1 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ build_pagination_url(pagination.total_pages) }}">{{ pagination.total_pages }}</a>
                </li>
            {% endif %}

            <!-- Next page -->
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ build_pagination_url(pagination.next_page) }}"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Next page">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="bi bi-chevron-right"></i>
                </span>
            </li>
            {% endif %}

            <!-- Last page -->
            {% if pagination.page < pagination.total_pages - 2 %}
            <li class="page-item">
                <a class="page-link" href="{{ build_pagination_url(pagination.total_pages) }}"
                   data-bs-toggle="tooltip" data-bs-placement="top" title="Last page">
                    <i class="bi bi-chevron-double-right"></i>
                </a>
            </li>
            {% endif %}
        </ul>
    </div>
</nav>

<!-- Enhanced Pagination Styles -->
<style>
.pagination-wrapper {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pagination .page-link {
    border: none;
    border-radius: 8px;
    margin: 0 2px;
    padding: 0.5rem 0.75rem;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
    background: transparent;
}

.pagination .page-link:hover {
    background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-color: #3b82f6;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #9ca3af;
    background: transparent;
}

/* Subtle click feedback without loader */
.pagination .page-link:active {
    transform: translateY(0) scale(0.98);
    transition: transform 0.1s ease;
}

.pagination-info {
    font-weight: 500;
}

@media (max-width: 768px) {
    .pagination-info {
        text-align: center;
        font-size: 0.8rem;
    }

    .pagination-wrapper {
        padding: 1rem;
    }
}
</style>

<!-- Enhanced Pagination JavaScript -->
<script>
// Store and restore scroll position for pagination
function preserveScrollPosition() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    sessionStorage.setItem('paginationScrollPosition', scrollPosition);
}

function restoreScrollPosition() {
    const scrollPosition = sessionStorage.getItem('paginationScrollPosition');
    if (scrollPosition) {
        // Smooth scroll to the preserved position
        window.scrollTo({
            top: parseInt(scrollPosition),
            behavior: 'smooth'
        });
        sessionStorage.removeItem('paginationScrollPosition');
    }
}

function changePerPage(perPage) {
    preserveScrollPosition();
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', '1'); // Reset to first page
    window.location.href = url.toString();
}

function jumpToPage(pageNum) {
    const totalPages = {{ pagination.total_pages }};
    const page = parseInt(pageNum);

    if (page >= 1 && page <= totalPages) {
        preserveScrollPosition();
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    } else {
        alert(`Please enter a page number between 1 and ${totalPages}`);
    }
}

// Preserve scroll position for all pagination links
document.addEventListener('DOMContentLoaded', function() {
    // Restore scroll position on page load
    restoreScrollPosition();

    const paginationLinks = document.querySelectorAll('.pagination .page-link, .btn-outline-primary[href*="page="]');

    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!this.closest('.page-item')?.classList.contains('disabled') &&
                !this.closest('.page-item')?.classList.contains('active')) {
                preserveScrollPosition();
            }
        });
    });

    // Keyboard shortcuts for pagination
    document.addEventListener('keydown', function(e) {
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        if (e.key === 'ArrowLeft' && e.ctrlKey) {
            e.preventDefault();
            const prevLink = document.querySelector('.pagination .page-item:not(.disabled) .page-link[href*="page={{ pagination.page - 1 }}"]');
            if (prevLink) {
                preserveScrollPosition();
                prevLink.click();
            }
        }

        if (e.key === 'ArrowRight' && e.ctrlKey) {
            e.preventDefault();
            const nextLink = document.querySelector('.pagination .page-item:not(.disabled) .page-link[href*="page={{ pagination.page + 1 }}"]');
            if (nextLink) {
                preserveScrollPosition();
                nextLink.click();
            }
        }
    });
});
</script>
{% endif %}
