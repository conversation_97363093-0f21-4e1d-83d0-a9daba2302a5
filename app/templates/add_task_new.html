{% extends "base.html" %}

{% block title %}A                    <div class="stat-item">
                        <i class="bi bi-keyboard"></i>
                        <span>Use Ctrl+1-5 for quick access</span>
                    </div>ask - AdhocLog{% endblock %}

{% block content %}
<!-- Include our custom CSS -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/task_form.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-features.css') }}">

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Add New Task</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <!-- Quick Templates Section -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning-fill"></i> Quick Templates
                    <small class="text-muted ms-2">Click to auto-fill form • Shortcuts: Ctrl+1-5</small>
                </h5>
            </div>
            <div class="card-body">
                <div id="template-buttons-container" class="template-buttons-container">
                    <!-- Template buttons will be populated by JavaScript -->
                    <div class="text-center py-2">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading templates...</span>
                        </div>
                    </div>
                </div>
                <div class="template-stats">
                    <div class="stat-item">
                        <i class="bi bi-clock"></i>
                        <span>Save time with one-click templates</span>
                    </div>
                    <div class="stat-item">
                        <i class="bi bi-keyboard"></i>
                        <span>Use Ctrl+1-5 for quick access</span>
                    </div>
                    <div class="stat-item">
                        <i class="bi bi-check-circle"></i>
                        <span>Ctrl+Enter to submit</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear-fill"></i> Advanced Actions
                    <small class="text-muted ms-2">Create templates & bulk entry</small>
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-outline-primary w-100" data-bs-toggle="modal" data-bs-target="#customTemplateModal">
                            <i class="bi bi-bookmark-plus"></i> Create Custom Template
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button type="button" class="btn btn-outline-success w-100" data-bs-toggle="modal" data-bs-target="#bulkEntryModal">
                            <i class="bi bi-list-ul"></i> Bulk Entry Mode
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="bi bi-star"></i> Create your own templates or add multiple tasks at once
                    </small>
                </div>
            </div>
        </div>

        <!-- Task Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-form"></i> Task Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row g-3">
                        <!-- Title -->
                        <div class="col-12">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ task_data.get('title', '') }}" required
                                   placeholder="Enter task title...">
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="col-md-6">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="{{ task_data.get('date', '') }}">
                        </div>

                        <!-- Estimated Time -->
                        <div class="col-md-6">
                            <label for="est_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="est_time" name="est_time"
                                   value="{{ task_data.get('est_time', 30) }}" min="1" max="999"
                                   placeholder="30">
                        </div>

                        <!-- Classification -->
                        <div class="col-md-6">
                            <label for="classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if task_data.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>

                        <!-- Category (Auto-filled) -->
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="{{ task_data.get('category', '') }}" readonly>
                            <div class="form-text">Auto-filled based on classification</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Description / Actions Taken</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Describe what you did or plan to do...">{{ task_data.get('description', '') }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Add Task
                            </button>
                            <a href="{{ url_for('task_list') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Add from Previous Tasks -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Add from Previous Tasks
                    <small class="text-muted ms-2">Click to auto-fill form</small>
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="quickAddContainer" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2 text-muted">Loading recent tasks...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Classification Guide -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Classification Guide</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Classification</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Planning</strong></td>
                                <td>
                                    <small>
                                        • Team meetings<br>
                                        • Brainstorming sessions<br>
                                        • Coordination with other teams
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Offline Processing</strong></td>
                                <td>
                                    <small>
                                        • Research and investigation<br>
                                        • Creating Excel reports<br>
                                        • Drafting documentation<br>
                                        • Preparing presentations<br>
                                        • Scripting/Coding<br>
                                        • Training/Certification
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Execution</strong></td>
                                <td>
                                    <small>
                                        • Presenting to managers<br>
                                        • Health checks<br>
                                        • Client System/Technology Operations<br>
                                        • Attendance/PTO Reminder<br>
                                        • Client Engineering Daily Case Management
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Business Support Activities</strong></td>
                                <td>
                                    <small>
                                        • Attend Townhall and other activities<br>
                                        • 1-on-1 Meeting/Catch-Up<br>
                                        • Daily Standup<br>
                                        • Technical Ramblings<br>
                                        • Client Engineering Team Weekly Meeting<br>
                                        • Audience of a Presentation
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Operational Project Involvement</strong></td>
                                <td>
                                    <small>
                                        • Assisting colleagues with difficult issues<br>
                                        • Participating in high-priority issue/SRT<br>
                                        • Operation Request from US team<br>
                                        • Involvement on US Projects<br>
                                        • Change Management
                                    </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom Template Creation Modal -->
<div class="modal fade" id="customTemplateModal" tabindex="-1" aria-labelledby="customTemplateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customTemplateModalLabel">
                    <i class="bi bi-bookmark-plus"></i> Create Custom Template
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customTemplateForm" novalidate>
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="template-name" class="form-label">Template Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="template-name" name="name" required
                                   placeholder="My Custom Template">
                            <div class="invalid-feedback">
                                Please provide a template name.
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="template-title" class="form-label">Task Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="template-title" name="title" required
                                   placeholder="Task title template">
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="template-classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="template-classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}">{{ cls }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="template-time" class="form-label">Estimated Time (minutes) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="template-time" name="est_time" required
                                   min="1" max="999" placeholder="30">
                            <div class="invalid-feedback">
                                Please provide estimated time.
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="template-description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="template-description" name="description" rows="3" required
                                      placeholder="Template description..."></textarea>
                            <div class="invalid-feedback">
                                Please provide a description.
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCustomTemplate">
                    <i class="bi bi-check-lg"></i> Save Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Entry Modal -->
<div class="modal fade" id="bulkEntryModal" tabindex="-1" aria-labelledby="bulkEntryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkEntryModalLabel">
                    <i class="bi bi-list-ul"></i> Bulk Task Entry
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <p class="text-muted mb-3">
                        <i class="bi bi-info-circle"></i>
                        Add multiple tasks at once. Use the buttons below to add rows, or copy common fields.
                    </p>

                    <div class="d-flex gap-2 mb-3">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="addBulkRow">
                            <i class="bi bi-plus"></i> Add Row
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="copyFirstRow">
                            <i class="bi bi-copy"></i> Copy First Row
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clearAllRows">
                            <i class="bi bi-trash"></i> Clear All
                        </button>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered" id="bulkTasksTable">
                        <thead class="table-light">
                            <tr>
                                <th style="width: 25%;">Title <span class="text-danger">*</span></th>
                                <th style="width: 20%;">Classification <span class="text-danger">*</span></th>
                                <th style="width: 35%;">Description <span class="text-danger">*</span></th>
                                <th style="width: 10%;">Time (min)</th>
                                <th style="width: 10%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="bulkTasksBody">
                            <!-- Bulk task rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="bi bi-lightbulb"></i>
                        <strong>Tips:</strong>
                        <ul class="mb-0 mt-1">
                            <li>Fill out the first row completely, then use "Copy First Row" to replicate common fields</li>
                            <li>Only modify the fields that differ between tasks</li>
                            <li>Tasks with missing required fields will be skipped</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="me-auto">
                    <span class="text-muted" id="bulkTaskCount">0 tasks ready</span>
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="submitBulkTasks" disabled>
                    <i class="bi bi-check-lg"></i> Create All Tasks
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Refactored Task Form JavaScript Modules -->
<!-- Core module (must be loaded first) -->
<script src="{{ url_for('static', filename='js/task_form_core.js') }}"></script>
<!-- Feature modules -->
<script src="{{ url_for('static', filename='js/task_template_manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_keyboard_shortcuts.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_smart_suggestions.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_ai_enhancements.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_analytics_tracker.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_form_validation.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_ui_notifications.js') }}"></script>
<script src="{{ url_for('static', filename='js/task_bulk_operations.js') }}"></script>
<script>
// Global function for filling task form (backward compatibility)
function fillTaskForm(taskDataJson) {
    const task = JSON.parse(decodeURIComponent(taskDataJson));

    if (window.taskFormManager) {
        // Use the enhanced template system
        window.taskFormManager.applyTemplate({
            name: task.title,
            data: task
        });
    } else {
        // Fallback to original method
        fillTaskFormFallback(task);
    }
}

function fillTaskFormFallback(task) {
    // Fill form fields
    document.getElementById('title').value = task.title;
    document.getElementById('classification').value = task.classification;
    document.getElementById('category').value = task.category;
    document.getElementById('description').value = task.description;
    document.getElementById('est_time').value = task.est_time;

    // Keep today's date
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    document.getElementById('date').value = `${year}-${month}-${day}`;

    // Scroll to form
    document.getElementById('title').scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Focus on title field for easy editing
    setTimeout(() => {
        document.getElementById('title').focus();
        document.getElementById('title').select();
    }, 500);
}

document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    const dateInput = document.getElementById('date');
    if (!dateInput.value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        dateInput.value = `${year}-${month}-${day}`;
    }

    // Dynamic category mapping (enhanced by main script)
    const classificationSelect = document.getElementById('classification');
    const categoryInput = document.getElementById('category');

    if (classificationSelect && categoryInput) {
        classificationSelect.addEventListener('change', function() {
            const classification = this.value;
            // Try enhanced mapping first
            if (window.taskFormManager) {
                window.taskFormManager.updateCategoryFromClassification();
            } else {
                // Fallback category mapping
                const categoryMap = {
                    'Planning': 'Adhoc',
                    'Offline Processing': 'Adhoc',
                    'Execution': 'Adhoc',
                    'Business Support Activities': 'Business Support Activities',
                    'Operational Project Involvement': 'Adhoc'
                };
                categoryInput.value = categoryMap[classification] || '';
            }
        });
    }

    // Enhanced form validation
    const form = document.querySelector('form[method="POST"]');
    if (form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });

        // Real-time validation feedback
        const requiredInputs = form.querySelectorAll('[required]');
        requiredInputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    }

    // Wait for enhanced script to load, otherwise use fallback
    setTimeout(() => {
        if (!window.taskFormManager) {
            console.log('Enhanced script not loaded, using fallback...');
            loadRecentTasksFallback();
        }
    }, 1000);

    function loadRecentTasksFallback() {
        fetch('/api/get_recent_tasks')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('quickAddContainer');
                if (data.tasks && data.tasks.length > 0) {
                    displayFallbackRecentTasks(data.tasks, container);
                } else {
                    container.innerHTML = '<div class="text-center py-3"><i class="bi bi-inbox text-muted"></i><p class="text-muted mt-2 mb-0">No recent tasks found.</p></div>';
                }
            })
            .catch(error => {
                console.error('Error loading recent tasks:', error);
                document.getElementById('quickAddContainer').innerHTML =
                    '<div class="text-center py-3"><i class="bi bi-exclamation-triangle text-warning"></i><p class="text-muted mt-2 mb-0">Error loading recent tasks.</p></div>';
            });
    }

    function displayFallbackRecentTasks(tasks, container) {
        let html = '<div class="list-group list-group-flush">';
        tasks.forEach((task, index) => {
            const shortTitle = task.title.length > 50 ? task.title.substring(0, 50) + '...' : task.title;
            const shortDesc = task.description.length > 80 ? task.description.substring(0, 80) + '...' : task.description;

            html += `
                <div class="list-group-item list-group-item-action py-2" style="cursor: pointer; border-left: 3px solid #0d6efd;" onclick="fillTaskForm('${encodeURIComponent(JSON.stringify(task))}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1" style="font-size: 0.9rem; font-weight: 600;">${shortTitle}</h6>
                            <p class="mb-1 text-muted" style="font-size: 0.8rem;">${shortDesc}</p>
                            <div class="d-flex gap-1">
                                <span class="badge bg-secondary" style="font-size: 0.7rem;">${task.classification}</span>
                                <span class="badge bg-info" style="font-size: 0.7rem;">${task.category}</span>
                            </div>
                        </div>
                        <div class="text-end ms-2">
                            <small class="text-muted d-block">${task.est_time} min</small>
                            <i class="bi bi-arrow-right-circle text-primary"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        container.innerHTML = html;
    }

    // Initialize refactored task form system
    try {
        if (window.TaskFormCore) {
            window.taskFormCore = new TaskFormCore();
            console.log('TaskFormCore initialized');

            // Initialize all feature modules
            const templateManager = new TaskTemplateManager();
            const keyboardShortcuts = new TaskKeyboardShortcuts();
            const smartSuggestions = new TaskSmartSuggestions();
            const aiEnhancements = new TaskAIEnhancements();
            const analyticsTracker = new TaskAnalyticsTracker();
            const formValidation = new TaskFormValidation();
            const uiNotifications = new TaskUINotifications();
            const bulkOperations = new TaskBulkOperations();

            // Inject dependencies into core module
            window.taskFormCore.injectTemplateManager(templateManager);
            window.taskFormCore.injectKeyboardShortcuts(keyboardShortcuts);
            window.taskFormCore.injectSmartSuggestions(smartSuggestions);
            window.taskFormCore.injectAIEnhancements(aiEnhancements);
            window.taskFormCore.injectAnalyticsTracker(analyticsTracker);
            window.taskFormCore.injectFormValidation(formValidation);
            window.taskFormCore.injectUINotifications(uiNotifications);
            window.taskFormCore.injectBulkOperations(bulkOperations);

            // Create global aliases for backward compatibility
            window.taskFormManager = window.taskFormCore;
            window.taskForm = window.taskFormCore;

            console.log('All task form modules initialized and injected successfully');
        } else {
            console.error('TaskFormCore not found - check if task_form_core.js loaded correctly');
        }
    } catch (error) {
        console.error('Error initializing refactored task form system:', error);
    }

    // Phase 2 Features - Custom Template and Bulk Entry
    initializeAdvancedFeatures();
});

function initializeAdvancedFeatures() {
    // Custom Template Modal Setup
    const customTemplateModal = document.getElementById('customTemplateModal');
    const saveCustomTemplateBtn = document.getElementById('saveCustomTemplate');
    const customTemplateForm = document.getElementById('customTemplateForm');

    // Save Custom Template
    if (saveCustomTemplateBtn) {
        saveCustomTemplateBtn.addEventListener('click', function() {
            if (customTemplateForm.checkValidity()) {
                const formData = new FormData(customTemplateForm);
                const templateData = {
                    name: formData.get('name'),
                    title: formData.get('title'),
                    classification: formData.get('classification'),
                    description: formData.get('description'),
                    est_time: parseInt(formData.get('est_time'))
                };

                // Show loading state
                saveCustomTemplateBtn.disabled = true;
                saveCustomTemplateBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';

                // Save via API
                fetch('/api/templates', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(templateData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal and show success message
                        const modal = bootstrap.Modal.getInstance(customTemplateModal);
                        modal.hide();

                        // Show success notification
                        showNotification('Custom template created successfully!', 'success');

                        // Reset form
                        customTemplateForm.reset();
                        customTemplateForm.classList.remove('was-validated');
                    } else {
                        showNotification('Error creating template: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error creating template', 'error');
                })
                .finally(() => {
                    // Reset button state
                    saveCustomTemplateBtn.disabled = false;
                    saveCustomTemplateBtn.innerHTML = '<i class="bi bi-check-lg"></i> Save Template';
                });
            } else {
                customTemplateForm.classList.add('was-validated');
            }
        });
    }

    // Pre-fill custom template form with current values when modal opens
    if (customTemplateModal) {
        customTemplateModal.addEventListener('shown.bs.modal', function() {
            const form = document.querySelector('form[method="POST"]');
            if (form) {
                const formData = new FormData(form);
                document.getElementById('template-title').value = formData.get('title') || '';
                document.getElementById('template-classification').value = formData.get('classification') || '';
                document.getElementById('template-description').value = formData.get('description') || '';
                document.getElementById('template-time').value = formData.get('est_time') || '30';
            }
        });

        // Reset form when modal is hidden
        customTemplateModal.addEventListener('hidden.bs.modal', function() {
            customTemplateForm.reset();
            customTemplateForm.classList.remove('was-validated');
        });
    }

    // Bulk Entry Modal Setup
    const bulkEntryModal = document.getElementById('bulkEntryModal');
    let bulkTaskCounter = 0;

    if (bulkEntryModal) {
        bulkEntryModal.addEventListener('shown.bs.modal', function() {
            setupBulkEntryModal();
        });

        bulkEntryModal.addEventListener('hidden.bs.modal', function() {
            // Reset bulk entry when modal is closed
            const bulkTasksBody = document.getElementById('bulkTasksBody');
            if (bulkTasksBody) {
                bulkTasksBody.innerHTML = '';
                bulkTaskCounter = 0;
            }
        });
    }

    function setupBulkEntryModal() {
        const bulkTasksBody = document.getElementById('bulkTasksBody');
        const addBulkRowBtn = document.getElementById('addBulkRow');
        const copyFirstRowBtn = document.getElementById('copyFirstRow');
        const clearAllRowsBtn = document.getElementById('clearAllRows');
        const submitBulkTasksBtn = document.getElementById('submitBulkTasks');

        // Clear existing event listeners by cloning elements
        if (addBulkRowBtn) {
            const newAddBtn = addBulkRowBtn.cloneNode(true);
            addBulkRowBtn.parentNode.replaceChild(newAddBtn, addBulkRowBtn);
            newAddBtn.addEventListener('click', () => addBulkTaskRow());
        }

        if (copyFirstRowBtn) {
            const newCopyBtn = copyFirstRowBtn.cloneNode(true);
            copyFirstRowBtn.parentNode.replaceChild(newCopyBtn, copyFirstRowBtn);
            newCopyBtn.addEventListener('click', () => copyFirstBulkRow());
        }

        if (clearAllRowsBtn) {
            const newClearBtn = clearAllRowsBtn.cloneNode(true);
            clearAllRowsBtn.parentNode.replaceChild(newClearBtn, clearAllRowsBtn);
            newClearBtn.addEventListener('click', () => clearAllBulkRows());
        }

        if (submitBulkTasksBtn) {
            const newSubmitBtn = submitBulkTasksBtn.cloneNode(true);
            submitBulkTasksBtn.parentNode.replaceChild(newSubmitBtn, submitBulkTasksBtn);
            newSubmitBtn.addEventListener('click', () => submitBulkTasks());
        }

        // Initialize with one empty row
        addBulkTaskRow();
    }

    function addBulkTaskRow(data = null) {
        const bulkTasksBody = document.getElementById('bulkTasksBody');
        const rowIndex = bulkTaskCounter++;

        const defaultData = data || {
            title: '',
            classification: '',
            description: '',
            est_time: '30'
        };

        const row = document.createElement('tr');
        row.className = 'bulk-task-row';
        row.setAttribute('data-row-index', rowIndex);
        row.innerHTML = `
            <td>
                <input type="text" class="form-control form-control-sm" placeholder="Task title..."
                       value="${defaultData.title}" required
                       oninput="updateBulkTaskCount()">
            </td>
            <td>
                <select class="form-select form-select-sm" required onchange="updateBulkTaskCount()">
                    <option value="">Select...</option>
                    <option value="Planning" ${defaultData.classification === 'Planning' ? 'selected' : ''}>Planning</option>
                    <option value="Offline Processing" ${defaultData.classification === 'Offline Processing' ? 'selected' : ''}>Offline Processing</option>
                    <option value="Execution" ${defaultData.classification === 'Execution' ? 'selected' : ''}>Execution</option>
                    <option value="Business Support Activities" ${defaultData.classification === 'Business Support Activities' ? 'selected' : ''}>Business Support Activities</option>
                    <option value="Operational Project Involvement" ${defaultData.classification === 'Operational Project Involvement' ? 'selected' : ''}>Operational Project Involvement</option>
                </select>
            </td>
            <td>
                <textarea class="form-control form-control-sm" rows="2" placeholder="Description..."
                          required oninput="updateBulkTaskCount()">${defaultData.description}</textarea>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" min="1" max="999"
                       value="${defaultData.est_time}" required
                       oninput="updateBulkTaskCount()">
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeBulkTaskRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;

        bulkTasksBody.appendChild(row);
        row.classList.add('adding');
        updateBulkTaskCount();
    }

    function copyFirstBulkRow() {
        const firstRow = document.querySelector('.bulk-task-row');
        if (firstRow) {
            const inputs = firstRow.querySelectorAll('input, select, textarea');
            const data = {
                title: inputs[0].value,
                classification: inputs[1].value,
                description: inputs[2].value,
                est_time: inputs[3].value
            };
            addBulkTaskRow(data);
        }
    }

    function clearAllBulkRows() {
        const bulkTasksBody = document.getElementById('bulkTasksBody');
        bulkTasksBody.innerHTML = '';
        bulkTaskCounter = 0;
        addBulkTaskRow();
    }

    // Make these functions global so they can be called from inline event handlers
    window.removeBulkTaskRow = function(button) {
        const row = button.closest('.bulk-task-row');
        row.classList.add('removing');
        setTimeout(() => {
            row.remove();
            updateBulkTaskCount();
        }, 300);
    };

    window.updateBulkTaskCount = function() {
        const rows = document.querySelectorAll('.bulk-task-row');
        const validRows = Array.from(rows).filter(row => {
            const inputs = row.querySelectorAll('input[required], select[required], textarea[required]');
            return Array.from(inputs).every(input => input.value.trim() !== '');
        });

        const count = validRows.length;
        const bulkTaskCount = document.getElementById('bulkTaskCount');
        const submitBtn = document.getElementById('submitBulkTasks');

        if (bulkTaskCount) {
            bulkTaskCount.textContent = `${count} task${count !== 1 ? 's' : ''} ready`;
            bulkTaskCount.className = count > 0 ? 'has-tasks' : '';
        }

        if (submitBtn) {
            submitBtn.disabled = count === 0;
        }
    };

    function submitBulkTasks() {
        const rows = document.querySelectorAll('.bulk-task-row');
        const tasks = [];

        rows.forEach(row => {
            const inputs = row.querySelectorAll('input, select, textarea');
            const title = inputs[0].value.trim();
            const classification = inputs[1].value;
            const description = inputs[2].value.trim();
            const est_time = parseInt(inputs[3].value);

            if (title && classification && description && est_time) {
                tasks.push({
                    title,
                    classification,
                    description,
                    est_time,
                    date: new Date().toISOString().split('T')[0]
                });
            }
        });

        if (tasks.length === 0) {
            showNotification('No valid tasks to create', 'warning');
            return;
        }

        const submitBtn = document.getElementById('submitBulkTasks');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Creating Tasks...';

        // Submit bulk tasks
        fetch('/api/tasks/bulk', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ tasks })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const modal = bootstrap.Modal.getInstance(bulkEntryModal);
                modal.hide();

                showNotification(`Successfully created ${data.created_count} tasks!`, 'success');

                // Optionally redirect to task list
                setTimeout(() => {
                    window.location.href = '/tasks';
                }, 2000);
            } else {
                showNotification('Error creating tasks: ' + (data.error || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error creating tasks', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="bi bi-check-lg"></i> Create All Tasks';
        });
    }

    // Make the functions available for the modal setup
    window.addBulkTaskRow = addBulkTaskRow;
    window.copyFirstBulkRow = copyFirstBulkRow;
    window.clearAllBulkRows = clearAllBulkRows;
    window.submitBulkTasks = submitBulkTasks;
}

// Utility function to show notifications
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 1060; min-width: 300px;';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
