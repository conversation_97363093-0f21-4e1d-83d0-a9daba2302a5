"""
Documentation routes for AdhocLog
Serves the built MkDocs documentation within the Flask application
"""

from flask import Blueprint, send_from_directory, redirect, url_for, current_app, abort
from pathlib import Path
import os
import logging

# Create the blueprint
docs_bp = Blueprint('docs', __name__, url_prefix='/docs')

def get_docs_directory():
    """Get the documentation directory path"""
    # Look for built documentation in the project root
    project_root = Path(current_app.root_path).parent
    docs_site_dir = project_root / 'docs_site'
    
    if docs_site_dir.exists():
        return docs_site_dir
    
    # Fallback to static docs directory
    static_docs_dir = Path(current_app.root_path) / 'static' / 'docs'
    if static_docs_dir.exists():
        return static_docs_dir
    
    return None

@docs_bp.route('/')
def docs_index():
    """Redirect to documentation index"""
    return redirect(url_for('docs.serve_docs', filename='index.html'))

@docs_bp.route('/<path:filename>')
def serve_docs(filename):
    """Serve documentation files"""
    docs_dir = get_docs_directory()
    
    if not docs_dir:
        current_app.logger.warning("Documentation directory not found")
        return """
        <html>
        <head><title>Documentation Not Available</title></head>
        <body>
            <h1>Documentation Not Available</h1>
            <p>The documentation has not been built yet.</p>
            <p>Please run the application startup process to build the documentation automatically.</p>
            <p><a href="/">Return to AdhocLog</a></p>
        </body>
        </html>
        """, 404
    
    # Handle directory requests - append index.html
    if filename.endswith('/') or ('.' not in filename and '/' not in filename):
        if not filename.endswith('/'):
            filename += '/'
        filename += 'index.html'
    
    # Security check - prevent directory traversal
    if '..' in filename or filename.startswith('/'):
        abort(404)
    
    try:
        return send_from_directory(docs_dir, filename)
    except FileNotFoundError:
        current_app.logger.warning(f"Documentation file not found: {filename}")
        return """
        <html>
        <head><title>Page Not Found</title></head>
        <body>
            <h1>Documentation Page Not Found</h1>
            <p>The requested documentation page could not be found.</p>
            <p><a href="/docs/">Return to Documentation Home</a></p>
            <p><a href="/">Return to AdhocLog</a></p>
        </body>
        </html>
        """, 404

@docs_bp.route('/rebuild')
def rebuild_docs():
    """Rebuild documentation (for development/admin use)"""
    try:
        from .utils.docs_builder import build_documentation
        
        success = build_documentation()
        
        if success:
            return """
            <html>
            <head><title>Documentation Rebuilt</title></head>
            <body>
                <h1>Documentation Rebuilt Successfully!</h1>
                <p>The documentation has been rebuilt and is now available.</p>
                <p><a href="/docs/">View Documentation</a></p>
                <p><a href="/">Return to AdhocLog</a></p>
            </body>
            </html>
            """, 200
        else:
            return """
            <html>
            <head><title>Documentation Build Failed</title></head>
            <body>
                <h1>Documentation Build Failed</h1>
                <p>There was an error rebuilding the documentation.</p>
                <p>Please check the application logs for details.</p>
                <p><a href="/">Return to AdhocLog</a></p>
            </body>
            </html>
            """, 500
            
    except ImportError:
        current_app.logger.error("Documentation builder not available")
        return """
        <html>
        <head><title>Documentation Builder Not Available</title></head>
        <body>
            <h1>Documentation Builder Not Available</h1>
            <p>The documentation builder is not available in this installation.</p>
            <p><a href="/">Return to AdhocLog</a></p>
        </body>
        </html>
        """, 500
    except Exception as e:
        current_app.logger.error(f"Error rebuilding documentation: {e}")
        return f"""
        <html>
        <head><title>Documentation Build Error</title></head>
        <body>
            <h1>Documentation Build Error</h1>
            <p>Error rebuilding documentation: {e}</p>
            <p><a href="/">Return to AdhocLog</a></p>
        </body>
        </html>
        """, 500

# Add a context processor to make docs available in templates
@docs_bp.app_context_processor
def inject_docs_status():
    """Inject documentation availability status into templates"""
    docs_dir = get_docs_directory()
    return {
        'docs_available': docs_dir is not None,
        'docs_url': url_for('docs.docs_index') if docs_dir else None
    }
