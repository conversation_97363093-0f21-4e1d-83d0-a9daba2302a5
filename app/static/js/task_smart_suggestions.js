/**
 * Task Smart Suggestions Module
 * Handles pattern recognition, smart suggestions, and auto-completion features
 */

class TaskSmartSuggestions {
    constructor() {
        this.core = null;
        this.suggestionCache = new Map();
        this.debounceTimers = new Map();
        this.activeSuggestions = new Set();
        this.suggestionHistory = [];

        // Configuration
        this.debounceDelay = 300;
        this.minTitleLength = 3;
        this.maxSuggestions = 5;
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.setupSuggestionListeners();
            this.loadSuggestionHistory();
            console.log('TaskSmartSuggestions initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskSmartSuggestions:', error);
        }
    }

    // Setup event listeners for smart suggestions
    setupSuggestionListeners() {
        const titleField = this.core.getFormElement('title');
        const descriptionField = this.core.getFormElement('description');
        const classificationField = this.core.getFormElement('classification');

        if (titleField) {
            titleField.addEventListener('input', (e) => {
                this.handleTitleInput(e.target.value);
            });

            titleField.addEventListener('blur', () => {
                // Delay hiding suggestions to allow clicking
                setTimeout(() => this.hideSuggestions('title'), 150);
            });
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', (e) => {
                this.handleDescriptionInput(e.target.value);
            });

            descriptionField.addEventListener('blur', () => {
                setTimeout(() => this.hideSuggestions('description'), 150);
            });
        }

        if (classificationField) {
            classificationField.addEventListener('change', (e) => {
                this.handleClassificationChange(e.target.value);
            });
        }
    }

    // Handle title input for suggestions
    handleTitleInput(title) {
        // Clear existing timer
        if (this.debounceTimers.has('title')) {
            clearTimeout(this.debounceTimers.get('title'));
        }

        // Set new timer
        const timer = setTimeout(() => {
            if (title.length >= this.minTitleLength) {
                this.generateTitleSuggestions(title);
            } else {
                this.hideSuggestions('title');
            }
        }, this.debounceDelay);

        this.debounceTimers.set('title', timer);
    }

    // Handle description input for suggestions
    handleDescriptionInput(description) {
        if (this.debounceTimers.has('description')) {
            clearTimeout(this.debounceTimers.get('description'));
        }

        const timer = setTimeout(() => {
            if (description.length >= this.minTitleLength) {
                this.generateDescriptionSuggestions(description);
            } else {
                this.hideSuggestions('description');
            }
        }, this.debounceDelay);

        this.debounceTimers.set('description', timer);
    }

    // Handle classification change for related suggestions
    handleClassificationChange(classification) {
        if (classification) {
            this.generateClassificationBasedSuggestions(classification);
        }
    }

    // Generate title-based suggestions
    async generateTitleSuggestions(title) {
        const cacheKey = `title_${title.toLowerCase()}`;

        // Check cache first
        if (this.suggestionCache.has(cacheKey)) {
            const suggestions = this.suggestionCache.get(cacheKey);
            this.displaySuggestions('title', suggestions);
            return;
        }

        try {
            // Get suggestions from server
            const response = await fetch('/api/suggestions/title', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.suggestions) {
                    // Cache the suggestions
                    this.suggestionCache.set(cacheKey, data.suggestions);
                    this.displaySuggestions('title', data.suggestions);

                    // Track suggestion display if analytics is available
                    if (this.core.analyticsTracker) {
                        this.core.analyticsTracker.trackSuggestionShown('title', data.suggestions, data.confidence || 0);
                    }
                }
            }
        } catch (error) {
            console.error('Error getting title suggestions:', error);
        }
    }

    // Generate description-based suggestions
    async generateDescriptionSuggestions(description) {
        const titleField = this.core.getFormElement('title');
        const title = titleField ? titleField.value : '';

        if (!title) return; // Need title for context

        const cacheKey = `description_${title.toLowerCase()}_${description.toLowerCase()}`;

        if (this.suggestionCache.has(cacheKey)) {
            const suggestions = this.suggestionCache.get(cacheKey);
            this.displaySuggestions('description', suggestions);
            return;
        }

        try {
            const response = await fetch('/api/suggestions/description', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.suggestions) {
                    this.suggestionCache.set(cacheKey, data.suggestions);
                    this.displaySuggestions('description', data.suggestions);

                    if (this.core.analyticsTracker) {
                        this.core.analyticsTracker.trackSuggestionShown('description', data.suggestions, data.confidence || 0);
                    }
                }
            }
        } catch (error) {
            console.error('Error getting description suggestions:', error);
        }
    }

    // Generate classification-based suggestions
    async generateClassificationBasedSuggestions(classification) {
        try {
            // Get duration suggestion
            const titleField = this.core.getFormElement('title');
            const descriptionField = this.core.getFormElement('description');

            const title = titleField ? titleField.value : '';
            const description = descriptionField ? descriptionField.value : '';

            if (title) {
                const response = await fetch('/api/suggestions/duration', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ title, description, classification })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.suggestion) {
                        this.displayDurationSuggestion(data.suggestion, data.confidence || 0);

                        if (this.core.analyticsTracker) {
                            this.core.analyticsTracker.trackSuggestionShown('duration', data.suggestion, data.confidence);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error getting classification-based suggestions:', error);
        }
    }

    // Display suggestions for a field
    displaySuggestions(fieldName, suggestions) {
        if (!suggestions || suggestions.length === 0) {
            this.hideSuggestions(fieldName);
            return;
        }

        // Remove existing suggestions
        this.hideSuggestions(fieldName);

        const field = this.core.getFormElement(fieldName);
        if (!field) return;

        // Create suggestions container
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = `suggestions-container ${fieldName}-suggestions`;
        suggestionsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        `;

        // Add suggestions
        suggestions.slice(0, this.maxSuggestions).forEach((suggestion, index) => {
            const suggestionItem = document.createElement('div');
            suggestionItem.className = 'suggestion-item';
            suggestionItem.style.cssText = `
                padding: 8px 12px;
                cursor: pointer;
                border-bottom: 1px solid #eee;
                transition: background-color 0.2s;
            `;

            // Extract suggestion text based on the suggestion object structure
            let suggestionText = '';
            let suggestionData = suggestion;

            if (typeof suggestion === 'string') {
                suggestionText = suggestion;
            } else if (suggestion && typeof suggestion === 'object') {
                // For title suggestions, use the title property
                if (suggestion.title) {
                    suggestionText = suggestion.title;
                    // Add additional info for richer display
                    suggestionItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="fw-medium text-primary">${this.highlightMatch(suggestion.title, this.getFieldValue('title'))}</div>
                                <small class="text-muted">${suggestion.classification || ''}</small>
                                ${suggestion.usage_count > 1 ? `<span class="badge bg-light text-dark ms-1">${suggestion.usage_count}x</span>` : ''}
                            </div>
                            <small class="text-muted">${suggestion.est_time || ''}${suggestion.est_time ? 'min' : ''}</small>
                        </div>
                    `;
                } else if (suggestion.text) {
                    suggestionText = suggestion.text;
                    suggestionItem.textContent = suggestionText;
                } else {
                    // Fallback for other object types
                    suggestionText = suggestion.value || suggestion.name || JSON.stringify(suggestion);
                    suggestionItem.textContent = suggestionText;
                }
            } else {
                suggestionText = String(suggestion);
                suggestionItem.textContent = suggestionText;
            }

            // If innerHTML wasn't set above, set textContent
            if (!suggestionItem.innerHTML.includes('div')) {
                suggestionItem.textContent = suggestionText;
            }

            // Add hover effect
            suggestionItem.addEventListener('mouseenter', () => {
                suggestionItem.style.backgroundColor = '#f8f9fa';
            });

            suggestionItem.addEventListener('mouseleave', () => {
                suggestionItem.style.backgroundColor = '';
            });

            // Add click handler
            suggestionItem.addEventListener('click', () => {
                this.applySuggestion(fieldName, suggestionText, suggestionData);
            });

            suggestionsContainer.appendChild(suggestionItem);
        });

        // Position container relative to field
        const fieldContainer = field.parentElement;
        fieldContainer.style.position = 'relative';
        fieldContainer.appendChild(suggestionsContainer);

        // Track active suggestion
        this.activeSuggestions.add(fieldName);

        // Add keyboard navigation
        this.setupSuggestionKeyboardNavigation(fieldName, suggestionsContainer);
    }

    // Display duration suggestion
    displayDurationSuggestion(suggestion, confidence) {
        const estTimeField = this.core.getFormElement('estTime');
        if (!estTimeField || estTimeField.value) return; // Don't override existing value

        // Create suggestion badge
        const suggestionBadge = document.createElement('div');
        suggestionBadge.className = 'duration-suggestion-badge';
        suggestionBadge.style.cssText = `
            position: absolute;
            top: -8px;
            right: 8px;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            z-index: 1001;
            animation: fadeInDown 0.3s ease;
        `;
        suggestionBadge.innerHTML = `${suggestion} min <small>(${Math.round(confidence * 100)}%)</small>`;

        // Add click handler
        suggestionBadge.addEventListener('click', () => {
            this.applySuggestion('est_time', suggestion);
            suggestionBadge.remove();
        });

        // Position relative to field
        const fieldContainer = estTimeField.parentElement;
        fieldContainer.style.position = 'relative';
        fieldContainer.appendChild(suggestionBadge);

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (suggestionBadge.parentNode) {
                suggestionBadge.remove();
            }
        }, 8000);
    }

    // Setup keyboard navigation for suggestions
    setupSuggestionKeyboardNavigation(fieldName, container) {
        const field = this.core.getFormElement(fieldName);
        if (!field) return;

        let selectedIndex = -1;
        const items = container.querySelectorAll('.suggestion-item');

        const keyHandler = (e) => {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                this.updateSuggestionSelection(items, selectedIndex);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                this.updateSuggestionSelection(items, selectedIndex);
            } else if (e.key === 'Enter' && selectedIndex >= 0) {
                e.preventDefault();
                items[selectedIndex].click();
            } else if (e.key === 'Escape') {
                this.hideSuggestions(fieldName);
            }
        };

        field.addEventListener('keydown', keyHandler);

        // Store handler for cleanup
        container.keyHandler = keyHandler;
        container.field = field;
    }

    // Update suggestion selection highlighting
    updateSuggestionSelection(items, selectedIndex) {
        items.forEach((item, index) => {
            if (index === selectedIndex) {
                item.style.backgroundColor = '#007bff';
                item.style.color = 'white';
            } else {
                item.style.backgroundColor = '';
                item.style.color = '';
            }
        });
    }

    // Apply a suggestion to a field
    applySuggestion(fieldName, value, suggestionData = null) {
        const field = this.core.getFormElement(fieldName);
        if (field) {
            // If we have full suggestion data and it's for the title field, apply all data
            if (fieldName === 'title' && suggestionData && typeof suggestionData === 'object' && suggestionData.title) {
                // Apply the full task data using core's template system
                this.core.applyTemplateData(suggestionData, `Suggestion: ${suggestionData.title}`);
                // Don't show additional notification since template application already shows one
            } else {
                // Just apply the text to the specific field
                field.value = value;
                field.dispatchEvent(new Event('change', { bubbles: true }));

                // Show feedback if UI notifications are available (only for single field updates)
                if (this.core.uiNotifications) {
                    this.core.uiNotifications.showNotification(`Applied ${fieldName} suggestion: ${value}`, 'success', 3000);
                }
            }

            // Hide suggestions
            this.hideSuggestions(fieldName);

            // Track suggestion interaction if analytics is available
            if (this.core.analyticsTracker) {
                this.core.analyticsTracker.trackSuggestionInteraction('accepted', fieldName, value);
            }

            // Store in suggestion history
            this.addToSuggestionHistory(fieldName, value);
        }
    }

    // Hide suggestions for a field
    hideSuggestions(fieldName) {
        const existingSuggestions = document.querySelector(`.${fieldName}-suggestions`);
        if (existingSuggestions) {
            // Clean up keyboard handler
            if (existingSuggestions.keyHandler && existingSuggestions.field) {
                existingSuggestions.field.removeEventListener('keydown', existingSuggestions.keyHandler);
            }
            existingSuggestions.remove();
        }

        this.activeSuggestions.delete(fieldName);
    }

    // Close all active suggestions
    closeAllSuggestions() {
        this.activeSuggestions.forEach(fieldName => {
            this.hideSuggestions(fieldName);
        });
        this.activeSuggestions.clear();
    }

    // Add to suggestion history for learning
    addToSuggestionHistory(fieldName, value) {
        const historyItem = {
            fieldName,
            value,
            timestamp: new Date().toISOString(),
            context: this.getFormContext()
        };

        this.suggestionHistory.push(historyItem);

        // Keep only last 100 items
        if (this.suggestionHistory.length > 100) {
            this.suggestionHistory = this.suggestionHistory.slice(-100);
        }

        // Save to localStorage
        this.saveSuggestionHistory();
    }

    // Get current form context for learning
    getFormContext() {
        if (!this.core) return {};

        const formData = this.core.getFormData();
        return {
            title: formData.title || '',
            classification: formData.classification || '',
            description: formData.description || ''
        };
    }

    // Load suggestion history from localStorage
    loadSuggestionHistory() {
        try {
            const saved = localStorage.getItem('taskSuggestionHistory');
            if (saved) {
                this.suggestionHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('Could not load suggestion history:', error);
            this.suggestionHistory = [];
        }
    }

    // Save suggestion history to localStorage
    saveSuggestionHistory() {
        try {
            localStorage.setItem('taskSuggestionHistory', JSON.stringify(this.suggestionHistory));
        } catch (error) {
            console.warn('Could not save suggestion history:', error);
        }
    }

    // Get suggestion statistics
    getSuggestionStats() {
        const stats = {
            totalSuggestions: this.suggestionHistory.length,
            fieldBreakdown: {},
            recentActivity: this.suggestionHistory.slice(-10)
        };

        this.suggestionHistory.forEach(item => {
            if (!stats.fieldBreakdown[item.fieldName]) {
                stats.fieldBreakdown[item.fieldName] = 0;
            }
            stats.fieldBreakdown[item.fieldName]++;
        });

        return stats;
    }

    // Clear suggestion cache
    clearCache() {
        this.suggestionCache.clear();
    }

    // Helper method to highlight matching text
    highlightMatch(text, query) {
        if (!query || query.length < 2) return text;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // Helper method to get field value
    getFieldValue(fieldName) {
        const field = this.core.getFormElement(fieldName);
        return field ? field.value : '';
    }

    // Cleanup
    destroy() {
        // Clear timers
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();

        // Close all suggestions
        this.closeAllSuggestions();

        // Clear cache
        this.clearCache();

        // Clear history
        this.suggestionHistory = [];
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskSmartSuggestions;
} else {
    window.TaskSmartSuggestions = TaskSmartSuggestions;
}
