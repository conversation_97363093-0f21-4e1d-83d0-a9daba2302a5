/**
 * Task UI Notifications Module
 * Handles notification system, modal management, and UI feedback
 */

class TaskUINotifications {
    constructor() {
        this.core = null;
        this.notificationContainer = null;
        this.activeNotifications = new Map();
        this.notificationQueue = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000;
        
        this.initializeNotificationContainer();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;
        
        try {
            this.setupNotificationStyles();
            console.log('TaskUINotifications initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskUINotifications:', error);
        }
    }

    // Initialize notification container
    initializeNotificationContainer() {
        // Create notification container if it doesn't exist
        this.notificationContainer = document.getElementById('notification-container');
        if (!this.notificationContainer) {
            this.notificationContainer = document.createElement('div');
            this.notificationContainer.id = 'notification-container';
            this.notificationContainer.className = 'notification-container';
            document.body.appendChild(this.notificationContainer);
        }
    }

    // Setup notification styles
    setupNotificationStyles() {
        // Add CSS styles if not already present
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 400px;
                }
                
                .notification {
                    margin-bottom: 10px;
                    padding: 12px 16px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    animation: slideInRight 0.3s ease-out;
                    transition: all 0.3s ease;
                    max-width: 100%;
                    word-wrap: break-word;
                }
                
                .notification.success {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    color: #155724;
                }
                
                .notification.error {
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    color: #721c24;
                }
                
                .notification.warning {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                }
                
                .notification.info {
                    background-color: #d1ecf1;
                    border: 1px solid #bee5eb;
                    color: #0c5460;
                }
                
                .notification-content {
                    flex: 1;
                    margin-right: 10px;
                    white-space: pre-line;
                }
                
                .notification-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    opacity: 0.7;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .notification-close:hover {
                    opacity: 1;
                }
                
                .notification.fade-out {
                    animation: slideOutRight 0.3s ease-in forwards;
                }
                
                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                
                @keyframes slideOutRight {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
                
                .notification-progress {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: 3px;
                    background-color: rgba(0, 0, 0, 0.2);
                    transition: width linear;
                }
                
                .modal-backdrop-custom {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    z-index: 1040;
                    animation: fadeIn 0.3s ease;
                }
                
                .modal-custom {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1050;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                    animation: modalSlideIn 0.3s ease;
                    max-width: 90vw;
                    max-height: 90vh;
                    overflow: auto;
                }
                
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                
                @keyframes modalSlideIn {
                    from {
                        transform: translate(-50%, -60%);
                        opacity: 0;
                    }
                    to {
                        transform: translate(-50%, -50%);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show a notification
    showNotification(message, type = 'info', duration = null, options = {}) {
        const notificationId = this.generateNotificationId();
        duration = duration || this.defaultDuration;

        // Check if we have too many notifications
        if (this.activeNotifications.size >= this.maxNotifications) {
            // Remove oldest notification
            const oldestId = this.activeNotifications.keys().next().value;
            this.removeNotification(oldestId);
        }

        // Create notification element
        const notification = this.createNotificationElement(notificationId, message, type, duration, options);
        
        // Add to container
        this.notificationContainer.appendChild(notification);
        
        // Track active notification
        this.activeNotifications.set(notificationId, {
            element: notification,
            type: type,
            message: message,
            timestamp: Date.now()
        });

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notificationId);
            }, duration);
        }

        // Track notification display if analytics is available
        if (this.core && this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('notification_shown', {
                type: type,
                message_length: message.length,
                duration: duration
            });
        }

        return notificationId;
    }

    // Create notification element
    createNotificationElement(id, message, type, duration, options) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.setAttribute('data-notification-id', id);
        notification.style.position = 'relative';

        // Create content
        const content = document.createElement('div');
        content.className = 'notification-content';
        content.textContent = message;

        // Create close button
        const closeBtn = document.createElement('button');
        closeBtn.className = 'notification-close';
        closeBtn.innerHTML = '×';
        closeBtn.title = 'Close notification';
        closeBtn.addEventListener('click', () => {
            this.removeNotification(id);
        });

        notification.appendChild(content);
        notification.appendChild(closeBtn);

        // Add progress bar if duration is set
        if (duration > 0 && options.showProgress !== false) {
            const progress = document.createElement('div');
            progress.className = 'notification-progress';
            progress.style.width = '100%';
            notification.appendChild(progress);

            // Animate progress bar
            setTimeout(() => {
                progress.style.width = '0%';
                progress.style.transition = `width ${duration}ms linear`;
            }, 10);
        }

        // Add click handler if provided
        if (options.onClick) {
            notification.style.cursor = 'pointer';
            notification.addEventListener('click', (e) => {
                if (e.target !== closeBtn) {
                    options.onClick();
                }
            });
        }

        return notification;
    }

    // Remove a notification
    removeNotification(notificationId) {
        const notification = this.activeNotifications.get(notificationId);
        if (!notification) return;

        // Add fade-out animation
        notification.element.classList.add('fade-out');

        // Remove after animation
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.activeNotifications.delete(notificationId);
        }, 300);
    }

    // Show success notification
    showSuccess(message, duration = 4000, options = {}) {
        return this.showNotification(message, 'success', duration, options);
    }

    // Show error notification
    showError(message, duration = 8000, options = {}) {
        return this.showNotification(message, 'error', duration, options);
    }

    // Show warning notification
    showWarning(message, duration = 6000, options = {}) {
        return this.showNotification(message, 'warning', duration, options);
    }

    // Show info notification
    showInfo(message, duration = 5000, options = {}) {
        return this.showNotification(message, 'info', duration, options);
    }

    // Show confirmation dialog
    showConfirmation(message, title = 'Confirm', options = {}) {
        return new Promise((resolve) => {
            const modalId = this.generateNotificationId();
            const modal = this.createConfirmationModal(modalId, message, title, options, resolve);
            document.body.appendChild(modal);
        });
    }

    // Create confirmation modal
    createConfirmationModal(id, message, title, options, resolve) {
        // Create backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop-custom';
        backdrop.setAttribute('data-modal-id', id);

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal-custom';
        modal.style.minWidth = '400px';

        const modalContent = `
            <div class="modal-header p-3 border-bottom">
                <h5 class="modal-title mb-0">${title}</h5>
            </div>
            <div class="modal-body p-3">
                <p class="mb-0">${message}</p>
            </div>
            <div class="modal-footer p-3 border-top d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary cancel-btn">
                    ${options.cancelText || 'Cancel'}
                </button>
                <button type="button" class="btn btn-primary confirm-btn">
                    ${options.confirmText || 'Confirm'}
                </button>
            </div>
        `;

        modal.innerHTML = modalContent;

        // Add event listeners
        const cancelBtn = modal.querySelector('.cancel-btn');
        const confirmBtn = modal.querySelector('.confirm-btn');

        const cleanup = () => {
            backdrop.remove();
        };

        cancelBtn.addEventListener('click', () => {
            cleanup();
            resolve(false);
        });

        confirmBtn.addEventListener('click', () => {
            cleanup();
            resolve(true);
        });

        backdrop.addEventListener('click', (e) => {
            if (e.target === backdrop) {
                cleanup();
                resolve(false);
            }
        });

        // Handle escape key
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                cleanup();
                resolve(false);
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);

        backdrop.appendChild(modal);
        return backdrop;
    }

    // Show loading notification
    showLoading(message = 'Loading...', options = {}) {
        const loadingId = this.generateNotificationId();
        
        const notification = this.createNotificationElement(
            loadingId, 
            message, 
            'info', 
            0, // No auto-dismiss
            { ...options, showProgress: false }
        );

        // Add loading spinner
        const spinner = document.createElement('div');
        spinner.innerHTML = `
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        `;
        notification.querySelector('.notification-content').prepend(spinner);

        this.notificationContainer.appendChild(notification);
        this.activeNotifications.set(loadingId, {
            element: notification,
            type: 'loading',
            message: message,
            timestamp: Date.now()
        });

        return loadingId;
    }

    // Update loading notification
    updateLoading(loadingId, message) {
        const notification = this.activeNotifications.get(loadingId);
        if (notification) {
            const content = notification.element.querySelector('.notification-content');
            // Keep the spinner, update text
            const textNode = content.lastChild;
            if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                textNode.textContent = message;
            }
        }
    }

    // Hide loading notification
    hideLoading(loadingId) {
        this.removeNotification(loadingId);
    }

    // Clear all notifications
    clearAllNotifications() {
        const notificationIds = Array.from(this.activeNotifications.keys());
        notificationIds.forEach(id => this.removeNotification(id));
    }

    // Show form submission feedback
    showFormSubmissionFeedback(success, message = null) {
        if (success) {
            this.showSuccess(
                message || 'Task created successfully!',
                4000,
                {
                    onClick: () => {
                        // Could navigate to task list or refresh
                    }
                }
            );
        } else {
            this.showError(
                message || 'Failed to create task. Please try again.',
                8000
            );
        }
    }

    // Show template application feedback
    showTemplateAppliedFeedback(templateName) {
        this.showSuccess(`Template "${templateName}" applied successfully`, 3000);
    }

    // Show keyboard shortcut feedback
    showShortcutFeedback(action) {
        this.showInfo(`Shortcut: ${action}`, 2000);
    }

    // Generate unique notification ID
    generateNotificationId() {
        return 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Get notification statistics
    getNotificationStats() {
        return {
            active_count: this.activeNotifications.size,
            max_notifications: this.maxNotifications,
            default_duration: this.defaultDuration,
            active_notifications: Array.from(this.activeNotifications.values()).map(n => ({
                type: n.type,
                message: n.message.substring(0, 50) + (n.message.length > 50 ? '...' : ''),
                timestamp: n.timestamp
            }))
        };
    }

    // Cleanup
    destroy() {
        this.clearAllNotifications();
        
        if (this.notificationContainer && this.notificationContainer.parentNode) {
            this.notificationContainer.parentNode.removeChild(this.notificationContainer);
        }
        
        this.activeNotifications.clear();
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskUINotifications;
} else {
    window.TaskUINotifications = TaskUINotifications;
}
