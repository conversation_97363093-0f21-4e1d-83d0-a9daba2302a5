/**
 * Task Form Validation Module
 * Handles form validation, field interactions, and data processing
 */

class TaskFormValidation {
    constructor() {
        this.core = null;
        this.validationRules = {};
        this.validationErrors = new Map();
        this.realTimeValidation = true;
        this.validationDebounceTime = 500;
        this.debounceTimers = new Map();

        this.initializeValidationRules();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.setupValidationListeners();
            this.setupFormSubmissionValidation();
            console.log('TaskFormValidation initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskFormValidation:', error);
        }
    }

    // Initialize validation rules for form fields
    initializeValidationRules() {
        this.validationRules = {
            title: {
                required: true,
                minLength: 3,
                maxLength: 200,
                pattern: /^[a-zA-Z0-9\s\-_.,!?()[\]]+$/,
                customValidation: (value) => {
                    // Check for meaningful content
                    if (value.trim().length < 3) {
                        return 'Title must be at least 3 characters long';
                    }
                    if (!/[a-zA-Z]/.test(value)) {
                        return 'Title must contain at least one letter';
                    }
                    return null;
                }
            },
            classification: {
                required: true,
                allowedValues: [
                    'Planning',
                    'Offline Processing',
                    'Execution',
                    'Business Support Activities',
                    'Operational Project Involvement'
                ]
            },
            description: {
                required: true,
                minLength: 10,
                maxLength: 1000,
                customValidation: (value) => {
                    if (value.trim().length < 10) {
                        return 'Description must be at least 10 characters long';
                    }
                    if (value.trim().split(/\s+/).length < 3) {
                        return 'Description should contain at least 3 words';
                    }
                    return null;
                }
            },
            est_time: {
                required: true,
                type: 'number',
                min: 1,
                max: 480, // 8 hours max
                customValidation: (value) => {
                    const num = parseInt(value);
                    if (isNaN(num)) {
                        return 'Estimated time must be a valid number';
                    }
                    if (num < 1) {
                        return 'Estimated time must be at least 1 minute';
                    }
                    if (num > 480) {
                        return 'Estimated time cannot exceed 8 hours (480 minutes)';
                    }
                    return null;
                }
            },
            date: {
                required: true,
                type: 'date',
                customValidation: (value) => {
                    if (!value) return 'Date is required';

                    const selectedDate = new Date(value);
                    const today = new Date();

                    // Allow past dates (many tasks are logged retroactively)
                    // Only warn about dates too far in the past or future
                    const oneYearAgo = new Date();
                    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

                    const oneYearFromNow = new Date();
                    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);

                    if (selectedDate < oneYearAgo) {
                        return 'Date seems too far in the past';
                    }

                    if (selectedDate > oneYearFromNow) {
                        return 'Date cannot be more than 1 year in the future';
                    }

                    return null;
                }
            }
        };
    }

    // Setup validation event listeners
    setupValidationListeners() {
        const formElements = this.core.getFormElements();

        Object.keys(this.validationRules).forEach(fieldName => {
            const field = formElements[fieldName] || document.querySelector(`[name="${fieldName}"]`);
            if (!field) return;

            // Real-time validation on input/change
            const eventType = field.type === 'select-one' ? 'change' : 'input';
            field.addEventListener(eventType, (e) => {
                if (this.realTimeValidation) {
                    this.debounceValidation(fieldName, e.target.value);
                }
            });

            // Validation on blur
            field.addEventListener('blur', (e) => {
                this.validateField(fieldName, e.target.value);
            });

            // Track field interactions for analytics
            field.addEventListener('focus', () => {
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackFieldInteraction(fieldName, 'focus');
                }
            });

            field.addEventListener('blur', () => {
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackFieldInteraction(fieldName, 'blur');
                }
            });

            field.addEventListener('change', (e) => {
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackFieldInteraction(fieldName, 'change', e.target.value);
                }
            });
        });
    }

    // Setup form submission validation
    setupFormSubmissionValidation() {
        const form = this.core.getFormElement('form');
        if (!form) return;

        // IMPORTANT: Only prevent submission if there are CRITICAL validation errors
        // Don't interfere with normal form submission for minor issues
        form.addEventListener('submit', (e) => {
            console.log('TaskFormValidation: Form submission event triggered');

            // Only validate critical required fields
            const title = form.querySelector('[name="title"]')?.value?.trim();
            const classification = form.querySelector('[name="classification"]')?.value;

            const criticalErrors = [];

            if (!title) {
                criticalErrors.push('Title is required');
            }

            if (!classification) {
                criticalErrors.push('Classification is required');
            }

            // Only prevent submission for critical errors
            if (criticalErrors.length > 0) {
                console.log('TaskFormValidation: Critical validation errors found:', criticalErrors);
                e.preventDefault();
                this.showCriticalErrors(criticalErrors);

                // Track validation failure
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackEvent('form_validation_failed', {
                        errors: criticalErrors
                    });
                }
            } else {
                console.log('TaskFormValidation: No critical errors, allowing submission');
            }
        });
    }

    // Debounced validation to avoid excessive validation calls
    debounceValidation(fieldName, value) {
        // Clear existing timer
        if (this.debounceTimers.has(fieldName)) {
            clearTimeout(this.debounceTimers.get(fieldName));
        }

        // Set new timer
        const timer = setTimeout(() => {
            this.validateField(fieldName, value);
        }, this.validationDebounceTime);

        this.debounceTimers.set(fieldName, timer);
    }

    // Validate a single field
    validateField(fieldName, value) {
        const rules = this.validationRules[fieldName];
        if (!rules) return true;

        const errors = [];

        // Required field validation
        if (rules.required && (!value || value.toString().trim() === '')) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required`);
        }

        // Skip other validations if field is empty and not required
        if (!value || value.toString().trim() === '') {
            this.clearFieldError(fieldName);
            return errors.length === 0;
        }

        // Type validation
        if (rules.type === 'number') {
            const num = parseFloat(value);
            if (isNaN(num)) {
                errors.push(`${this.getFieldDisplayName(fieldName)} must be a valid number`);
            }
        }

        // Length validation
        if (rules.minLength && value.length < rules.minLength) {
            errors.push(`${this.getFieldDisplayName(fieldName)} must be at least ${rules.minLength} characters long`);
        }

        if (rules.maxLength && value.length > rules.maxLength) {
            errors.push(`${this.getFieldDisplayName(fieldName)} cannot exceed ${rules.maxLength} characters`);
        }

        // Range validation for numbers
        if (rules.type === 'number' && !isNaN(parseFloat(value))) {
            const num = parseFloat(value);
            if (rules.min !== undefined && num < rules.min) {
                errors.push(`${this.getFieldDisplayName(fieldName)} must be at least ${rules.min}`);
            }
            if (rules.max !== undefined && num > rules.max) {
                errors.push(`${this.getFieldDisplayName(fieldName)} cannot exceed ${rules.max}`);
            }
        }

        // Pattern validation
        if (rules.pattern && !rules.pattern.test(value)) {
            errors.push(`${this.getFieldDisplayName(fieldName)} contains invalid characters`);
        }

        // Allowed values validation
        if (rules.allowedValues && !rules.allowedValues.includes(value)) {
            errors.push(`${this.getFieldDisplayName(fieldName)} must be one of the allowed values`);
        }

        // Custom validation
        if (rules.customValidation) {
            const customError = rules.customValidation(value);
            if (customError) {
                errors.push(customError);
            }
        }

        // Update field validation state
        if (errors.length > 0) {
            this.setFieldError(fieldName, errors[0]); // Show first error
            return false;
        } else {
            this.clearFieldError(fieldName);
            return true;
        }
    }

    // Validate entire form
    validateForm() {
        let isValid = true;
        const formElements = this.core.getFormElements();

        // Clear previous errors
        this.validationErrors.clear();

        // Validate each field
        Object.keys(this.validationRules).forEach(fieldName => {
            const field = formElements[fieldName] || document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                const fieldValid = this.validateField(fieldName, field.value);
                if (!fieldValid) {
                    isValid = false;
                }
            }
        });

        // Cross-field validation
        if (isValid) {
            isValid = this.validateCrossFields();
        }

        return isValid;
    }

    // Cross-field validation (relationships between fields) - SIMPLIFIED
    validateCrossFields() {
        // Simplified to avoid blocking form submission
        // Only critical cross-field validation that would prevent data corruption
        return true;  // Allow submission unless there are truly critical issues
    }

    // Set field error state and message
    setFieldError(fieldName, errorMessage) {
        this.validationErrors.set(fieldName, errorMessage);

        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        // Add error class to field
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');

        // Create or update error message element
        let errorElement = field.parentElement.querySelector('.invalid-feedback');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            field.parentElement.appendChild(errorElement);
        }
        errorElement.textContent = errorMessage;
        errorElement.style.display = 'block';
    }

    // Clear field error state
    clearFieldError(fieldName) {
        this.validationErrors.delete(fieldName);

        const field = document.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        // Remove error classes
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');

        // Hide error message
        const errorElement = field.parentElement.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    // Show critical validation errors (simplified)
    showCriticalErrors(errors) {
        const errorMessage = errors.join('\n• ');

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showNotification(
                `Please fix the following:\n• ${errorMessage}`,
                'error',
                5000
            );
        } else {
            alert(`Please fix the following:\n• ${errorMessage}`);
        }

        // Focus on first problematic field
        if (errors.includes('Title is required')) {
            const titleField = document.querySelector('[name="title"]');
            if (titleField) titleField.focus();
        } else if (errors.includes('Classification is required')) {
            const classificationField = document.querySelector('[name="classification"]');
            if (classificationField) classificationField.focus();
        }
    }

    // Show validation summary for form submission errors
    showValidationSummary() {
        if (this.validationErrors.size === 0) return;

        const errors = Array.from(this.validationErrors.entries());
        const errorMessages = errors.map(([field, message]) => `• ${message}`).join('\n');

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showNotification(
                `Please fix the following errors:\n${errorMessages}`,
                'error',
                8000
            );
        } else {
            alert(`Please fix the following errors:\n${errorMessages}`);
        }

        // Focus on first field with error
        const firstErrorField = document.querySelector(`[name="${errors[0][0]}"]`);
        if (firstErrorField) {
            firstErrorField.focus();
        }
    }

    // Get display name for field
    getFieldDisplayName(fieldName) {
        const displayNames = {
            title: 'Title',
            classification: 'Classification',
            description: 'Description',
            est_time: 'Estimated Time',
            date: 'Date',
            category: 'Category'
        };
        return displayNames[fieldName] || fieldName;
    }

    // Sanitize input data
    sanitizeInput(value, fieldName) {
        if (typeof value !== 'string') return value;

        // Basic HTML sanitization
        value = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        value = value.replace(/<[^>]*>/g, '');

        // Field-specific sanitization
        switch (fieldName) {
            case 'title':
            case 'description':
                // Remove excessive whitespace
                value = value.replace(/\s+/g, ' ').trim();
                break;
            case 'est_time':
                // Ensure numeric value
                value = value.replace(/[^\d]/g, '');
                break;
        }

        return value;
    }

    // Process form data before submission
    processFormData(formData) {
        const processed = {};

        Object.entries(formData).forEach(([key, value]) => {
            processed[key] = this.sanitizeInput(value, key);
        });

        // Additional processing
        if (processed.est_time) {
            processed.est_time = parseInt(processed.est_time);
        }

        if (processed.title) {
            processed.title = processed.title.trim();
        }

        if (processed.description) {
            processed.description = processed.description.trim();
        }

        return processed;
    }

    // Get validation state
    getValidationState() {
        return {
            hasErrors: this.validationErrors.size > 0,
            errorCount: this.validationErrors.size,
            errors: Object.fromEntries(this.validationErrors),
            realTimeValidation: this.realTimeValidation
        };
    }

    // Toggle real-time validation
    toggleRealTimeValidation() {
        this.realTimeValidation = !this.realTimeValidation;

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showNotification(
                `Real-time validation ${this.realTimeValidation ? 'enabled' : 'disabled'}`,
                'info',
                3000
            );
        }
    }

    // Clear all validation errors
    clearAllErrors() {
        this.validationErrors.clear();

        // Remove visual error states
        document.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });

        document.querySelectorAll('.invalid-feedback').forEach(error => {
            error.style.display = 'none';
        });
    }

    // Cleanup
    destroy() {
        // Clear timers
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();

        // Clear errors
        this.clearAllErrors();
        this.validationErrors.clear();
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskFormValidation;
} else {
    window.TaskFormValidation = TaskFormValidation;
}
