/**
 * Enhanced Add Task JavaScript Module
 * Handles form validation, template filling, and task creation functionality
 */

class AddTaskManager {
    constructor() {
        console.log('AddTaskManager: Initializing...');
        this.form = null;
        this.submitButton = null;
        this.classificationSelect = null;
        this.categoryInput = null;
        this.dateInput = null;

        this.init();
    }

    init() {
        // Initialize immediately since we're called from DOMContentLoaded
        this.setupElements();
        this.setupEventListeners();
        this.setDefaultDate();
        this.loadRecentTasks();
        console.log('AddTaskManager: Initialization complete');
    }

    setupElements() {
        this.form = document.querySelector('form[method="POST"]');
        this.submitButton = this.form?.querySelector('button[type="submit"]');
        this.classificationSelect = document.getElementById('classification');
        this.categoryInput = document.getElementById('category');
        this.dateInput = document.getElementById('date');
    }

    setupEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (event) => this.handleFormSubmit(event));
            this.setupRealTimeValidation();
        }

        if (this.classificationSelect && this.categoryInput) {
            this.classificationSelect.addEventListener('change', () => this.updateCategoryFromClassification());
        }
    }

    setDefaultDate() {
        if (this.dateInput && !this.dateInput.value) {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            this.dateInput.value = `${year}-${month}-${day}`;
        }
    }

    handleFormSubmit(event) {
        console.log('AddTaskManager: Form submit event triggered');

        // Check form validity first
        if (!this.form.checkValidity()) {
            console.log('AddTaskManager: Form validation failed');
            event.preventDefault();
            event.stopPropagation();
            this.form.classList.add('was-validated');
            return false;
        }

        console.log('AddTaskManager: Form validation passed, allowing submission');
        this.form.classList.add('was-validated');

        // Set processing state with a small delay to show feedback
        // but don't interfere with form submission
        setTimeout(() => {
            if (this.submitButton) {
                this.submitButton.disabled = true;
                this.submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Adding Task...';
            }
        }, 50);

        // Allow form to submit naturally - DO NOT prevent default
        return true;
    }

    resetSubmitButton() {
        if (this.submitButton) {
            this.submitButton.disabled = false;
            this.submitButton.innerHTML = '<i class="bi bi-check-circle"></i> Add Task';
        }
    }

    setupRealTimeValidation() {
        const requiredInputs = this.form.querySelectorAll('[required]');
        requiredInputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    }

    updateCategoryFromClassification() {
        const classification = this.classificationSelect.value;

        // Try enhanced mapping first
        if (window.taskFormManager) {
            window.taskFormManager.updateCategoryFromClassification();
        } else {
            // Fallback category mapping
            const categoryMap = {
                'Planning': 'Adhoc',
                'Offline Processing': 'Adhoc',
                'Execution': 'Adhoc',
                'Business Support Activities': 'Business Support Activities',
                'Operational Project Involvement': 'Adhoc'
            };
            this.categoryInput.value = categoryMap[classification] || '';
        }
    }

    fillTaskForm(taskDataJson) {
        const task = JSON.parse(decodeURIComponent(taskDataJson));

        if (window.taskFormManager) {
            // Use the enhanced template system
            window.taskFormManager.applyTemplate({
                name: task.title,
                data: task
            });
        } else {
            // Fallback to original method
            this.fillTaskFormFallback(task);
        }
    }

    fillTaskFormFallback(task) {
        // Fill form fields
        document.getElementById('title').value = task.title;
        document.getElementById('classification').value = task.classification;
        document.getElementById('category').value = task.category;
        document.getElementById('description').value = task.description;
        document.getElementById('est_time').value = task.est_time;

        // Keep today's date
        this.setDefaultDate();

        // Scroll to form
        document.getElementById('title').scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Focus on title field for easy editing
        setTimeout(() => {
            const titleField = document.getElementById('title');
            titleField.focus();
            titleField.select();
        }, 500);
    }

    loadRecentTasks() {
        // Wait for enhanced script to load, otherwise use fallback
        setTimeout(() => {
            if (!window.taskFormManager) {
                console.log('Enhanced script not loaded, using fallback...');
                this.loadRecentTasksFallback();
            }
        }, 1000);
    }

    loadRecentTasksFallback() {
        fetch('/api/get_recent_tasks')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('quickAddContainer');
                if (data.tasks && data.tasks.length > 0) {
                    this.displayFallbackRecentTasks(data.tasks, container);
                } else {
                    container.innerHTML = '<div class="text-center py-3"><i class="bi bi-inbox text-muted"></i><p class="text-muted mt-2 mb-0">No recent tasks found.</p></div>';
                }
            })
            .catch(error => {
                console.error('Error loading recent tasks:', error);
                document.getElementById('quickAddContainer').innerHTML =
                    '<div class="text-center py-3"><i class="bi bi-exclamation-triangle text-warning"></i><p class="text-muted mt-2 mb-0">Error loading recent tasks.</p></div>';
            });
    }

    displayFallbackRecentTasks(tasks, container) {
        let html = '<div class="list-group list-group-flush">';
        tasks.forEach((task, index) => {
            const shortTitle = task.title.length > 50 ? task.title.substring(0, 50) + '...' : task.title;
            const shortDesc = task.description.length > 80 ? task.description.substring(0, 80) + '...' : task.description;

            html += `
                <div class="list-group-item list-group-item-action py-2" style="cursor: pointer; border-left: 3px solid #0d6efd;" onclick="window.addTaskManager.fillTaskForm('${encodeURIComponent(JSON.stringify(task))}')">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1" style="font-size: 0.9rem; font-weight: 600;">${shortTitle}</h6>
                            <p class="mb-1 text-muted" style="font-size: 0.8rem;">${shortDesc}</p>
                            <div class="d-flex gap-1">
                                <span class="badge bg-secondary" style="font-size: 0.7rem;">${task.classification}</span>
                                <span class="badge bg-info" style="font-size: 0.7rem;">${task.category}</span>
                            </div>
                        </div>
                        <div class="text-end ms-2">
                            <small class="text-muted d-block">${task.est_time} min</small>
                            <i class="bi bi-arrow-right-circle text-primary"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        container.innerHTML = html;
    }

    initializeAdvancedFeatures() {
        // Custom Template Modal Setup
        const customTemplateModal = document.getElementById('customTemplateModal');
        const saveCustomTemplateBtn = document.getElementById('saveCustomTemplate');
        const customTemplateForm = document.getElementById('customTemplateForm');

        // Save Custom Template
        if (saveCustomTemplateBtn) {
            saveCustomTemplateBtn.addEventListener('click', function() {
                if (window.taskFormManager) {
                    window.taskFormManager.handleCreateTemplate();
                }
            });
        }

        // Pre-fill custom template form with current values when modal opens
        if (customTemplateModal) {
            customTemplateModal.addEventListener('shown.bs.modal', function() {
                // Check if we're editing an existing template
                if (window.taskFormManager && window.taskFormManager.editingTemplateData) {
                    const templateData = window.taskFormManager.editingTemplateData;
                    document.getElementById('template-name').value = templateData.name || '';
                    document.getElementById('template-title').value = templateData.title || '';
                    document.getElementById('template-classification').value = templateData.classification || '';
                    document.getElementById('template-description').value = templateData.description || '';
                    document.getElementById('template-time').value = templateData.est_time || '30';
                } else {
                    // Pre-fill with current form values for new template creation
                    const form = document.querySelector('form[method="POST"]');
                    if (form) {
                        const formData = new FormData(form);
                        document.getElementById('template-title').value = formData.get('title') || '';
                        document.getElementById('template-classification').value = formData.get('classification') || '';
                        document.getElementById('template-description').value = formData.get('description') || '';
                        document.getElementById('template-time').value = formData.get('est_time') || '30';
                    }
                }
            });

            // Reset form when modal is hidden
            customTemplateModal.addEventListener('hidden.bs.modal', function() {
                customTemplateForm.reset();
                customTemplateForm.classList.remove('was-validated');

                // Clear editing state and reset modal to create mode
                if (window.taskFormManager) {
                    window.taskFormManager.editingTemplateId = null;
                    window.taskFormManager.editingTemplateData = null;
                    document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-bookmark-plus"></i> Create Custom Template';
                    document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Save Template';
                }
            });
        }

        // Initialize bulk entry features
        this.initializeBulkEntry();
    }

    initializeBulkEntry() {
        // Bulk Entry Modal Setup
        const bulkEntryModal = document.getElementById('bulkEntryModal');
        let bulkTaskCounter = 0;

        if (bulkEntryModal) {
            bulkEntryModal.addEventListener('shown.bs.modal', () => {
                this.setupBulkEntryModal();
            });

            bulkEntryModal.addEventListener('hidden.bs.modal', function() {
                // Reset bulk entry when modal is closed
                const bulkTasksBody = document.getElementById('bulkTasksBody');
                if (bulkTasksBody) {
                    bulkTasksBody.innerHTML = '';
                    bulkTaskCounter = 0;
                }
            });
        }
    }

    setupBulkEntryModal() {
        // Implementation for bulk entry modal setup
        // This would contain the bulk entry functionality
        console.log('Setting up bulk entry modal...');
    }
}

// Make AddTaskManager available globally
window.AddTaskManager = AddTaskManager;

// Global function for backward compatibility
function fillTaskForm(taskDataJson) {
    if (window.addTaskManager) {
        window.addTaskManager.fillTaskForm(taskDataJson);
    }
}
