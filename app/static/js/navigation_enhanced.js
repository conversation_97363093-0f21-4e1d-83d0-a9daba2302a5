/**
 * AdhocLog - Enhanced Navigation & Responsive JavaScript
 *
 * This file provides enhanced navigation behavior, responsive features,
 * and improved user experience for the AdhocLog application.
 */

// Enhanced Navigation Manager
class NavigationManager {
    constructor() {
        this.navbar = document.querySelector('.navbar');
        this.navbarCollapse = document.querySelector('.navbar-collapse');
        this.lastScrollTop = 0;
        this.scrollThreshold = 50;
        this.isScrolling = false;

        this.init();
    }

    init() {
        this.setupScrollBehavior();
        this.setupMobileMenu();
        this.setupDropdowns();
        this.setupKeyboardNavigation();
        this.setupActiveStates();
        this.setupTooltips();
        this.setupLoadingStates();
    }

    setupScrollBehavior() {
        let ticking = false;

        const updateNavbar = () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

            if (scrollTop > this.scrollThreshold) {
                this.navbar.classList.add('navbar-scrolled');
            } else {
                this.navbar.classList.remove('navbar-scrolled');
            }

            // Hide navbar on scroll down, show on scroll up (mobile)
            if (window.innerWidth <= 991) {
                if (scrollTop > this.lastScrollTop && scrollTop > 100) {
                    this.navbar.style.transform = 'translateY(-100%)';
                } else {
                    this.navbar.style.transform = 'translateY(0)';
                }
            }

            this.lastScrollTop = scrollTop;
            ticking = false;
        };

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateNavbar);
                ticking = true;
            }
        });
    }

    setupMobileMenu() {
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link:not(.dropdown-toggle)');

        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (this.navbarCollapse && this.navbarCollapse.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(this.navbarCollapse, {
                        toggle: false
                    });
                    bsCollapse.hide();
                }
            });
        });

        // Handle outside clicks
        document.addEventListener('click', (e) => {
            if (window.innerWidth <= 991 &&
                this.navbarCollapse &&
                this.navbarCollapse.classList.contains('show') &&
                !this.navbar.contains(e.target)) {

                const bsCollapse = new bootstrap.Collapse(this.navbarCollapse, {
                    toggle: false
                });
                bsCollapse.hide();
            }
        });
    }

    setupDropdowns() {
        const dropdownElements = document.querySelectorAll('.dropdown');

        dropdownElements.forEach(dropdown => {
            const dropdownMenu = dropdown.querySelector('.dropdown-menu');

            dropdown.addEventListener('show.bs.dropdown', () => {
                dropdownMenu.style.animation = 'fadeInUp 0.2s ease';
            });

            dropdown.addEventListener('hide.bs.dropdown', () => {
                dropdownMenu.style.animation = 'fadeOut 0.15s ease';
            });
        });
    }

    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Skip if user is typing in form fields
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.tagName === 'SELECT') {
                return;
            }

            // Navigation shortcuts
            if (e.altKey) {
                switch (e.key.toLowerCase()) {
                    case 'h':
                        e.preventDefault();
                        this.navigateToPage('docs');
                        break;
                    case 'a':
                        e.preventDefault();
                        this.navigateToPage('add_task');
                        break;
                    case 'd':
                        e.preventDefault();
                        this.navigateToPage('dashboard');
                        break;
                    case 't':
                        e.preventDefault();
                        this.navigateToPage('task_list');
                        break;
                    case 's':
                        e.preventDefault();
                        this.navigateToPage('statistics');
                        break;
                }
            }

            // Search shortcut
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                this.focusSearch();
            }
        });
    }

    navigateToPage(page) {
        const links = {
            'docs': 'a[href*="docs"]',
            'add_task': 'a[href*="add_task"]',
            'dashboard': 'a[href*="dashboard"], a[href="/"]',
            'task_list': 'a[href*="task_list"]',
            'statistics': 'a[href*="statistics"]'
        };

        const selector = links[page];
        if (selector) {
            const link = document.querySelector(selector);
            if (link) {
                link.click();
            }
        }
    }

    focusSearch() {
        const searchInput = document.querySelector('input[type="search"], input[name="search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }

    setupActiveStates() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (currentPath === href ||
                        (href !== '/' && currentPath.includes(href)))) {
                link.classList.add('active');
            }
        });
    }

    setupTooltips() {
        const tooltipTriggerList = [].slice.call(
            document.querySelectorAll('[data-bs-toggle="tooltip"]')
        );

        tooltipTriggerList.map(tooltipTriggerEl => {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                delay: { show: 500, hide: 100 }
            });
        });
    }

    setupLoadingStates() {
        const forms = document.querySelectorAll('form');

        forms.forEach(form => {
            // Skip forms that have custom submit handling
            if (form.hasAttribute('data-custom-submit')) {
                console.log('NavigationManager: Skipping form with custom submit handling');
                return;
            }

            form.addEventListener('submit', (e) => {
                const submitBtn = form.querySelector('button[type="submit"], .btn-submit');
                if (submitBtn && form.checkValidity()) {
                    this.setButtonLoading(submitBtn);
                }
            });
        });

        // Handle navigation loading states
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.href && !link.href.includes('#') && !link.target) {
                    this.setLinkLoading(link);
                }
            });
        });
    }

    setButtonLoading(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Processing...';
        button.disabled = true;

        // Reset after 30 seconds as fallback
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 30000);
    }

    setLinkLoading(link) {
        const originalHTML = link.innerHTML;
        link.style.opacity = '0.7';
        link.style.pointerEvents = 'none';

        // Reset after page load or timeout
        setTimeout(() => {
            link.innerHTML = originalHTML;
            link.style.opacity = '';
            link.style.pointerEvents = '';
        }, 2000);
    }
}

// Enhanced Card Manager
class CardManager {
    constructor() {
        this.cards = document.querySelectorAll('.card');
        this.init();
    }

    init() {
        this.setupHoverEffects();
        this.setupClickEffects();
        this.setupIntersectionObserver();
    }

    setupHoverEffects() {
        this.cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                if (!card.classList.contains('no-hover')) {
                    this.addHoverEffect(card);
                }
            });

            card.addEventListener('mouseleave', () => {
                this.removeHoverEffect(card);
            });
        });
    }

    addHoverEffect(card) {
        card.style.transform = 'translateY(-6px) scale(1.02)';
        card.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
    }

    removeHoverEffect(card) {
        card.style.transform = 'translateY(0) scale(1)';
        card.style.boxShadow = '';
    }

    setupClickEffects() {
        const taskCards = document.querySelectorAll('.task-card');

        taskCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Only trigger if not clicking on buttons or links
                if (!e.target.closest('a, button')) {
                    const editLink = card.querySelector('a[href*="edit_task"]');
                    if (editLink) {
                        editLink.click();
                    }
                }
            });
        });
    }

    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            this.cards.forEach(card => {
                observer.observe(card);
            });
        }
    }
}

// Responsive Utilities
class ResponsiveUtils {
    constructor() {
        this.breakpoints = {
            xs: 576,
            sm: 768,
            md: 992,
            lg: 1200,
            xl: 1400
        };

        this.init();
    }

    init() {
        this.setupResizeHandler();
        this.optimizeForTouch();
        this.handleOrientationChange();
    }

    setupResizeHandler() {
        let resizeTimer;

        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
    }

    handleResize() {
        const width = window.innerWidth;

        // Adjust table responsiveness
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            const wrapper = table.closest('.table-responsive');
            if (wrapper) {
                if (width < this.breakpoints.md) {
                    wrapper.style.overflowX = 'auto';
                } else {
                    wrapper.style.overflowX = '';
                }
            }
        });

        // Adjust card layouts
        this.adjustCardLayouts(width);
    }

    adjustCardLayouts(width) {
        const cardContainers = document.querySelectorAll('.row .col-md-6, .row .col-lg-4');

        cardContainers.forEach(container => {
            if (width < this.breakpoints.sm) {
                container.style.marginBottom = '1rem';
            } else if (width < this.breakpoints.md) {
                container.style.marginBottom = '1.5rem';
            } else {
                container.style.marginBottom = '';
            }
        });
    }

    optimizeForTouch() {
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');

            // Increase button sizes on touch devices
            const buttons = document.querySelectorAll('.btn-sm');
            buttons.forEach(btn => {
                btn.style.padding = '0.5rem 1rem';
                btn.style.fontSize = '0.9rem';
            });
        }
    }

    handleOrientationChange() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleResize();
            }, 100);
        });
    }

    getCurrentBreakpoint() {
        const width = window.innerWidth;

        if (width >= this.breakpoints.xl) return 'xl';
        if (width >= this.breakpoints.lg) return 'lg';
        if (width >= this.breakpoints.md) return 'md';
        if (width >= this.breakpoints.sm) return 'sm';
        return 'xs';
    }
}

// Performance Monitor
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.init();
    }

    init() {
        this.measurePageLoad();
        this.monitorInteractions();
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            this.metrics.pageLoad = {
                domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                loadComplete: perfData.loadEventEnd - perfData.loadEventStart,
                totalTime: perfData.loadEventEnd - perfData.fetchStart
            };
        });
    }

    monitorInteractions() {
        // Monitor button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('button, .btn')) {
                const startTime = performance.now();

                setTimeout(() => {
                    const endTime = performance.now();
                    this.logInteraction('button_click', endTime - startTime);
                }, 0);
            }
        });
    }

    logInteraction(type, duration) {
        if (!this.metrics.interactions) {
            this.metrics.interactions = [];
        }

        this.metrics.interactions.push({
            type,
            duration,
            timestamp: Date.now()
        });
    }

    getMetrics() {
        return this.metrics;
    }
}

// Initialize all managers when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize core managers
    window.navigationManager = new NavigationManager();
    window.cardManager = new CardManager();
    window.responsiveUtils = new ResponsiveUtils();

    // Initialize performance monitoring in production
    if (window.location.hostname !== 'localhost') {
        window.performanceMonitor = new PerformanceMonitor();
    }

    // Add global keyboard shortcuts help
    const showKeyboardShortcuts = () => {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Keyboard Shortcuts</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-12">
                                <h6>Navigation</h6>
                                <ul class="list-unstyled">
                                    <li><kbd>Alt + D</kbd> Dashboard</li>
                                    <li><kbd>Alt + A</kbd> Add Task</li>
                                    <li><kbd>Alt + T</kbd> Task List</li>
                                    <li><kbd>Alt + S</kbd> Statistics</li>
                                    <li><kbd>Alt + H</kbd> Help</li>
                                </ul>
                                <h6>General</h6>
                                <ul class="list-unstyled">
                                    <li><kbd>Ctrl + K</kbd> Focus Search</li>
                                    <li><kbd>?</kbd> Show Shortcuts</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    };

    // Show shortcuts on ? key
    document.addEventListener('keydown', (e) => {
        if (e.key === '?' && !e.target.matches('input, textarea, select')) {
            e.preventDefault();
            showKeyboardShortcuts();
        }
    });

    // Add animation classes
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .touch-device .btn:hover {
            transform: none !important;
        }

        .touch-device .card:hover {
            transform: none !important;
        }
    `;
    document.head.appendChild(style);
});

// Export for use in other scripts
window.AdhocLogNavigation = {
    NavigationManager,
    CardManager,
    ResponsiveUtils,
    PerformanceMonitor
};
