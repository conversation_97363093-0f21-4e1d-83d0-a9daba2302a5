/**
 * Task Bulk Operations Module
 * Handles bulk task entry and management functionality
 */

class TaskBulkOperations {
    constructor() {
        this.core = null;
        this.bulkEntryModal = null;
        this.bulkTasks = [];
        this.currentEditIndex = -1;
        this.bulkTemplates = new Map();

        this.initializeBulkTemplates();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.setupExistingBulkEntryModal();
            console.log('TaskBulkOperations initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskBulkOperations:', error);
        }
    }

    // Initialize bulk operation templates
    initializeBulkTemplates() {
        this.bulkTemplates.set('daily_standup', {
            name: 'Daily Standup Tasks',
            tasks: [
                { title: 'Review yesterday\'s progress', classification: 'Planning', est_time: 10 },
                { title: 'Plan today\'s priorities', classification: 'Planning', est_time: 15 },
                { title: 'Check team updates', classification: 'Business Support Activities', est_time: 5 },
                { title: 'Update project status', classification: 'Business Support Activities', est_time: 10 }
            ]
        });

        this.bulkTemplates.set('project_kickoff', {
            name: 'Project Kickoff Tasks',
            tasks: [
                { title: 'Review project requirements', classification: 'Planning', est_time: 30 },
                { title: 'Set up project workspace', classification: 'Execution', est_time: 20 },
                { title: 'Schedule team meetings', classification: 'Planning', est_time: 15 },
                { title: 'Create project timeline', classification: 'Planning', est_time: 45 },
                { title: 'Identify key stakeholders', classification: 'Planning', est_time: 20 }
            ]
        });

        this.bulkTemplates.set('weekly_review', {
            name: 'Weekly Review Tasks',
            tasks: [
                { title: 'Review completed tasks', classification: 'Planning', est_time: 20 },
                { title: 'Analyze time tracking data', classification: 'Offline Processing', est_time: 15 },
                { title: 'Plan next week priorities', classification: 'Planning', est_time: 30 },
                { title: 'Update project status reports', classification: 'Business Support Activities', est_time: 25 }
            ]
        });
    }

    // Setup existing bulk entry modal from HTML template
    setupExistingBulkEntryModal() {
        try {
            // Use the existing modal from the HTML template
            this.bulkEntryModal = document.getElementById('bulkEntryModal');
            if (this.bulkEntryModal) {
                this.setupBulkModalEventListeners();
                this.setupBulkTemplateButtons();

                // Set up the existing bulk entry button click handler
                const existingBulkBtn = document.querySelector('[data-bs-target="#bulkEntryModal"]');
                if (existingBulkBtn) {
                    existingBulkBtn.addEventListener('click', () => {
                        this.showBulkEntryModal();
                    });
                }

                // Set up modal shown event to ensure default row is added
                this.bulkEntryModal.addEventListener('shown.bs.modal', () => {
                    if (this.bulkTasks.length === 0) {
                        this.addBulkTask();
                    }
                });

                // Set up modal hidden event to clear tasks
                this.bulkEntryModal.addEventListener('hidden.bs.modal', () => {
                    this.bulkTasks = [];
                    this.renderBulkTasksList();
                    this.updateBulkTaskCount();
                });
            }
        } catch (error) {
            console.error('Error setting up existing bulk entry modal:', error);
        }
    }



    // Setup event listeners for bulk modal
    setupBulkModalEventListeners() {
        // Add task button (using HTML template ID)
        const addRowBtn = document.getElementById('addBulkRow');
        if (addRowBtn) {
            addRowBtn.addEventListener('click', () => {
                this.addBulkTask();
            });
        }

        // Clear all button (using HTML template ID)
        const clearAllBtn = document.getElementById('clearAllRows');
        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                this.clearAllBulkTasks();
            });
        }

        // Create tasks button (using HTML template ID)
        const submitBtn = document.getElementById('submitBulkTasks');
        if (submitBtn) {
            submitBtn.addEventListener('click', () => {
                this.createBulkTasks();
            });
        }

        // Copy first row button
        const copyBtn = document.getElementById('copyFirstRow');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                this.copyFirstRowToAll();
            });
        }
    }

    // Setup bulk template buttons
    setupBulkTemplateButtons() {
        const container = document.getElementById('bulkTemplateButtons');
        if (!container) return;

        container.innerHTML = '';

        this.bulkTemplates.forEach((template, key) => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn btn-outline-info btn-sm';
            button.textContent = template.name;
            button.addEventListener('click', () => {
                this.applyBulkTemplate(key);
            });
            container.appendChild(button);
        });
    }

    // Show bulk entry modal
    showBulkEntryModal() {
        if (this.bulkEntryModal) {
            const modal = new bootstrap.Modal(this.bulkEntryModal);
            modal.show();

            // Set default date to today
            const dateField = document.getElementById('bulkDate');
            if (dateField && !dateField.value) {
                dateField.value = new Date().toISOString().split('T')[0];
            }

            // Add default row if no tasks exist
            if (this.bulkTasks.length === 0) {
                this.addBulkTask();
            }

            // Track modal opening
            if (this.core.analyticsTracker) {
                this.core.analyticsTracker.trackEvent('bulk_entry_modal_opened');
            }
        }
    }

    // Add a new bulk task
    addBulkTask(taskData = null) {
        const task = taskData || {
            title: '',
            classification: '',
            description: '',
            est_time: '',
            date: document.getElementById('bulkDate')?.value || new Date().toISOString().split('T')[0]
        };

        this.bulkTasks.push(task);
        this.renderBulkTasksList();
        this.updateBulkTaskCount();

        // Focus on the new task's title field
        setTimeout(() => {
            const newTaskIndex = this.bulkTasks.length - 1;
            const titleField = document.querySelector(`[data-bulk-index="${newTaskIndex}"] .bulk-task-title`);
            if (titleField) {
                titleField.focus();
            }
        }, 100);
    }

    // Remove a bulk task
    removeBulkTask(index) {
        if (index >= 0 && index < this.bulkTasks.length) {
            this.bulkTasks.splice(index, 1);
            this.renderBulkTasksList();
            this.updateBulkTaskCount();
        }
    }

    // Render the bulk tasks list
    renderBulkTasksList() {
        const container = document.getElementById('bulkTasksBody');
        const emptyMessage = document.getElementById('emptyBulkMessage');

        if (!container) return;

        if (this.bulkTasks.length === 0) {
            container.innerHTML = '<tr><td colspan="5" class="text-muted text-center py-4">No tasks added yet. Click "Add Row" to get started.</td></tr>';
            return;
        }

        const tasksHtml = this.bulkTasks.map((task, index) => `
            <tr data-bulk-index="${index}">
                <td>
                    <input type="text" class="form-control form-control-sm bulk-task-title"
                           value="${task.title}" placeholder="Enter task title">
                </td>
                <td>
                    <select class="form-select form-select-sm bulk-task-classification">
                        <option value="">Select...</option>
                        <option value="Planning" ${task.classification === 'Planning' ? 'selected' : ''}>Planning</option>
                        <option value="Offline Processing" ${task.classification === 'Offline Processing' ? 'selected' : ''}>Offline Processing</option>
                        <option value="Execution" ${task.classification === 'Execution' ? 'selected' : ''}>Execution</option>
                        <option value="Business Support Activities" ${task.classification === 'Business Support Activities' ? 'selected' : ''}>Business Support Activities</option>
                        <option value="Operational Project Involvement" ${task.classification === 'Operational Project Involvement' ? 'selected' : ''}>Operational Project Involvement</option>
                    </select>
                </td>
                <td>
                    <textarea class="form-control form-control-sm bulk-task-description"
                              rows="2" placeholder="Enter task description">${task.description}</textarea>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm bulk-task-time"
                           value="${task.est_time}" min="1" max="480" placeholder="30">
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger bulk-remove-btn"
                            title="Remove task">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        container.innerHTML = tasksHtml;

        // Add event listeners to the new elements
        this.setupBulkTaskEventListeners();
    }

    // Setup event listeners for bulk task items
    setupBulkTaskEventListeners() {
        // Remove buttons
        document.querySelectorAll('.bulk-remove-btn').forEach((btn, index) => {
            btn.addEventListener('click', () => {
                this.removeBulkTask(index);
            });
        });

        // Input change handlers
        document.querySelectorAll('.bulk-task-item').forEach((item, index) => {
            const titleField = item.querySelector('.bulk-task-title');
            const classificationField = item.querySelector('.bulk-task-classification');
            const timeField = item.querySelector('.bulk-task-time');
            const descriptionField = item.querySelector('.bulk-task-description');

            if (titleField) {
                titleField.addEventListener('input', (e) => {
                    this.bulkTasks[index].title = e.target.value;
                });
            }

            if (classificationField) {
                classificationField.addEventListener('change', (e) => {
                    this.bulkTasks[index].classification = e.target.value;
                });
            }

            if (timeField) {
                timeField.addEventListener('input', (e) => {
                    this.bulkTasks[index].est_time = e.target.value;
                });
            }

            if (descriptionField) {
                descriptionField.addEventListener('input', (e) => {
                    this.bulkTasks[index].description = e.target.value;
                });
            }
        });
    }

    // Apply bulk template
    applyBulkTemplate(templateKey) {
        const template = this.bulkTemplates.get(templateKey);
        if (!template) return;

        // Clear existing tasks
        this.bulkTasks = [];

        // Add template tasks
        template.tasks.forEach(taskData => {
            this.addBulkTask({
                ...taskData,
                description: taskData.description || '',
                date: document.getElementById('bulkDate')?.value || new Date().toISOString().split('T')[0]
            });
        });

        // Track template usage
        if (this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('bulk_template_applied', {
                template_key: templateKey,
                template_name: template.name,
                task_count: template.tasks.length
            });
        }

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showSuccess(`Applied template: ${template.name}`, 3000);
        }
    }

    // Apply classification to all tasks
    applyBulkClassification() {
        const classification = document.getElementById('bulkClassification').value;
        if (!classification) return;

        this.bulkTasks.forEach(task => {
            task.classification = classification;
        });

        this.renderBulkTasksList();

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showSuccess(`Applied classification "${classification}" to all tasks`, 3000);
        }
    }

    // Apply date to all tasks
    applyBulkDate() {
        const date = document.getElementById('bulkDate').value;
        if (!date) return;

        this.bulkTasks.forEach(task => {
            task.date = date;
        });

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showSuccess(`Applied date ${date} to all tasks`, 3000);
        }
    }

    // Clear all bulk tasks
    clearAllBulkTasks() {
        if (this.bulkTasks.length === 0) return;

        if (this.core.uiNotifications) {
            this.core.uiNotifications.showConfirmation(
                'Are you sure you want to clear all tasks?',
                'Clear All Tasks'
            ).then(confirmed => {
                if (confirmed) {
                    this.bulkTasks = [];
                    this.renderBulkTasksList();
                    this.updateBulkTaskCount();
                }
            });
        } else {
            if (confirm('Are you sure you want to clear all tasks?')) {
                this.bulkTasks = [];
                this.renderBulkTasksList();
                this.updateBulkTaskCount();
            }
        }
    }

    // Create all bulk tasks
    async createBulkTasks() {
        if (this.bulkTasks.length === 0) {
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showWarning('No tasks to create');
            }
            return;
        }

        // Validate tasks
        const validationErrors = this.validateBulkTasks();
        if (validationErrors.length > 0) {
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showError(
                    `Please fix the following errors:\n${validationErrors.join('\n')}`,
                    8000
                );
            }
            return;
        }

        // Show loading
        let loadingId = null;
        if (this.core.uiNotifications) {
            loadingId = this.core.uiNotifications.showLoading(`Creating ${this.bulkTasks.length} tasks...`);
        }

        try {
            const results = await this.submitBulkTasks();

            if (loadingId && this.core.uiNotifications) {
                this.core.uiNotifications.hideLoading(loadingId);
            }

            if (results.success) {
                if (this.core.uiNotifications) {
                    this.core.uiNotifications.showSuccess(
                        `Successfully created ${results.created} tasks!`,
                        5000
                    );
                }

                // Close modal
                const modal = bootstrap.Modal.getInstance(this.bulkEntryModal);
                if (modal) {
                    modal.hide();
                }

                // Clear tasks
                this.bulkTasks = [];

                // Track success
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackEvent('bulk_tasks_created', {
                        task_count: results.created,
                        success: true
                    });
                }
            } else {
                if (this.core.uiNotifications) {
                    this.core.uiNotifications.showError(
                        `Failed to create tasks: ${results.error}`,
                        8000
                    );
                }
            }
        } catch (error) {
            if (loadingId && this.core.uiNotifications) {
                this.core.uiNotifications.hideLoading(loadingId);
            }

            console.error('Error creating bulk tasks:', error);
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showError('An error occurred while creating tasks', 8000);
            }
        }
    }

    // Validate bulk tasks
    validateBulkTasks() {
        const errors = [];

        this.bulkTasks.forEach((task, index) => {
            const taskNum = index + 1;

            if (!task.title || task.title.trim().length < 3) {
                errors.push(`Task ${taskNum}: Title must be at least 3 characters`);
            }

            if (!task.classification) {
                errors.push(`Task ${taskNum}: Classification is required`);
            }

            if (!task.est_time || parseInt(task.est_time) < 1) {
                errors.push(`Task ${taskNum}: Estimated time must be at least 1 minute`);
            }

            if (!task.description || task.description.trim().length < 10) {
                errors.push(`Task ${taskNum}: Description must be at least 10 characters`);
            }
        });

        return errors;
    }

    // Submit bulk tasks to server
    async submitBulkTasks() {
        const response = await fetch('/api/tasks/bulk-create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ tasks: this.bulkTasks })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }

    // Update bulk task count display
    updateBulkTaskCount() {
        const countElement = document.getElementById('bulkTaskCount');
        if (countElement) {
            countElement.textContent = this.bulkTasks.length;
        }

        const createBtn = document.getElementById('createBulkTasks');
        if (createBtn) {
            createBtn.disabled = this.bulkTasks.length === 0;
        }
    }

    // Get bulk operation statistics
    getBulkStats() {
        return {
            current_tasks: this.bulkTasks.length,
            available_templates: this.bulkTemplates.size,
            template_names: Array.from(this.bulkTemplates.values()).map(t => t.name)
        };
    }

    // Cleanup
    destroy() {
        this.bulkTasks = [];
        this.currentEditIndex = -1;

        if (this.bulkEntryModal && this.bulkEntryModal.parentNode) {
            this.bulkEntryModal.parentNode.removeChild(this.bulkEntryModal);
        }
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskBulkOperations;
} else {
    window.TaskBulkOperations = TaskBulkOperations;
}
