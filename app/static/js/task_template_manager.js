/**
 * Task Template Manager Module
 * Handles all template-related functionality including default and custom templates
 */

class TaskTemplateManager {
    constructor() {
        this.core = null;
        this.templates = [];
        this.customTemplates = [];
        this.editingTemplateId = null;
        this.editingTemplateData = null;
        this.templateToDelete = null;

        this.initializeDefaultTemplates();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.setupTemplateButtons();
            // Wait for Bootstrap to be available before setting up modals
            this.waitForBootstrap().then(() => {
                this.setupCustomTemplateModal();
            });
            await this.loadCustomTemplates();
            console.log('TaskTemplateManager initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskTemplateManager:', error);
        }
    }

    // Wait for Bootstrap to be available and DOM to be ready
    waitForBootstrap() {
        return new Promise((resolve) => {
            const checkReady = () => {
                if (typeof bootstrap !== 'undefined' && document.getElementById('manageTemplatesModal')) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            checkReady();
        });
    }

    // Initialize default templates
    initializeDefaultTemplates() {
        this.templates = [
            {
                id: 'meeting-prep',
                name: 'Meeting Preparation',
                icon: 'bi-calendar-event',
                data: {
                    title: 'Prepare for [Meeting Name]',
                    classification: 'Planning',
                    description: 'Review agenda, prepare materials, and gather necessary documents for the meeting.',
                    est_time: 30
                }
            },
            {
                id: 'email-response',
                name: 'Email Response',
                icon: 'bi-envelope',
                data: {
                    title: 'Respond to [Sender Name] email',
                    classification: 'Business Support Activities',
                    description: 'Review and respond to email regarding [topic].',
                    est_time: 15
                }
            },
            {
                id: 'document-review',
                name: 'Document Review',
                icon: 'bi-file-text',
                data: {
                    title: 'Review [Document Name]',
                    classification: 'Offline Processing',
                    description: 'Thoroughly review document for accuracy, completeness, and compliance.',
                    est_time: 45
                }
            },
            {
                id: 'data-analysis',
                name: 'Data Analysis',
                icon: 'bi-graph-up',
                data: {
                    title: 'Analyze [Dataset/Report Name]',
                    classification: 'Offline Processing',
                    description: 'Perform data analysis to identify trends, patterns, and insights.',
                    est_time: 60
                }
            },
            {
                id: 'project-update',
                name: 'Project Update',
                icon: 'bi-clipboard-check',
                data: {
                    title: 'Update [Project Name] status',
                    classification: 'Operational Project Involvement',
                    description: 'Update project status, review progress, and identify next steps.',
                    est_time: 20
                }
            },
            {
                id: 'training-session',
                name: 'Training Session',
                icon: 'bi-mortarboard',
                data: {
                    title: 'Attend [Training Topic] session',
                    classification: 'Business Support Activities',
                    description: 'Participate in training session and take notes on key learnings.',
                    est_time: 90
                }
            },
            {
                id: 'system-maintenance',
                name: 'System Maintenance',
                icon: 'bi-gear',
                data: {
                    title: 'Perform [System Name] maintenance',
                    classification: 'Execution',
                    description: 'Execute routine maintenance tasks to ensure system reliability.',
                    est_time: 40
                }
            },
            {
                id: 'client-follow-up',
                name: 'Client Follow-up',
                icon: 'bi-telephone',
                data: {
                    title: 'Follow up with [Client Name]',
                    classification: 'Business Support Activities',
                    description: 'Contact client to check on progress and address any concerns.',
                    est_time: 25
                }
            }
        ];
    }

    // Setup template buttons in the UI
    setupTemplateButtons() {
        const templateContainer = document.getElementById('template-buttons-container');
        if (!templateContainer) return;

        // Clear existing content
        templateContainer.innerHTML = '';

        // Add default templates
        this.templates.forEach(template => {
            const button = this.createTemplateButton(template);
            templateContainer.appendChild(button);
        });

        // Add custom templates if they exist
        this.updateTemplateButtons();
    }

    // Create a template button element
    createTemplateButton(template) {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-outline-primary btn-sm template-btn mb-2';
        button.setAttribute('data-template-id', template.id);

        // Determine badge based on template type
        const badgeHtml = template.isCustom
            ? '<span class="badge bg-info text-light ms-2" style="font-size: 0.65em;">Custom</span>'
            : '<span class="badge bg-primary text-light ms-2" style="font-size: 0.65em;">Default</span>';

        button.innerHTML = `
            <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-center flex-grow-1 me-2" style="min-width: 0;">
                    <i class="${template.icon} me-2 flex-shrink-0"></i>
                    <span class="fw-medium text-truncate">${template.name}</span>
                    ${badgeHtml}
                </div>
                <small class="text-muted flex-shrink-0">${template.data.est_time}min</small>
            </div>
        `;

        button.addEventListener('click', () => {
            this.applyTemplate(template);
        });

        return button;
    }

    // Apply a template to the form
    applyTemplate(template) {
        if (!this.core) return;

        // Track template usage if analytics is available
        if (this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('template_applied', {
                template_id: template.id,
                template_name: template.name,
                is_custom: template.isCustom || false
            });
        }

        // Apply template data to form (this will show its own notification)
        this.core.applyTemplateData(template.data, template.name);
    }

    // Load custom templates from server
    async loadCustomTemplates() {
        try {
            const response = await fetch('/api/templates');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates = data.templates;
                    this.updateTemplateButtons();
                }
            }
        } catch (error) {
            console.error('Error loading custom templates:', error);
        }
    }

    // Update template buttons with both default and custom templates
    updateTemplateButtons() {
        const templateContainer = document.getElementById('template-buttons-container');
        if (!templateContainer) return;

        // Clear existing content
        templateContainer.innerHTML = '';

        // Add default templates
        this.templates.forEach(template => {
            const button = this.createTemplateButton(template);
            templateContainer.appendChild(button);
        });

        // Add custom templates
        this.customTemplates.forEach(template => {
            const customTemplate = {
                id: `custom-${template.id}`,
                name: template.name,
                icon: 'bi-star',
                data: {
                    title: template.title,
                    classification: template.classification,
                    description: template.description,
                    est_time: template.est_time
                },
                isCustom: true,
                customId: template.id
            };

            const button = this.createTemplateButton(customTemplate);

            // Add delete option for custom templates
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'btn btn-sm btn-outline-danger position-absolute top-0 end-0 translate-middle';
            deleteBtn.innerHTML = '<i class="bi bi-x"></i>';
            deleteBtn.onclick = (e) => {
                e.stopPropagation();
                this.deleteCustomTemplate(template.id);
            };
            deleteBtn.style.fontSize = '10px';
            deleteBtn.style.padding = '2px 4px';

            button.style.position = 'relative';
            button.appendChild(deleteBtn);
            templateContainer.appendChild(button);
        });
    }

    // Setup custom template modal handlers
    setupCustomTemplateModal() {
        // Set up form submission handler for custom template modal
        const saveButton = document.getElementById('saveCustomTemplate');
        if (saveButton) {
            saveButton.addEventListener('click', () => {
                this.handleCreateTemplate();
            });
        }

        // Set up the manage templates modal to load templates when opened
        const manageModal = document.getElementById('manageTemplatesModal');
        if (manageModal) {
            manageModal.addEventListener('shown.bs.modal', () => {
                this.loadTemplatesForManagement();
            });
        } else {
            console.warn('Manage templates modal not found in DOM');
        }

        // Also set up the manage templates button click handler as a fallback
        const manageButton = document.querySelector('[data-bs-target="#manageTemplatesModal"]');
        if (manageButton) {
            manageButton.addEventListener('click', () => {
                // Ensure the modal opens and loads templates
                setTimeout(() => {
                    const modal = document.getElementById('manageTemplatesModal');
                    if (modal && modal.classList.contains('show')) {
                        this.loadTemplatesForManagement();
                    }
                }, 100);
            });
        } else {
            console.warn('Manage templates button not found in DOM');
        }

        // Set up delete confirmation modal
        const confirmDeleteButton = document.getElementById('confirmDeleteTemplate');
        if (confirmDeleteButton) {
            confirmDeleteButton.addEventListener('click', () => {
                if (this.templateToDelete) {
                    this.deleteCustomTemplate(this.templateToDelete);
                    this.templateToDelete = null;

                    // Close the delete modal
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteTemplateModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }
                }
            });
        }

        // Reset form when custom template modal is closed
        const customModal = document.getElementById('customTemplateModal');
        if (customModal) {
            customModal.addEventListener('hidden.bs.modal', () => {
                const form = document.getElementById('customTemplateForm');
                if (form) {
                    form.reset();
                    form.classList.remove('was-validated');
                }
                // Reset editing state
                this.editingTemplateId = null;
                this.editingTemplateData = null;
                document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-bookmark-plus"></i> Create Custom Template';
                document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Save Template';
            });

            // Populate form when modal is shown for editing
            customModal.addEventListener('shown.bs.modal', () => {
                if (this.editingTemplateData) {
                    const form = document.getElementById('customTemplateForm');
                    if (form) {
                        form.querySelector('[name="name"]').value = this.editingTemplateData.name || '';
                        form.querySelector('[name="title"]').value = this.editingTemplateData.title || '';
                        form.querySelector('[name="classification"]').value = this.editingTemplateData.classification || '';
                        form.querySelector('[name="description"]').value = this.editingTemplateData.description || '';
                        form.querySelector('[name="est_time"]').value = this.editingTemplateData.est_time || '';
                    }
                }
            });
        }
    }

    // Create a new custom template
    async createCustomTemplate(templateData) {
        try {
            const response = await fetch('/api/templates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(templateData)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates.push(data.template);
                    this.updateTemplateButtons();

                    if (this.core.uiNotifications) {
                        this.core.uiNotifications.showNotification('Custom template created successfully!', 'success');
                    }
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error creating custom template:', error);
            return false;
        }
    }

    // Update an existing custom template
    async updateCustomTemplate(templateData) {
        try {
            const response = await fetch(`/api/templates/${templateData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(templateData)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    // Update the template in the local array
                    const index = this.customTemplates.findIndex(t => t.id === templateData.id);
                    if (index !== -1) {
                        this.customTemplates[index] = data.template;
                    }
                    this.updateTemplateButtons();

                    if (this.core.uiNotifications) {
                        this.core.uiNotifications.showNotification('Template updated successfully!', 'success');
                    }

                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error updating custom template:', error);
            return false;
        }
    }

    // Delete a custom template
    async deleteCustomTemplate(templateId) {
        try {
            const response = await fetch(`/api/templates/${templateId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.customTemplates = this.customTemplates.filter(t => t.id !== templateId);
                    this.updateTemplateButtons();

                    if (this.core.uiNotifications) {
                        this.core.uiNotifications.showNotification('Template deleted successfully!', 'success');
                    }

                    // Refresh the manage templates modal if it's open
                    const manageModal = document.getElementById('manageTemplatesModal');
                    if (manageModal && manageModal.classList.contains('show')) {
                        this.loadTemplatesForManagement();
                    }

                    return true;
                }
            }
            return false;
        } catch (error) {
            console.error('Error deleting template:', error);
            return false;
        }
    }

    // Handle template creation form submission
    async handleCreateTemplate() {
        const form = document.getElementById('customTemplateForm');
        if (!form) {
            console.error('Custom template form not found');
            return;
        }

        // Check form validity first
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showNotification('Please fill in all required fields correctly', 'error');
            }
            return;
        }

        const formData = new FormData(form);

        const templateData = {
            name: formData.get('name'),
            title: formData.get('title'),
            classification: formData.get('classification'),
            description: formData.get('description'),
            est_time: parseInt(formData.get('est_time'))
        };

        // Additional validation
        if (!templateData.name || !templateData.title || !templateData.classification || !templateData.description || isNaN(templateData.est_time) || templateData.est_time <= 0) {
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showNotification('Please fill in all required fields', 'error');
            }
            return;
        }

        // Show loading state
        const saveBtn = document.getElementById('saveCustomTemplate');
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';

        let success;
        try {
            if (this.editingTemplateId) {
                // Update existing template
                templateData.id = this.editingTemplateId;
                success = await this.updateCustomTemplate(templateData);
            } else {
                // Create new template
                success = await this.createCustomTemplate(templateData);
            }

            if (success) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('customTemplateModal'));
                if (modal) {
                    modal.hide();
                }
                form.reset(); // Clear the form
                form.classList.remove('was-validated');

                // Reset editing state
                this.editingTemplateId = null;
                this.editingTemplateData = null;
                document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-bookmark-plus"></i> Create Custom Template';
                document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Save Template';

                // Reload templates to reflect changes
                await this.loadCustomTemplates();
            } else {
                if (this.core.uiNotifications) {
                    this.core.uiNotifications.showNotification(this.editingTemplateId ? 'Failed to update template' : 'Failed to create template', 'error');
                }
            }
        } catch (error) {
            console.error('Error handling template creation:', error);
            if (this.core.uiNotifications) {
                this.core.uiNotifications.showNotification('An error occurred while saving the template', 'error');
            }
        } finally {
            // Restore button state
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }

    // Load templates for management modal
    async loadTemplatesForManagement() {
        const container = document.getElementById('templates-list-container');
        if (!container) return;

        try {
            const response = await fetch('/api/templates');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.displayTemplatesForManagement(data.templates);
                } else {
                    container.innerHTML = '<p class="text-muted text-center">No custom templates found.</p>';
                }
            }
        } catch (error) {
            console.error('Error loading templates:', error);
            container.innerHTML = '<p class="text-danger text-center">Error loading templates.</p>';
        }
    }

    // Display templates in management modal
    displayTemplatesForManagement(templates) {
        const container = document.getElementById('templates-list-container');
        if (!container) return;

        if (templates.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-bookmark fs-1 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">No custom templates yet.</p>
                    <p class="text-muted">Create your first template to get started!</p>
                </div>
            `;
            return;
        }

        let html = '<div class="list-group">';
        templates.forEach(template => {
            html += `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h6 class="mb-0">${template.name}</h6>
                                <small class="text-muted">${template.est_time} min</small>
                            </div>
                            <p class="mb-1 text-muted">${template.title}</p>
                            <small class="text-muted">
                                <span class="badge bg-secondary me-1">${template.classification}</span>
                                ${template.description.length > 60 ? template.description.substring(0, 60) + '...' : template.description}
                            </small>
                        </div>
                        <div class="btn-group ms-3" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="taskFormManager.templateManager.editTemplate('${template.id}')" title="Edit Template">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="taskFormManager.templateManager.confirmDeleteTemplate('${template.id}', '${template.name}')" title="Delete Template">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        container.innerHTML = html;
    }

    // Edit template
    editTemplate(templateId) {
        // Find the template
        const template = this.customTemplates.find(t => t.id === templateId);
        if (!template) {
            console.error('Template not found:', templateId);
            return;
        }

        // Store the template ID and data for updating
        this.editingTemplateId = templateId;
        this.editingTemplateData = template;

        // Close manage modal first
        const manageModal = bootstrap.Modal.getInstance(document.getElementById('manageTemplatesModal'));
        if (manageModal) {
            manageModal.hide();
        }

        // Wait for the manage modal to close, then open edit modal
        setTimeout(() => {
            // Change modal title and button text first
            document.getElementById('customTemplateModalLabel').innerHTML = '<i class="bi bi-pencil"></i> Edit Custom Template';
            document.getElementById('saveCustomTemplate').innerHTML = '<i class="bi bi-check-lg"></i> Update Template';

            // Open edit modal
            const editModal = new bootstrap.Modal(document.getElementById('customTemplateModal'));
            editModal.show();

            // The form will be populated by the modal's 'shown.bs.modal' event listener
        }, 300);
    }

    // Confirm template deletion
    confirmDeleteTemplate(templateId, templateName) {
        // Set the template name in the modal
        document.getElementById('deleteTemplateName').textContent = templateName;

        // Store the template ID for deletion
        this.templateToDelete = templateId;

        // Show the delete confirmation modal
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteTemplateModal'));
        deleteModal.show();
    }

    // Get all templates (default + custom)
    getAllTemplates() {
        return [...this.templates, ...this.customTemplates.map(t => ({
            id: `custom-${t.id}`,
            name: t.name,
            icon: 'bi-star',
            data: {
                title: t.title,
                classification: t.classification,
                description: t.description,
                est_time: t.est_time
            },
            isCustom: true,
            customId: t.id
        }))];
    }

    // Cleanup
    destroy() {
        this.templates = [];
        this.customTemplates = [];
        this.editingTemplateId = null;
        this.editingTemplateData = null;
        this.templateToDelete = null;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskTemplateManager;
} else {
    window.TaskTemplateManager = TaskTemplateManager;
}
