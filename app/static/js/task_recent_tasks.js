/**
 * Task Recent Tasks Module
 * Handles loading and displaying recent tasks for quick add functionality
 */

class TaskRecentTasks {
    constructor() {
        this.core = null;
        this.recentTasks = [];
        this.favorites = [];
        this.loadingState = false;
        
        this.loadFavorites();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.loadRecentTasks();
            console.log('TaskRecentTasks initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskRecentTasks:', error);
        }
    }

    // Load recent tasks from API
    async loadRecentTasks() {
        if (this.loadingState) return;
        
        this.loadingState = true;
        const container = document.getElementById('quickAddContainer');
        
        if (container) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2 text-muted">Loading recent tasks...</span>
                </div>
            `;
        }

        try {
            // Try enhanced API first
            const response = await fetch('/api/recent-tasks?limit=10&with_frequency=true');
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.tasks) {
                    this.recentTasks = data.tasks;
                    this.displayEnhancedRecentTasks(data.tasks);
                    this.loadingState = false;
                    return;
                }
            }
            
            // Fallback to basic API
            const fallbackResponse = await fetch('/api/get_recent_tasks');
            if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                if (fallbackData.tasks) {
                    this.recentTasks = fallbackData.tasks;
                    this.displayFallbackRecentTasks(fallbackData.tasks);
                } else {
                    this.displayEmptyState();
                }
            } else {
                this.displayErrorState();
            }
        } catch (error) {
            console.error('Error loading recent tasks:', error);
            this.displayErrorState();
        }
        
        this.loadingState = false;
    }

    // Display enhanced recent tasks with frequency data
    displayEnhancedRecentTasks(tasks) {
        const container = document.getElementById('quickAddContainer');
        if (!container) return;

        if (tasks.length === 0) {
            this.displayEmptyState();
            return;
        }

        let html = '<div class="list-group list-group-flush">';
        
        tasks.forEach((task, index) => {
            const isFavorite = this.favorites.includes(task.id);
            const shortTitle = task.title.length > 40 ? task.title.substring(0, 40) + '...' : task.title;
            const shortDesc = task.description && task.description.length > 60 ? 
                task.description.substring(0, 60) + '...' : (task.description || 'No description');

            html += `
                <div class="list-group-item list-group-item-action py-2 recent-task-item"
                     style="cursor: pointer; border-left: 3px solid ${isFavorite ? '#ffc107' : '#0d6efd'};"
                     data-task='${encodeURIComponent(JSON.stringify(task))}'>
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="mb-0 me-2" style="font-size: 0.9rem; font-weight: 600;">${shortTitle}</h6>
                                ${task.usage_count ? `<span class="badge bg-light text-dark badge-sm">${task.usage_count}x</span>` : ''}
                            </div>
                            <p class="mb-1 text-muted" style="font-size: 0.8rem;">${shortDesc}</p>
                            <div class="d-flex gap-1 align-items-center">
                                <span class="badge bg-secondary" style="font-size: 0.7rem;">${task.classification}</span>
                                <span class="text-muted" style="font-size: 0.7rem;">${task.est_time} min</span>
                                ${task.category ? `<span class="badge bg-info" style="font-size: 0.7rem;">${task.category}</span>` : ''}
                            </div>
                        </div>
                        <div class="d-flex flex-column align-items-end">
                            <button class="btn btn-sm btn-outline-warning favorite-btn mb-1" 
                                    data-task-id="${task.id || index}" 
                                    title="${isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
                                <i class="bi ${isFavorite ? 'bi-star-fill' : 'bi-star'}"></i>
                            </button>
                            <i class="bi bi-arrow-right-circle text-primary"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        
        this.setupRecentTasksEventListeners();
    }

    // Display fallback recent tasks (basic format)
    displayFallbackRecentTasks(tasks) {
        const container = document.getElementById('quickAddContainer');
        if (!container) return;

        if (tasks.length === 0) {
            this.displayEmptyState();
            return;
        }

        let html = '<div class="list-group list-group-flush">';
        
        tasks.forEach((task, index) => {
            const shortTitle = task.title.length > 50 ? task.title.substring(0, 50) + '...' : task.title;
            const shortDesc = task.description && task.description.length > 80 ? 
                task.description.substring(0, 80) + '...' : (task.description || 'No description');

            html += `
                <div class="list-group-item list-group-item-action recent-task-item"
                     style="cursor: pointer;"
                     data-task='${encodeURIComponent(JSON.stringify(task))}'>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${shortTitle}</h6>
                            <p class="mb-1 text-muted small">${shortDesc}</p>
                            <div class="d-flex gap-2">
                                <span class="badge bg-secondary">${task.classification}</span>
                                <span class="text-muted small">${task.est_time} min</span>
                            </div>
                        </div>
                        <div>
                            <i class="bi bi-arrow-right-circle text-primary"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        
        this.setupRecentTasksEventListeners();
    }

    // Display empty state
    displayEmptyState() {
        const container = document.getElementById('quickAddContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="text-muted mt-2 mb-0">No recent tasks found.</p>
                    <p class="text-muted">Use templates above to get started!</p>
                </div>
            `;
        }
    }

    // Display error state
    displayErrorState() {
        const container = document.getElementById('quickAddContainer');
        if (container) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                    <p class="text-muted mt-2 mb-0">Error loading recent tasks.</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="taskFormCore.recentTasks.loadRecentTasks()">
                        <i class="bi bi-arrow-clockwise"></i> Retry
                    </button>
                </div>
            `;
        }
    }

    // Setup event listeners for recent task interactions
    setupRecentTasksEventListeners() {
        // Recent task click to fill form
        document.querySelectorAll('.recent-task-item').forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't trigger if clicking on favorite button
                if (e.target.closest('.favorite-btn')) return;

                const taskData = JSON.parse(decodeURIComponent(item.dataset.task));
                this.applyRecentTask(taskData);
            });
        });

        // Favorite button clicks
        document.querySelectorAll('.favorite-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const taskId = btn.dataset.taskId;
                this.toggleFavorite(taskId);
            });
        });
    }

    // Apply recent task data to form
    applyRecentTask(taskData) {
        if (!this.core) return;

        // Track recent task usage if analytics is available
        if (this.core.analyticsTracker) {
            this.core.analyticsTracker.trackEvent('recent_task_applied', {
                task_title: taskData.title,
                task_classification: taskData.classification
            });
        }

        // Apply task data to form using core's template system
        this.core.applyTemplateData(taskData, `Recent: ${taskData.title}`);

        // Show notification if UI notifications are available
        if (this.core.uiNotifications) {
            this.core.uiNotifications.showNotification(
                `Applied recent task: ${taskData.title}`,
                'success',
                3000
            );
        }
    }

    // Toggle favorite status
    toggleFavorite(taskId) {
        const index = this.favorites.indexOf(taskId);
        if (index === -1) {
            this.favorites.push(taskId);
        } else {
            this.favorites.splice(index, 1);
        }
        
        this.saveFavorites();
        this.loadRecentTasks(); // Refresh display
        
        if (this.core.uiNotifications) {
            const action = index === -1 ? 'added to' : 'removed from';
            this.core.uiNotifications.showNotification(
                `Task ${action} favorites`,
                'info',
                2000
            );
        }
    }

    // Load favorites from localStorage
    loadFavorites() {
        try {
            const saved = localStorage.getItem('taskFormFavorites');
            this.favorites = saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.warn('Could not load favorites:', error);
            this.favorites = [];
        }
    }

    // Save favorites to localStorage
    saveFavorites() {
        try {
            localStorage.setItem('taskFormFavorites', JSON.stringify(this.favorites));
        } catch (error) {
            console.warn('Could not save favorites:', error);
        }
    }

    // Get recent tasks statistics
    getRecentTasksStats() {
        return {
            total_recent_tasks: this.recentTasks.length,
            favorites_count: this.favorites.length,
            loading_state: this.loadingState
        };
    }

    // Cleanup
    destroy() {
        this.recentTasks = [];
        this.favorites = [];
        this.loadingState = false;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskRecentTasks;
} else {
    window.TaskRecentTasks = TaskRecentTasks;
}
