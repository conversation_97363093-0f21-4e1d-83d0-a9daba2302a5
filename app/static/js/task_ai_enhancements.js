/**
 * Task AI Enhancements Module
 * Handles AI analysis, predictions, and enhancement features
 */

class TaskAIEnhancements {
    constructor() {
        this.core = null;
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessing = false;
        this.aiAnalysisEnabled = false;

        this.loadAIAnalysisState();
    }

    setCore(core) {
        this.core = core;
        this.init();
    }

    async init() {
        if (!this.core) return;

        try {
            this.setupAIAnalysisListeners();
            this.addAIToggleButton();
            this.setupAIInsightsContainer();
            console.log('TaskAIEnhancements initialized successfully');
        } catch (error) {
            console.error('Error initializing TaskAIEnhancements:', error);
        }
    }

    // Setup AI analysis event listeners
    setupAIAnalysisListeners() {
        const titleField = this.core.getFormElement('title');
        const descriptionField = this.core.getFormElement('description');

        if (titleField) {
            titleField.addEventListener('input', () => {
                if (this.aiAnalysisEnabled && titleField.value.length >= 3) {
                    this.debounceAIAnalysis();
                }
            });
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', () => {
                if (this.aiAnalysisEnabled) {
                    this.debounceAIAnalysis();
                }
            });
        }
    }

    // Debounced AI analysis to avoid too many requests
    debounceAIAnalysis() {
        if (this.analysisTimer) {
            clearTimeout(this.analysisTimer);
        }

        this.analysisTimer = setTimeout(() => {
            this.performAIAnalysis();
        }, 1000);
    }

    // Perform comprehensive AI analysis
    async performAIAnalysis() {
        if (!this.aiAnalysisEnabled) return;

        const titleField = this.core.getFormElement('title');
        const descriptionField = this.core.getFormElement('description');

        const title = titleField ? titleField.value : '';
        const description = descriptionField ? descriptionField.value : '';

        if (!title || title.length < 3) return;

        try {
            const analysis = await this.analyzeTask(title, description);
            if (analysis) {
                this.displayAIInsights(analysis);

                // Track AI analysis if analytics is available
                if (this.core.analyticsTracker) {
                    this.core.analyticsTracker.trackEvent('ai_analysis_performed', {
                        title_length: title.length,
                        description_length: description.length,
                        analysis_type: 'comprehensive'
                    });
                }
            }
        } catch (error) {
            console.error('Error performing AI analysis:', error);
        }
    }

    // Comprehensive AI analysis of a task
    async analyzeTask(title, description = '') {
        const cacheKey = `analyze_${title}_${description}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            const response = await fetch('/api/ai/analyze-task', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.cache.set(cacheKey, data.analysis);
                    return data.analysis;
                }
            }
        } catch (error) {
            console.error('Error analyzing task:', error);
        }

        return null;
    }

    // Get AI predictions for next likely tasks
    async getNextTaskPredictions(limit = 5) {
        try {
            const response = await fetch(`/api/ai/predict-next-tasks?limit=${limit}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.predictions;
                }
            }
        } catch (error) {
            console.error('Error getting next task predictions:', error);
        }

        return [];
    }

    // Get workload optimization recommendations
    async getWorkloadOptimization(targetDate = null) {
        const dateParam = targetDate ? `?date=${targetDate}` : '';

        try {
            const response = await fetch(`/api/ai/workload-optimization${dateParam}`);

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.optimization;
                }
            }
        } catch (error) {
            console.error('Error getting workload optimization:', error);
        }

        return null;
    }

    // Get enhanced AI-powered classification suggestion
    async getEnhancedClassificationSuggestion(title, description = '') {
        try {
            const response = await fetch('/api/suggestions/enhanced-classification', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.prediction;
                }
            }
        } catch (error) {
            console.error('Error getting enhanced classification:', error);
        }

        return null;
    }

    // Get enhanced AI-powered duration suggestion
    async getEnhancedDurationSuggestion(title, description = '', classification = '') {
        try {
            const response = await fetch('/api/suggestions/enhanced-duration', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ title, description, classification })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    return data.estimation;
                }
            }
        } catch (error) {
            console.error('Error getting enhanced duration:', error);
        }

        return null;
    }

    // Setup AI insights container
    setupAIInsightsContainer() {
        // Create AI insights container if it doesn't exist
        let container = document.getElementById('ai-insights-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'ai-insights-container';
            container.className = 'ai-insights-container mt-3';

            // Insert after the form
            const form = this.core.getFormElement('form');
            if (form && form.parentNode) {
                form.parentNode.insertBefore(container, form.nextSibling);
            }
        }

        // Hide initially if AI analysis is disabled
        if (!this.aiAnalysisEnabled) {
            container.style.display = 'none';
        }
    }

    // Display AI analysis insights in the UI
    displayAIInsights(analysis) {
        const container = document.getElementById('ai-insights-container');
        if (!analysis || !container) return;

        const insightsHtml = `
            <div class="ai-insights-card card border-primary mb-3">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="bi bi-robot"></i> AI Task Analysis
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-light ai-toggle-btn"
                            title="Disable AI Analysis">
                        <i class="bi bi-toggle-on"></i>
                    </button>
                </div>
                <div class="card-body">
                    <!-- Priority Analysis -->
                    <div class="mb-3">
                        <h6 class="text-primary">Priority Assessment</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-${this.getPriorityColor(analysis.priority.level)} me-2">
                                ${analysis.priority.level.toUpperCase()}
                            </span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <div class="progress-bar bg-${this.getPriorityColor(analysis.priority.level)}"
                                     style="width: ${Math.round(analysis.priority.confidence * 100)}%"></div>
                            </div>
                            <small class="text-muted">${Math.round(analysis.priority.confidence * 100)}%</small>
                        </div>
                    </div>

                    <!-- Complexity Analysis -->
                    <div class="mb-3">
                        <h6 class="text-primary">Complexity Assessment</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-${this.getComplexityColor(analysis.complexity.level)} me-2">
                                ${analysis.complexity.level.toUpperCase()}
                            </span>
                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                <div class="progress-bar bg-${this.getComplexityColor(analysis.complexity.level)}"
                                     style="width: ${analysis.complexity.score * 100}%"></div>
                            </div>
                            <small class="text-muted">${Math.round(analysis.complexity.score * 100)}%</small>
                        </div>
                    </div>

                    <!-- Duration Prediction -->
                    <div class="mb-3">
                        <h6 class="text-primary">Duration Estimate</h6>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">${analysis.duration.duration} min</span>
                            <small class="text-muted">
                                Range: ${analysis.duration.range.min}-${analysis.duration.range.max} min
                                (${Math.round(analysis.duration.confidence * 100)}% confidence)
                            </small>
                        </div>
                    </div>

                    <!-- AI Insights -->
                    ${analysis.insights && analysis.insights.length > 0 ? `
                        <div class="mb-3">
                            <h6 class="text-primary">AI Insights</h6>
                            <ul class="list-unstyled mb-0">
                                ${analysis.insights.map(insight => `
                                    <li class="mb-1">
                                        <i class="bi bi-lightbulb text-warning me-1"></i>
                                        <small>${insight}</small>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <!-- Similar Tasks -->
                    ${analysis.similar_tasks && analysis.similar_tasks.length > 0 ? `
                        <div class="mb-0">
                            <h6 class="text-primary">Similar Tasks Found</h6>
                            <div class="row">
                                ${analysis.similar_tasks.slice(0, 3).map(similar => `
                                    <div class="col-md-4 mb-2">
                                        <div class="border rounded p-2">
                                            <small class="fw-bold">${similar.title}</small><br>
                                            <small class="text-muted">
                                                ${similar.classification} • ${similar.est_time}min
                                                <br>Similarity: ${Math.round(similar.similarity * 100)}%
                                            </small>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        container.innerHTML = insightsHtml;

        // Add toggle button event listener
        const toggleBtn = container.querySelector('.ai-toggle-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleAIAnalysis();
            });
        }
    }

    // Get priority color for UI
    getPriorityColor(level) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info',
            'normal': 'secondary'
        };
        return colors[level] || 'secondary';
    }

    // Get complexity color for UI
    getComplexityColor(level) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'success'
        };
        return colors[level] || 'secondary';
    }

    // Load AI analysis state from localStorage
    loadAIAnalysisState() {
        try {
            const saved = localStorage.getItem('aiAnalysisEnabled');
            // Default to true if no saved state exists
            this.aiAnalysisEnabled = saved !== null ? saved === 'true' : true;
        } catch (error) {
            console.warn('Could not load AI analysis state:', error);
            this.aiAnalysisEnabled = true; // Default to enabled
        }
    }

    // Save AI analysis state to localStorage
    saveAIAnalysisState() {
        localStorage.setItem('aiAnalysisEnabled', this.aiAnalysisEnabled.toString());
    }

    // Toggle AI analysis on/off
    toggleAIAnalysis() {
        this.aiAnalysisEnabled = !this.aiAnalysisEnabled;
        this.saveAIAnalysisState();

        // Show/hide AI analysis cards based on toggle state
        const aiCards = document.querySelectorAll('.ai-widget-card, .ai-insight-card, #ai-insights-container, .ai-analysis-card');
        aiCards.forEach(card => {
            if (this.aiAnalysisEnabled) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        if (this.aiAnalysisEnabled) {
            // Re-run AI analysis if enabled
            const titleField = this.core.getFormElement('title');
            if (titleField && titleField.value.length >= 3) {
                this.performAIAnalysis();
            }

            if (this.core.uiNotifications) {
                this.core.uiNotifications.showNotification('AI Task Analysis enabled', 'success', 3000);
            }
        } else {
            // Clear AI insights if disabled
            const container = document.getElementById('ai-insights-container');
            if (container) {
                container.innerHTML = '';
            }

            if (this.core.uiNotifications) {
                this.core.uiNotifications.showNotification('AI Task Analysis disabled', 'info', 3000);
            }
        }

        // Update toggle button
        this.updateToggleButton();
    }

    // Add AI toggle button to the form header
    addAIToggleButton() {
        try {
            const cardHeader = document.querySelector('.card-header');
            if (cardHeader && !document.getElementById('aiToggleMainBtn')) {
                const toggleBtn = document.createElement('button');
                toggleBtn.type = 'button';
                toggleBtn.id = 'aiToggleMainBtn';
                toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
                toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
                toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;

                toggleBtn.addEventListener('click', () => {
                    this.toggleAIAnalysis();
                });

                cardHeader.appendChild(toggleBtn);
            }
        } catch (error) {
            console.error('Error adding AI toggle button:', error);
        }
    }

    // Update toggle button appearance
    updateToggleButton() {
        const toggleBtn = document.getElementById('aiToggleMainBtn');
        if (toggleBtn) {
            toggleBtn.className = `btn btn-sm ${this.aiAnalysisEnabled ? 'btn-primary' : 'btn-outline-secondary'} ms-2`;
            toggleBtn.innerHTML = `<i class="bi bi-robot"></i> AI Analysis ${this.aiAnalysisEnabled ? 'ON' : 'OFF'}`;
            toggleBtn.title = `${this.aiAnalysisEnabled ? 'Disable' : 'Enable'} AI Analysis`;
        }
    }

    // Show AI workload optimization modal
    async showWorkloadOptimization() {
        try {
            const optimization = await this.getWorkloadOptimization();
            if (optimization) {
                // Display optimization recommendations in a modal
                this.displayWorkloadOptimizationModal(optimization);
            }
        } catch (error) {
            console.error('Error showing workload optimization:', error);
        }
    }

    // Display workload optimization modal
    displayWorkloadOptimizationModal(optimization) {
        // Create and show modal with optimization recommendations
        const modalHtml = `
            <div class="modal fade" id="workloadOptimizationModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-graph-up"></i> AI Workload Optimization
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Optimization content would go here -->
                            <p>AI-powered workload optimization recommendations...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page and show
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('workloadOptimizationModal'));
        modal.show();

        // Clean up modal after hiding
        document.getElementById('workloadOptimizationModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // Get AI analysis state
    isAIAnalysisEnabled() {
        return this.aiAnalysisEnabled;
    }

    // Clear AI cache
    clearCache() {
        this.cache.clear();
    }

    // Cleanup
    destroy() {
        if (this.analysisTimer) {
            clearTimeout(this.analysisTimer);
        }

        this.clearCache();
        this.requestQueue = [];
        this.isProcessing = false;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskAIEnhancements;
} else {
    window.TaskAIEnhancements = TaskAIEnhancements;
}
