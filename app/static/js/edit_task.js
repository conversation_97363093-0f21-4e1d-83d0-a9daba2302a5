/**
 * Edit Task Enhanced JavaScript Module
 * Handles form validation, category mapping, and button state management for task editing
 */

class EditTaskManager {
    constructor() {
        console.log('EditTaskManager: Initializing...');
        this.form = null;
        this.submitButton = null;
        this.classificationSelect = null;
        this.categoryInput = null;
        this.init();
    }

    init() {
        this.setupElements();
        this.setupEventListeners();
        this.initializeTaskFormManager();
        console.log('EditTaskManager: Initialization complete');
    }

    setupElements() {
        this.form = document.querySelector('form[method="POST"]');
        this.submitButton = this.form ? this.form.querySelector('button[type="submit"]') : null;
        this.classificationSelect = document.getElementById('classification');
        this.categoryInput = document.getElementById('category');
    }

    setupEventListeners() {
        this.setupCategoryMapping();
        this.setupFormValidation();
        this.setupRealTimeValidation();
    }

    setupCategoryMapping() {
        if (this.classificationSelect && this.categoryInput) {
            this.classificationSelect.addEventListener('change', () => {
                this.updateCategoryFromClassification();
            });
        }
    }

    updateCategoryFromClassification() {
        const classification = this.classificationSelect.value;

        // Try enhanced mapping first
        if (window.taskFormManager) {
            window.taskFormManager.updateCategoryFromClassification();
        } else {
            // Fallback category mapping
            const categoryMap = {
                'Planning': 'Adhoc',
                'Offline Processing': 'Adhoc',
                'Execution': 'Adhoc',
                'Business Support Activities': 'Business Support Activities',
                'Operational Project Involvement': 'Adhoc'
            };
            this.categoryInput.value = categoryMap[classification] || '';
        }
    }

    setupFormValidation() {
        if (!this.form) return;

        this.form.addEventListener('submit', (event) => {
            this.handleFormSubmit(event);
        });
    }

    handleFormSubmit(event) {
        console.log('EditTaskManager: Form submit event triggered');

        // Check form validity first
        if (!this.form.checkValidity()) {
            console.log('EditTaskManager: Form validation failed');
            event.preventDefault();
            event.stopPropagation();
            this.form.classList.add('was-validated');
            return false;
        }

        console.log('EditTaskManager: Form validation passed, allowing submission');
        this.form.classList.add('was-validated');

        // Set processing state with a small delay to show feedback
        // but don't interfere with form submission
        setTimeout(() => {
            if (this.submitButton) {
                this.submitButton.disabled = true;
                this.submitButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating Task...';
            }
        }, 50);

        // Allow form to submit naturally - DO NOT prevent default
        return true;
    }

    resetButtonState() {
        if (this.submitButton) {
            this.submitButton.disabled = false;
            this.submitButton.innerHTML = '<i class="bi bi-check-circle"></i> Update Task';
        }
    }

    setupSafetyTimeout() {
        setTimeout(() => {
            if (this.submitButton && this.submitButton.disabled) {
                console.log('Form submission timeout - button reset');
                this.resetButtonState();
            }
        }, 10000); // 10 second timeout
    }

    setupRealTimeValidation() {
        if (!this.form) return;

        const requiredInputs = this.form.querySelectorAll('[required]');
        requiredInputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.checkValidity()) {
                    this.classList.remove('is-invalid');
                    this.classList.add('is-valid');
                } else {
                    this.classList.remove('is-valid');
                    this.classList.add('is-invalid');
                }
            });
        });
    }

    initializeTaskFormManager() {
        // Initialize TaskFormManager when available
        if (window.TaskFormManager) {
            window.taskFormManager = new TaskFormManager();
        }
    }
}

// Make EditTaskManager available globally
window.EditTaskManager = EditTaskManager;

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EditTaskManager;
}
