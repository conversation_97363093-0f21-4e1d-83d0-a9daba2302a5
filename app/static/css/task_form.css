/* Task Form Enhancements CSS */

/* Template Buttons */
.template-buttons-container {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
}

.template-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0.75rem;
    width: 100%;
    min-width: 0; /* Allow shrinking */
    height: auto;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    text-align: left;
}

.template-btn .d-flex {
    width: 100%;
    min-width: 0; /* Allow flex items to shrink */
}

.template-btn .fw-medium {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 1;
    min-width: 0;
}

.template-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
    border-color: #0d6efd;
}

.template-btn:active {
    transform: translateY(0);
}

.template-btn small {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Responsive template buttons */
@media (max-width: 768px) {
    .template-buttons-container {
        flex-direction: column;
        padding: 0.5rem;
    }

    .template-btn {
        flex-direction: column;
        align-items: flex-start;
        height: auto;
        width: 100%;
        min-width: 0;
        padding: 0.5rem;
    }

    .template-btn .d-flex {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
        gap: 0.25rem;
    }

    .template-btn .d-flex .d-flex {
        flex-direction: row;
        align-items: center;
        width: 100%;
    }

    .template-btn .fw-medium {
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
        font-size: 0.875rem;
    }

    .template-btn small {
        align-self: flex-end;
        margin-top: 0.25rem;
        font-size: 0.75rem;
    }

    .template-btn .badge {
        font-size: 0.6rem !important;
        padding: 0.1em 0.3em;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .template-buttons-container {
        padding: 0.375rem;
    }

    .template-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    .template-btn .fw-medium {
        font-size: 0.8rem;
    }

    .template-btn .badge {
        font-size: 0.55rem !important;
    }
}

/* Card header responsiveness */
.card-header .d-flex {
    flex-wrap: wrap;
    gap: 0.5rem;
}

.card-header .btn-group {
    flex-shrink: 0;
}

@media (max-width: 576px) {
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .card-header .btn-group {
        align-self: flex-end;
        margin-top: 0.5rem;
    }

    .card-header .btn-group .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .card-header small {
        font-size: 0.75rem;
    }
}

/* Enhanced Recent Tasks */
.recent-task-item {
    transition: all 0.2s ease;
    position: relative;
}

.recent-task-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
}

.recent-task-item:active {
    transform: translateX(0);
}

.favorite-btn {
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.favorite-btn:hover {
    transform: scale(1.1);
}

.badge-sm {
    font-size: 0.65rem;
    padding: 0.2em 0.4em;
}

/* Form Field Animations */
.form-control, .form-select {
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

/* Form Button Alignment */
.form-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    justify-content: flex-start;
}

.form-actions .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 38px; /* Consistent button height */
    line-height: 1.5;
    vertical-align: middle;
}

.form-actions .btn i {
    margin-right: 0.375rem;
}

/* Ensure proper button alignment in flexbox contexts */
.d-flex.gap-2 .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    vertical-align: middle;
}

/* Toast Notifications */
.toast {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Quick Stats */
.template-stats {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.template-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.template-stats .stat-item i {
    color: #6c757d;
}

/* Improved Template Instructions */
.template-instructions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.instruction-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: white;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.instruction-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.instruction-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.instruction-icon i {
    font-size: 1rem;
}

.instruction-content {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex-grow: 1;
}

.instruction-content strong {
    font-size: 0.875rem;
    font-weight: 600;
    color: #495057;
    line-height: 1.2;
}

.instruction-content small {
    font-size: 0.75rem;
    line-height: 1.2;
}

/* Responsive instruction cards */
@media (max-width: 768px) {
    .template-instructions {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .instruction-card {
        padding: 0.375rem 0.5rem;
        gap: 0.5rem;
    }

    .instruction-icon {
        width: 28px;
        height: 28px;
    }

    .instruction-icon i {
        font-size: 0.875rem;
    }

    .instruction-content strong {
        font-size: 0.8rem;
    }

    .instruction-content small {
        font-size: 0.7rem;
    }
}

/* Loading States */
.template-btn.loading {
    pointer-events: none;
    opacity: 0.6;
}

.template-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Improved Form Layout */
.form-section {
    margin-bottom: 2rem;
}

.form-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* Enhanced Card Headers */
.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #dee2e6;
}

/* Quick Action Buttons */
.quick-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.quick-actions .btn {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* Mobile Optimizations */
@media (max-width: 576px) {
    .template-buttons-container {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .recent-task-item {
        padding: 1rem !important;
    }

    .quick-actions {
        flex-direction: column;
    }

    .quick-actions .btn {
        margin-bottom: 0.5rem;
    }
}

/* Accessibility Improvements */
.template-btn:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

.favorite-btn:focus {
    outline: 2px solid #ffc107;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .template-btn {
        border-width: 2px;
    }

    .recent-task-item {
        border-left-width: 4px !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .template-btn,
    .recent-task-item,
    .form-control,
    .form-select,
    .favorite-btn {
        transition: none;
    }

    .toast {
        animation: none;
    }

    .template-btn:hover,
    .recent-task-item:hover {
        transform: none;
    }
}

/* Phase 2: Custom Template and Bulk Entry Styles */

/* Custom Template Modal */
.custom-template-form .form-label {
    font-weight: 600;
    color: #495057;
}

.custom-template-form .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Template Delete Button */
.template-btn .btn-outline-danger {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.template-btn:hover .btn-outline-danger {
    opacity: 1;
}

/* Custom template styling */
.template-btn.custom-template {
    border: 2px dashed #6c757d;
    background: linear-gradient(135deg, #fff3cd 0%, #fff9e6 100%);
}

.template-btn.custom-template:hover {
    border-color: #ffc107;
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
}

/* Bulk Entry Styles */
.bulk-entry-container {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.bulk-task-item {
    transition: all 0.3s ease;
    border-left: 4px solid #0d6efd;
}

.bulk-task-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bulk-task-item .form-control-sm {
    font-size: 0.875rem;
}

.bulk-task-item .btn-outline-danger {
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.bulk-task-item:hover .btn-outline-danger {
    opacity: 1;
}

/* Bulk entry headers */
.bulk-entry-headers {
    display: flex;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #6c757d;
    font-size: 0.875rem;
}

.bulk-entry-headers > div {
    padding: 0.5rem;
}

/* Bulk mode toggle */
.bulk-mode-active {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
}

/* Progress indicators for bulk operations */
.bulk-progress {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 9999;
}

/* Notification styles */
.notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced template stats */
.template-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
    font-size: 0.875rem;
}

.template-stats .stat-item i {
    color: #0d6efd;
}

/* Pattern Recognition Indicators (for future Phase 3) */
.pattern-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #ff6b6b, #ffa726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Mobile optimizations for Phase 2 */
@media (max-width: 768px) {
    .bulk-task-item .row {
        gap: 0.5rem;
    }

    .bulk-task-item .col-md-1,
    .bulk-task-item .col-md-2,
    .bulk-task-item .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.5rem;
    }

    .template-btn .btn-outline-danger {
        opacity: 1;
        position: static;
        margin-top: 0.5rem;
    }

    .bulk-entry-container .card-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .bulk-entry-container .card-header > div {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
    }
}

/* Responsive spacing between Quick Add and Quick Templates */
@media (max-width: 991.98px) {
    /* On screens smaller than lg, add spacing between stacked columns */
    .col-lg-4 {
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .col-lg-4 {
        margin-top: 1.5rem;
    }
}

@media (max-width: 576px) {
    .col-lg-4 {
        margin-top: 1rem;
    }
}

/* Touch-friendly enhancements for mobile */
@media (hover: none) {
    .template-btn .btn-outline-danger,
    .bulk-task-item .btn-outline-danger {
        opacity: 1;
    }

    .template-btn {
        padding: 1rem;
        min-height: 100px;
    }
}

/* Enhanced accessibility */
.template-btn:focus,
.bulk-task-item .form-control:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Loading states */
.loading-template {
    opacity: 0.6;
    pointer-events: none;
}

.loading-template::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}
