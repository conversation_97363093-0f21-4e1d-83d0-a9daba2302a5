/**
 * AdhocLog - Enhanced Navigation & Responsive Design CSS
 *
 * This file contains advanced navigation styling and responsive design improvements
 * following modern UI/UX best practices for corporate applications.
 */

/* ==========================================
   RESET & BASE STYLES
   ========================================== */

/* Ensure no default margins/padding on html/body */
html, body {
    margin: 0;
    padding: 0;
}

/* Smooth scrolling behavior */
html {
    scroll-behavior: smooth;
}

/* Enhanced navbar scroll behavior */
.navbar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Navbar scroll state */
.navbar.navbar-scrolled {
    box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
}

/* Active navigation indicators */
.nav-link.active {
    position: relative;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 3px;
    background: linear-gradient(90deg, #fbbf24, #f59e0b);
    border-radius: 2px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 20px;
        opacity: 1;
    }
}

/* Navigation link hover effects */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    z-index: 1;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link .nav-text,
.nav-link .nav-icon {
    position: relative;
    z-index: 2;
}

/* Enhanced mobile navigation */
@media (max-width: 991.98px) {
    .navbar-collapse {
        animation: fadeInDown 0.3s ease;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        margin-top: 1rem;
        padding: 1.5rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .nav-link {
        margin: 0.25rem 0;
        padding: 0.875rem 1rem !important;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-link:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Mobile dropdown adjustments */
    .dropdown-menu {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        margin-top: 0.5rem;
        animation: fadeInUp 0.2s ease;
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================
   CARD & CONTENT ENHANCEMENTS
   ========================================== */

/* Enhanced card styling */
.card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-4px);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px 16px 0 0 !important;
    font-weight: 600;
    color: #334155;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Stats cards enhancements */
.stats-card {
    background: var(--primary-gradient);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
    min-height: 140px;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: rotate(0deg) translate(0, 0);
    }
    50% {
        transform: rotate(180deg) translate(10px, -10px);
    }
}

.stats-card .card-body {
    position: relative;
    z-index: 2;
}

/* Task cards enhancements */
.task-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid #3b82f6;
    position: relative;
    overflow: hidden;
}

.task-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

.task-card:hover {
    transform: translateY(-6px);
    border-left-color: #1d4ed8;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.task-card:hover::before {
    width: 8px;
}

/* ==========================================
   RESPONSIVE GRID IMPROVEMENTS
   ========================================== */

/* Enhanced responsive breakpoints */
@media (min-width: 576px) {
    .container-sm-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 768px) {
    .container-md-fluid {
        padding-left: 3rem;
        padding-right: 3rem;
    }
}

@media (min-width: 992px) {
    .container-lg-fluid {
        padding-left: 4rem;
        padding-right: 4rem;
    }
}

/* Responsive spacing utilities */
.spacing-responsive {
    margin: 1rem 0;
}

@media (min-width: 768px) {
    .spacing-responsive {
        margin: 2rem 0;
    }
}

@media (min-width: 992px) {
    .spacing-responsive {
        margin: 3rem 0;
    }
}

/* ==========================================
   FORM ENHANCEMENTS
   ========================================== */

/* Enhanced form controls */
.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.875rem 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
}

.form-control:focus, .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 1);
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

/* Enhanced buttons */
.btn {
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

.btn-primary {
    background: var(--primary-gradient);
    box-shadow: 0 4px 14px 0 rgba(102, 126, 234, 0.39);
}

.btn-primary:hover {
    background: var(--primary-gradient-hover);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px 0 rgba(102, 126, 234, 0.5);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px 0 rgba(16, 185, 129, 0.5);
}

.btn-outline-primary {
    border: 2px solid #3b82f6;
    color: #3b82f6;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.3);
}

/* ==========================================
   TABLE ENHANCEMENTS
   ========================================== */

.table {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid #e2e8f0;
    font-weight: 700;
    color: #475569;
    padding: 1rem 1.25rem;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.1em;
}

.table td {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transform: scale(1.01);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* ==========================================
   MOBILE OPTIMIZATIONS
   ========================================== */

@media (max-width: 768px) {
    /* Adjust main content padding */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Stack stats cards */
    .stats-card {
        margin-bottom: 1rem;
        min-height: 120px;
    }

    /* Adjust card padding */
    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem;
    }

    /* Responsive table */
    .table-responsive {
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* Mobile form adjustments */
    .form-control, .form-select {
        padding: 1rem;
        font-size: 1rem; /* Prevent zoom on iOS */
    }

    /* Mobile button adjustments */
    .btn {
        padding: 0.875rem 1.25rem;
        font-size: 1rem;
    }

    /* Touch-friendly spacing */
    .btn-group .btn {
        margin: 0.25rem 0;
    }
}

@media (max-width: 576px) {
    /* Extra small screens */
    body {
        font-size: 0.875rem;
    }

    h1 {
        font-size: 1.75rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .card {
        border-radius: 12px;
    }

    .btn {
        border-radius: 8px;
        font-size: 0.875rem;
    }

    /* Compact stats layout */
    .stats-card .display-4 {
        font-size: 2rem;
    }

    /* Mobile navigation adjustments */
    .navbar-brand {
        font-size: 1.1rem;
    }

    .brand-logo {
        width: 22px;
        height: 22px;
    }
}

/* ==========================================
   ACCESSIBILITY ENHANCEMENTS
   ========================================== */

/* Focus indicators */
.btn:focus,
.form-control:focus,
.form-select:focus,
.nav-link:focus {
    outline: 3px solid rgba(59, 130, 246, 0.5);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #374151;
    }

    .nav-link {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .navbar,
    .card,
    .btn,
    .nav-link {
        transition: none;
    }

    .card:hover,
    .task-card:hover,
    .btn:hover {
        transform: none;
    }
}

/* ==========================================
   DARK MODE PREPARATIONS
   ========================================== */

@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8fafc;
        --text-secondary: #e2e8f0;
        --bg-primary: #1e293b;
        --bg-secondary: #334155;
        --border-color: #475569;
    }

    /* Note: Full dark mode implementation would go here */
    /* This is prepared for future dark mode support */
}

/* ==========================================
   PERFORMANCE OPTIMIZATIONS
   ========================================== */

/* GPU acceleration for animations */
.card,
.btn,
.nav-link,
.task-card {
    will-change: transform;
    transform: translateZ(0);
}

/* Optimize backdrop-filter performance */
.navbar,
.card,
.dropdown-menu {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transform: translateZ(0);
}

/* ==========================================
   PRINT STYLES
   ========================================== */

@media print {
    .navbar,
    .btn,
    .dropdown-menu {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }

    body {
        background: white !important;
        color: black !important;
    }
}
