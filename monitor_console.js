// Console monitoring script to detect JavaScript errors
// This can be pasted into browser console to monitor for errors

console.log('🔍 Console Monitor Started - Watching for JavaScript errors and form submission events');

// Store original console methods
const originalError = console.error;
const originalWarn = console.warn;
const originalLog = console.log;

// Track errors
let errorCount = 0;
let warningCount = 0;
let formSubmissionCount = 0;

// Override console.error to track errors
console.error = function(...args) {
    errorCount++;
    originalError.apply(console, ['❌ ERROR #' + errorCount + ':', ...args]);
};

// Override console.warn to track warnings
console.warn = function(...args) {
    warningCount++;
    originalWarn.apply(console, ['⚠️ WARNING #' + warningCount + ':', ...args]);
};

// Monitor form submissions
document.addEventListener('submit', function(e) {
    formSubmissionCount++;
    console.log(`📝 FORM SUBMISSION #${formSubmissionCount}:`, {
        form: e.target,
        action: e.target.action,
        method: e.target.method,
        timestamp: new Date().toISOString()
    });
    
    // Monitor if form submission gets prevented
    setTimeout(() => {
        if (e.defaultPrevented) {
            console.warn('Form submission was prevented by JavaScript');
        } else {
            console.log('Form submission proceeding normally');
        }
    }, 10);
});

// Monitor button state changes
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
            const target = mutation.target;
            if (target.tagName === 'BUTTON' && target.type === 'submit') {
                if (target.disabled) {
                    console.log('🔄 Submit button disabled:', target.innerHTML);
                } else if (mutation.type === 'childList' && target.innerHTML.includes('Processing')) {
                    console.log('⏳ Submit button in processing state:', target.innerHTML);
                }
            }
        }
    });
});

// Start observing
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['disabled', 'class']
});

// Monitor page navigation
window.addEventListener('beforeunload', function() {
    console.log('🚀 Page is navigating away (this is expected after form submission)');
});

// Report status every 10 seconds
setInterval(() => {
    console.log(`📊 Status Report: ${errorCount} errors, ${warningCount} warnings, ${formSubmissionCount} form submissions`);
}, 10000);

console.log('✅ Console Monitor Ready - Submit forms to see detailed logging');
