@echo off
setlocal enabledelayedexpansion
REM AdhocLog - Windows Launcher
REM Universal launcher for Windows systems with comprehensive Python detection

cd /d "%~dp0"

REM Debug output for SharePoint troubleshooting
echo [DEBUG] Script directory: %~dp0
echo [DEBUG] Current directory: %CD%
echo [DEBUG] Script name: %~nx0

echo ========================================
echo   [LAUNCH] AdhocLog
echo   Universal Launcher (Windows)
echo ========================================

REM SharePoint Environment Detection and Configuration
echo.
echo [INFO] Checking deployment environment...

set "SHAREPOINT_DETECTED=false"
set "SHAREPOINT_TYPE="
set "CURRENT_PATH=%CD%"

REM Check for various SharePoint/OneDrive path patterns
echo !CURRENT_PATH! | findstr /I "OneDrive" >nul
if !errorlevel! == 0 (
    set "SHAREPOINT_DETECTED=true"
    echo !CURRENT_PATH! | findstr /I "OneDrive - " >nul
    if !errorlevel! == 0 (
        set "SHAREPOINT_TYPE=OneDrive Business"
    ) else (
        set "SHAREPOINT_TYPE=OneDrive Personal"
    )
)

echo !CURRENT_PATH! | findstr /I "SharePoint" >nul
if !errorlevel! == 0 (
    set "SHAREPOINT_DETECTED=true"
    set "SHAREPOINT_TYPE=SharePoint Site"
)

echo !CURRENT_PATH! | findstr /I "Microsoft Teams" >nul
if !errorlevel! == 0 (
    set "SHAREPOINT_DETECTED=true"
    set "SHAREPOINT_TYPE=Teams Document Library"
)

echo !CURRENT_PATH! | findstr /I "\Sites\" >nul
if !errorlevel! == 0 (
    set "SHAREPOINT_DETECTED=true"
    set "SHAREPOINT_TYPE=SharePoint/Teams"
)

if "!SHAREPOINT_DETECTED!"=="true" (
    echo 🌐 SharePoint environment detected: !SHAREPOINT_TYPE!
    echo 📁 Path: !CURRENT_PATH!
    echo 🚀 Configuring for SharePoint deployment...

    REM Clean Python cache directories
    echo 🧹 Cleaning Python cache directories...
    for /d /r . %%d in (__pycache__) do (
        if exist "%%d" (
            rmdir /s /q "%%d" 2>nul >nul
            if !errorlevel! == 0 echo   ✅ Removed cache directory
        )
    )

    REM Clean .pyc files
    for /r . %%f in (*.pyc) do del "%%f" 2>nul >nul

    REM Setup cache isolation
    set "PYTHONDONTWRITEBYTECODE=1"
    echo 🔒 Cache isolation enabled

    set "TEMP_CACHE_DIR=%TEMP%\adhoclog_cache_%USERNAME%"
    mkdir "!TEMP_CACHE_DIR!" 2>nul
    if exist "!TEMP_CACHE_DIR!" (
        set "PYTHONPYCACHEPREFIX=!TEMP_CACHE_DIR!"
        echo 📁 User cache directory: !TEMP_CACHE_DIR!
    )

    REM Create user-specific data directories
    set "USER_DATA_DIR=data\user_%USERNAME%"
    echo 👤 Setting up user data for: %USERNAME%
    mkdir "!USER_DATA_DIR!" 2>nul
    if exist "!USER_DATA_DIR!" (
        echo ✅ User data directory: !USER_DATA_DIR!
        set "ADHOCLOG_USER_DATA_DIR=!USER_DATA_DIR!"
        set "ADHOCLOG_SHAREPOINT_MODE=1"
    ) else (
        echo ⚠️ Using legacy data structure
    )

    echo ✅ SharePoint configuration complete
) else (
    echo 💻 Local development environment detected
    echo ✅ Using standard configuration
)
echo.

REM Detect platform information for virtual environment management
for /f "tokens=4-5 delims=. " %%i in ('ver') do set "WIN_VERSION=%%i.%%j"
set "PLATFORM_OS=Windows"
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set "PLATFORM_ARCH=x64"
) else if "%PROCESSOR_ARCHITECTURE%"=="ARM64" (
    set "PLATFORM_ARCH=ARM64"
) else (
    set "PLATFORM_ARCH=x86"
)

REM Function to check if a command exists
set "PYTHON_CMD="
set "PYTHON_VERSION="
set "BEST_PYTHON="
set "BEST_VERSION="

echo 🔍 Detecting Python installations...

REM Array of Python commands to try
set "python_candidates=python3 python py -3 py -3.11 py -3.10 py -3.9 py -3.8 py -3.7"

REM Check each candidate
for %%p in (%python_candidates%) do (
    echo Testing: %%p
    %%p --version >nul 2>&1
    if !errorlevel! == 0 (
        for /f "tokens=2" %%v in ('%%p --version 2^>^&1') do (
            set "version=%%v"
            echo !version! | findstr /b "3\." >nul
            if !errorlevel! == 0 (
                echo ✅ Found Python: %%p (!version!)

                REM Test if tkinter is available
                %%p -c "import tkinter; print('tkinter available')" >nul 2>&1
                if !errorlevel! == 0 (
                    echo ✅ Found Python with tkinter: %%p (!version!)
                    set "BEST_PYTHON=%%p"
                    set "BEST_VERSION=!version!"
                    goto :python_found
                ) else (
                    echo ⚠️ Found Python without tkinter: %%p (!version!)
                    if "!BEST_PYTHON!"=="" (
                        set "BEST_PYTHON=%%p"
                        set "BEST_VERSION=!version!"
                    )
                )
            )
        )
    )
)

REM Use best Python found, even if no tkinter
if not "!BEST_PYTHON!"=="" (
    set "PYTHON_CMD=!BEST_PYTHON!"
    set "PYTHON_VERSION=!BEST_VERSION!"
    goto :python_found
)

REM Python not found
echo ❌ Python 3.7+ is required but not found!
echo.
echo 📥 Please install Python from one of these sources:
echo    • Official Python: https://www.python.org/downloads/windows/
echo    • Microsoft Store: Search for "Python 3.11"
echo    • Anaconda: https://www.anaconda.com/products/distribution
echo.
echo ⚠️ Installation Tips:
echo    • Check "Add Python to PATH" during installation
echo    • Choose "Install for all users" if you have admin rights
echo    • After installation, restart this script
echo.
echo 🔄 Available installation methods:
echo    • python --version
echo    • python3 --version
echo    • py -3 --version
echo.
set /p "install_choice=Would you like to open the Python download page? (y/n): "
if /i "!install_choice!"=="y" (
    start https://www.python.org/downloads/windows/
)
pause
exit /b 1

:python_found
echo ✅ Selected Python: !PYTHON_CMD! (!PYTHON_VERSION!)

echo 🪟 Windows Version: !WIN_VERSION!
echo 💻 Architecture: !PLATFORM_ARCH!

REM Setup virtual environment for SharePoint deployments
if "!SHAREPOINT_DETECTED!"=="true" (
    echo.
    echo [SETUP] Setting up virtual environment in home directory...

    REM Create virtual environment in user's home directory
    set "VENV_BASE_DIR=%USERPROFILE%\.venvs"
    set "VENV_DIR=%VENV_BASE_DIR%\adhoc-log-app"
    echo [INFO] User: !USERNAME!
    echo [INFO] Virtual environment: !VENV_DIR!

    REM Create .venvs directory in home if it doesn't exist
    if not exist "%VENV_BASE_DIR%" mkdir "%VENV_BASE_DIR%"

    REM Check if virtual environment already exists and is valid
    set "VENV_VALID=false"
    set "VENV_PYTHON=!VENV_DIR!\Scripts\python.exe"

    if exist "!VENV_DIR!" (
        echo [INFO] Checking existing virtual environment...
        if exist "!VENV_PYTHON!" (
            "!VENV_PYTHON!" --version >nul 2>&1
            if !errorlevel! == 0 (
                REM Check platform compatibility
                for /f "delims=" %%i in ('"!VENV_PYTHON!" -c "import platform; print(platform.machine())" 2^>nul') do set "VENV_PLATFORM=%%i"
                if "!VENV_PLATFORM!"=="!PLATFORM_ARCH!" (
                    echo [SUCCESS] Existing virtual environment is compatible
                    set "VENV_VALID=true"

                    REM Set up environment to use this venv
                    set "VIRTUAL_ENV=!VENV_DIR!"
                    set "PATH=!VENV_DIR!\Scripts;!PATH!"
                    set "PYTHON_CMD=!VENV_DIR!\Scripts\python.exe"

                    REM Check if requirements are installed
                    "!PYTHON_CMD!" -c "import flask" >nul 2>&1
                    if !errorlevel! neq 0 (
                        echo [INSTALL] Installing/updating requirements...
                        call :install_venv_requirements "!VENV_DIR!"
                    ) else (
                        echo [SUCCESS] Required packages already installed
                    )
                ) else (
                    echo [WARNING] Platform mismatch detected - recreating virtual environment
                    rmdir /s /q "!VENV_DIR!" 2>nul
                )
            ) else (
                echo [WARNING] Virtual environment is broken - recreating
                rmdir /s /q "!VENV_DIR!" 2>nul
            )
        )
    )

    REM Create new virtual environment if needed
    if "!VENV_VALID!"=="false" (
        echo [CREATE] Creating new virtual environment...
        !PYTHON_CMD! -m venv "!VENV_DIR!"
        if !errorlevel! == 0 (
            echo [SUCCESS] Virtual environment created successfully

            REM Set up environment
            set "VIRTUAL_ENV=!VENV_DIR!"
            set "PATH=!VENV_DIR!\Scripts;!PATH!"
            set "PYTHON_CMD=!VENV_DIR!\Scripts\python.exe"

            REM Install requirements
            call :install_venv_requirements "!VENV_DIR!"
        ) else (
            echo [ERROR] Failed to create virtual environment
            echo [INFO] Falling back to system Python
        )
    )

    REM Cleanup old virtual environments from project directory
    call :cleanup_old_venvs
)

echo.
echo 🔍 Checking GUI availability...

REM Test tkinter functionality
set "GUI_AVAILABLE=false"
set "GUI_REASON="

echo 🔍 Testing tkinter availability...
!PYTHON_CMD! -c "import tkinter; root = tkinter.Tk(); root.withdraw(); root.destroy(); print('tkinter_works')" >nul 2>&1
if !errorlevel! == 0 (
    echo ✅ tkinter is working correctly
    set "GUI_AVAILABLE=true"
    set "GUI_REASON=tkinter available and functional"
) else (
    echo ❌ tkinter test failed
    !PYTHON_CMD! -c "import tkinter" >nul 2>&1
    if !errorlevel! == 0 (
        echo ⚠️ tkinter is installed but not working properly
        set "GUI_REASON=tkinter installed but display unavailable"
    ) else (
        echo ❌ tkinter is not installed
        set "GUI_REASON=tkinter not installed"
        echo 💡 On Windows, tkinter should be included with Python
        echo 💡 Try reinstalling Python from python.org with default options
    )
)

REM Check for headless environment (like Windows Server Core or SSH)
if defined SSH_CLIENT (
    echo 🔒 SSH session detected - GUI may not be available
    set "GUI_AVAILABLE=false"
    set "GUI_REASON=SSH session (no GUI access)"
)

echo.

REM Show options based on GUI availability
REM Lets go!!!!!!!!!!!!!!
echo Choose how to launch the application:
echo.
if "!GUI_AVAILABLE!"=="true" (
    echo ✅ GUI is available!
    echo.
    echo 1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface
    echo 2. 🚀 Quick Start - Start app immediately
    echo 3. 🔧 Advanced Options - Access utility scripts
    echo 4. ❓ Help ^& Troubleshooting
    echo.
    set /p "choice=Enter your choice (1-4): "
) else (
    echo ❌ GUI is not available (!GUI_REASON!)
    echo Available options:
    echo 1. 🚀 Quick Start - Start app immediately
    echo 2. 🔧 Advanced Options - Access utility scripts
    echo 3. ❓ Help ^& Troubleshooting
    echo.
    set /p "choice=Enter your choice (1-3): "
    REM Adjust choice for non-GUI systems
    if "!choice!"=="1" set "choice=2"
    if "!choice!"=="2" set "choice=3"
    if "!choice!"=="3" set "choice=4"
)

if "!choice!"=="1" goto :gui_launcher
if "!choice!"=="2" goto :quick_start
if "!choice!"=="3" goto :advanced
if "!choice!"=="4" goto :help
echo ❌ Invalid option. Please try again.
pause
goto :0

:gui_launcher
if "!GUI_AVAILABLE!"=="true" (
    echo [INFO] Starting GUI Launcher...
    REM Check for home directory virtual environment first
    if exist "!VENV_DIR!\Scripts\python.exe" (
        echo [SUCCESS] Using home directory virtual environment Python...
        echo [DEBUG] Starting GUI with: "!VENV_DIR!\Scripts\python.exe" gui_launcher.py
        "!VENV_DIR!\Scripts\python.exe" gui_launcher.py
        if !errorlevel! neq 0 (
            echo.
            echo ❌ GUI Launcher failed to start properly
            echo [DEBUG] Exit code: !errorlevel!
            echo.
            pause
        )
    ) else if exist "venv\Scripts\python.exe" (
        echo [WARNING] Using legacy virtual environment Python...
        echo [DEBUG] Starting GUI with: "venv\Scripts\python.exe" gui_launcher.py
        "venv\Scripts\python.exe" gui_launcher.py
        if !errorlevel! neq 0 (
            echo.
            echo ❌ GUI Launcher failed to start properly
            echo [DEBUG] Exit code: !errorlevel!
            echo.
            pause
        )
    ) else (
        echo [INFO] Virtual environment not found, using system Python...
        echo [DEBUG] Starting GUI with: !PYTHON_CMD! gui_launcher.py
        !PYTHON_CMD! gui_launcher.py
        if !errorlevel! neq 0 (
            echo.
            echo ❌ GUI Launcher failed to start properly
            echo [DEBUG] Exit code: !errorlevel!
            echo.
            pause
        )
    )
) else (
    echo [ERROR] GUI not available on this system (!GUI_REASON!)
    echo [INFO] Use option 2 for Quick Start instead
    pause
)
goto :end

:quick_start
echo 🚀 Quick Start - Starting application...
REM Use virtual environment Python if available
if exist "venv\Scripts\python.exe" (
    echo 📦 Using virtual environment Python...
    echo 📖 Building documentation...
    "venv\Scripts\python.exe" -c "try: import mkdocs; print('✅ MkDocs available')" >nul 2>&1 || echo "⚠️ MkDocs not available, documentation will be limited"
    echo [DEBUG] Starting application with: "venv\Scripts\python.exe" scripts\run.py
    "venv\Scripts\python.exe" scripts\run.py
    if !errorlevel! neq 0 (
        echo.
        echo ❌ Application failed to start properly
        echo [DEBUG] Exit code: !errorlevel!
        echo.
        pause
    )
) else (
    echo ⚠️ Virtual environment not found, using system Python...
    echo 📖 Building documentation...
    %PYTHON_CMD% -c "try: import mkdocs; print('✅ MkDocs available')" >nul 2>&1 || echo "⚠️ MkDocs not available, documentation will be limited"
    echo [DEBUG] Starting application with: %PYTHON_CMD% scripts\run.py
    %PYTHON_CMD% scripts\run.py
    if !errorlevel! neq 0 (
        echo.
        echo ❌ Application failed to start properly
        echo [DEBUG] Exit code: !errorlevel!
        echo.
        pause
    )
)
goto :end

:advanced
echo 🔧 Advanced Options:
echo 1. Setup/Repair Environment
echo 2. Run Diagnostics
echo 3. Create Sample Data
echo 4. Clear All Data (⚠️ Destructive)
echo 5. Debug Mode
echo 6. Back to Main Menu
echo.
set /p "adv_choice=Choose option (1-6): "

if "%adv_choice%"=="1" goto :setup
if "%adv_choice%"=="2" goto :diagnostics
if "%adv_choice%"=="3" goto :sample_data
if "%adv_choice%"=="4" goto :clear_data
if "%adv_choice%"=="5" goto :debug_mode
if "%adv_choice%"=="6" goto :0
echo ❌ Invalid option
pause
goto :advanced

:setup
echo 🔧 Running comprehensive setup and repair...
echo.

REM Step 1: Configure pip for corporate/VPN environments
echo 🔍 Step 1: Configuring pip for corporate/VPN environments...
if not exist "%APPDATA%\pip" mkdir "%APPDATA%\pip"
(
echo [global]
echo trusted-host = pypi.org
echo                pypi.python.org
echo                files.pythonhosted.org
echo timeout = 60
echo retries = 3
) > "%APPDATA%\pip\pip.ini"
echo ✅ pip configuration created

REM Step 2: Validate/repair virtual environment
echo.
echo 🔍 Step 2: Checking virtual environment...

REM Function to validate virtual environment
set "VENV_PYTHON=venv\Scripts\python.exe"
set "VENV_VALID=false"

if exist "venv" (
    echo 📁 Virtual environment directory found
    if exist "!VENV_PYTHON!" (
        echo 🔍 Testing virtual environment Python...
        "!VENV_PYTHON!" --version >nul 2>&1
        if !errorlevel! == 0 (
            for /f "tokens=2" %%v in ('"!VENV_PYTHON!" --version 2^>^&1') do (
                echo ✅ Virtual environment Python working: %%v
                set "VENV_VALID=true"
            )
        ) else (
            echo ❌ Virtual environment Python broken
        )
    ) else (
        echo ❌ Virtual environment Python executable missing
    )
) else (
    echo 📦 Virtual environment not found
)

REM Create or repair virtual environment if needed
if "!VENV_VALID!"=="false" (
    echo 🔧 Creating/repairing virtual environment...

    REM Remove broken virtual environment if it exists
    if exist "venv" (
        echo 🗑️ Removing broken virtual environment...
        rmdir /s /q "venv" 2>nul
    )

    echo 📦 Creating new virtual environment...
    !PYTHON_CMD! -m venv venv
    if !errorlevel! neq 0 (
        echo ❌ Failed to create virtual environment with !PYTHON_CMD!
        echo 🔧 Trying alternative methods...

        REM Try different Python commands
        for %%p in (python3 python py -3) do (
            echo Trying: %%p
            %%p --version >nul 2>&1
            if !errorlevel! == 0 (
                %%p -m venv venv
                if !errorlevel! == 0 (
                    echo ✅ Virtual environment created with %%p
                    goto :venv_created
                )
            )
        )

        echo ❌ All virtual environment creation attempts failed
        echo 💡 Try installing Python from python.org with default options
        pause
        goto :advanced
    ) else (
        echo ✅ Virtual environment created successfully
    )
) else (
    echo ✅ Virtual environment is ready
)

:venv_created
REM Verify the virtual environment one more time
if not exist "!VENV_PYTHON!" (
    echo ❌ Virtual environment setup failed - python.exe not found
    echo 🔍 Checking directory structure...
    if exist "venv" (
        dir "venv" /b
        if exist "venv\Scripts" (
            echo Scripts directory contents:
            dir "venv\Scripts" /b
        )
    )
    pause
    goto :advanced
)

echo ✅ Virtual environment ready: !VENV_PYTHON!

REM Step 3: Upgrade pip with multiple strategies
echo.
echo 🔍 Step 3: Upgrading pip...
set "PIP_SUCCESS=false"

REM Try multiple pip upgrade strategies
echo 📦 Attempting pip upgrade...
"!VENV_PYTHON!" -m pip install --upgrade pip
if !errorlevel! == 0 (
    echo ✅ pip upgraded successfully
    set "PIP_SUCCESS=true"
) else (
    echo ⚠️ Standard pip upgrade failed, trying with trusted hosts...
    "!VENV_PYTHON!" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip
    if !errorlevel! == 0 (
        echo ✅ pip upgraded with trusted hosts
        set "PIP_SUCCESS=true"
    ) else (
        echo ⚠️ Virtual environment pip upgrade failed, trying with system Python...
        !PYTHON_CMD! -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip
        if !errorlevel! == 0 (
            echo ✅ pip upgraded with system Python
            set "PIP_SUCCESS=true"
        ) else (
            echo ⚠️ All pip upgrade attempts failed, continuing with existing pip...
        )
    )
)

REM Step 4: Install/update dependencies
echo.
echo 🔍 Step 4: Installing dependencies...

REM Check if requirements.txt exists
if not exist "requirements.txt" (
    echo ❌ requirements.txt not found in current directory
    echo 📁 Current directory: !CD!
    echo 📋 Available files:
    dir *.txt /b 2>nul
    pause
    goto :advanced
)

echo 📋 Found requirements.txt, installing packages...
set "REQ_SUCCESS=false"

REM Try multiple installation strategies
echo 📦 Attempting standard installation...
"!VENV_PYTHON!" -m pip install -r requirements.txt
if !errorlevel! == 0 (
    echo ✅ Dependencies installed successfully
    set "REQ_SUCCESS=true"
) else (
    echo ⚠️ Standard install failed, trying with trusted hosts...
    "!VENV_PYTHON!" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
    if !errorlevel! == 0 (
        echo ✅ Dependencies installed with trusted hosts
        set "REQ_SUCCESS=true"
    ) else (
        echo ⚠️ Virtual environment install failed, trying with system Python...
        !PYTHON_CMD! -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
        if !errorlevel! == 0 (
            echo ✅ Dependencies installed with system Python
            set "REQ_SUCCESS=true"
        ) else (
            echo ⚠️ Batch install failed, trying individual packages...

            REM Try installing packages individually
            for /f "usebackq delims=" %%a in ("requirements.txt") do (
                set "package=%%a"
                REM Skip empty lines and comments
                if not "!package!"=="" (
                    echo !package! | findstr /b "#" >nul
                    if !errorlevel! neq 0 (
                        echo 📦 Installing !package!...
                        "!VENV_PYTHON!" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org "!package!"
                        if !errorlevel! == 0 (
                            echo ✅ Installed !package!
                        ) else (
                            echo ⚠️ Failed to install !package!
                        )
                    )
                )
            )
            set "REQ_SUCCESS=partial"
        )
    )
)

if "!REQ_SUCCESS!"=="false" (
    echo ❌ Failed to install requirements
    echo.
    echo 🔧 Troubleshooting tips:
    echo • Check your internet connection
    echo • If behind corporate firewall/VPN, contact IT support
    echo • Try running as administrator
    echo • Check if requirements.txt exists and is readable
    echo • Try manually: !PYTHON_CMD! -m pip install flask
    echo.
    echo 📋 Contents of requirements.txt:
    type requirements.txt 2>nul
    pause
    goto :advanced
)

REM Step 5: Create data directory if missing
echo.
echo 🔍 Step 5: Checking data directory...
if not exist "data" (
    mkdir "data"
    echo ✅ Data directory created
) else (
    echo ✅ Data directory exists
)

REM Step 6: Test Flask installation
echo.
echo 🔍 Step 6: Testing Flask installation...
"!VENV_PYTHON!" -c "import flask; print('Flask ' + flask.__version__ + ' ready')" 2>nul
if !errorlevel! == 0 (
    for /f "delims=" %%i in ('"!VENV_PYTHON!" -c "import flask; print('Flask ' + flask.__version__ + ' ready')" 2^>nul') do echo ✅ %%i
) else (
    echo ⚠️ Flask test failed
)

REM Step 7: Test tkinter if GUI was available
if "!GUI_AVAILABLE!"=="true" (
    echo.
    echo 🔍 Step 7: Testing tkinter in virtual environment...
    "!VENV_PYTHON!" -c "import tkinter; print('tkinter available in virtual environment')" 2>nul
    if !errorlevel! == 0 (
        echo ✅ tkinter working in virtual environment
    ) else (
        echo ⚠️ tkinter not available in virtual environment
        echo 💡 This may affect GUI launcher functionality
    )
)

echo.
echo 🎉 Comprehensive setup and repair completed!
echo.
echo ✅ Summary:
echo   • Virtual environment: Ready
echo   • Dependencies: Installed
echo   • Data directory: Ready
echo   • Flask: Tested
if "!GUI_AVAILABLE!"=="true" (
    echo   • GUI support: Available
) else (
    echo   • GUI support: Not available (!GUI_REASON!)
)
echo.
echo 💡 You can now start the application from the main menu.
pause
goto :advanced

:diagnostics
echo ========================================
echo   AdhocLog - Diagnostics (Windows)
echo ========================================
echo.

echo System Information:
echo OS: Windows !WIN_VERSION!
echo Architecture: %PROCESSOR_ARCHITECTURE%
echo User: %USERNAME%
echo Project directory: !CD!
echo.

echo Python Information:
echo ✅ Selected Python: !PYTHON_CMD! (!PYTHON_VERSION!)
echo   Location:
for /f "delims=" %%i in ('where !PYTHON_CMD! 2^>nul') do echo     %%i
echo   pip available:
!PYTHON_CMD! -m pip --version >nul 2>&1 && echo     ✅ Yes || echo     ❌ No
echo.

echo Project Files:
if exist "requirements.txt" (echo ✅ requirements.txt: Found) else (echo ❌ requirements.txt: Missing)
if exist "app.py" (echo ✅ app.py: Found) else (echo ❌ app.py: Missing)
if exist "config.py" (echo ✅ config.py: Found) else (echo ❌ config.py: Missing)
if exist "data_manager.py" (echo ✅ data_manager.py: Found) else (echo ❌ data_manager.py: Missing)
if exist "templates" (echo ✅ templates/: Found) else (echo ❌ templates/: Missing)
echo.

echo Virtual Environment:
if exist "venv" (
    echo ✅ venv directory exists
    if exist "venv\Scripts\python.exe" (
        echo   ✅ Python executable: Found
        "venv\Scripts\python.exe" --version >nul 2>&1 && (
            for /f "tokens=2" %%v in ('"venv\Scripts\python.exe" --version 2^>^&1') do echo     Version: %%v
        ) || echo     ❌ Not working
    ) else (
        echo   ❌ Python executable: Missing
    )
    if exist "venv\Scripts\activate.bat" (echo   ✅ Activate script: Found) else (echo   ❌ Activate script: Missing)
) else (
    echo ❌ venv directory not found
)
echo.

echo Data Directory:
if exist "data" (
    echo ✅ data directory exists
    echo   Contents:
    dir "data\*.json" /b 2>nul | findstr . >nul && (
        for /f %%f in ('dir "data\*.json" /b 2^>nul ^| find /c /v ""') do echo     %%f JSON files found
    ) || echo     No JSON files found
) else (
    echo ❌ data directory not found
)
echo.

echo Permissions:
if exist "launch_app.bat" (echo ✅ launch_app.bat: Readable) else (echo ❌ launch_app.bat: Missing)
if exist "scripts\run.py" (echo ✅ scripts\run.py: Readable) else (echo ❌ scripts\run.py: Missing)
if exist "gui_launcher.py" (echo ✅ gui_launcher.py: Readable) else (echo ❌ gui_launcher.py: Missing)
echo.

echo Network:
echo Checking common ports...
netstat -an | findstr ":5000 " >nul && echo ❌ Port 5000: In use || echo ✅ Port 5000: Available
netstat -an | findstr ":8000 " >nul && echo ❌ Port 8000: In use || echo ✅ Port 8000: Available
echo.

echo Python Dependencies:
if exist "venv\Scripts\python.exe" (
    echo Using virtual environment Python:
    "venv\Scripts\python.exe" -c "import flask; print('✅ Flask: ' + flask.__version__)" 2>nul || echo ❌ Flask: Not installed
    "venv\Scripts\python.exe" -c "import werkzeug; print('✅ Werkzeug: ' + werkzeug.__version__)" 2>nul || echo ❌ Werkzeug: Not installed
    "venv\Scripts\python.exe" -c "import jinja2; print('✅ Jinja2: ' + jinja2.__version__)" 2>nul || echo ❌ Jinja2: Not installed
) else (
    echo Using system Python:
    !PYTHON_CMD! -c "import flask; print('✅ Flask: ' + flask.__version__)" 2>nul || echo ❌ Flask: Not installed
    !PYTHON_CMD! -c "import werkzeug; print('✅ Werkzeug: ' + werkzeug.__version__)" 2>nul || echo ❌ Werkzeug: Not installed
    !PYTHON_CMD! -c "import jinja2; print('✅ Jinja2: ' + jinja2.__version__)" 2>nul || echo ❌ Jinja2: Not installed
)

echo.
echo GUI Support:
!PYTHON_CMD! -c "import tkinter; print('✅ tkinter: Available')" 2>nul || echo ❌ tkinter: Not available
if "!GUI_AVAILABLE!"=="true" (
    echo ✅ GUI Launcher: Available
) else (
    echo ❌ GUI Launcher: Not available (!GUI_REASON!)
)

echo.
echo ========================================
echo Diagnostic complete!
echo ========================================
pause
goto :advanced

:sample_data
echo 📊 Creating sample data...
REM Use virtual environment Python if available
if exist "venv\Scripts\python.exe" (
    echo 📦 Using virtual environment Python...
    "venv\Scripts\python.exe" create_sample_data.py
) else (
    echo ⚠️ Virtual environment not found, using system Python...
    %PYTHON_CMD% create_sample_data.py
)
echo ✅ Sample data created
pause
goto :advanced

:clear_data
echo 🗑️ Clear All Data...
echo ⚠️ WARNING: This will delete all your task data!
set /p "confirm=Are you sure? (yes/no): "
if /i "%confirm%"=="yes" (
    del /q "data\tasks_*.json" 2>nul
    echo ✅ Data cleared
) else (
    echo ❌ Operation cancelled
)
pause
goto :advanced

:debug_mode
echo 🐛 Starting in debug mode...
set FLASK_DEBUG=1
REM Use virtual environment Python if available
if exist "venv\Scripts\python.exe" (
    echo 📦 Using virtual environment Python...
    echo [DEBUG] Starting debug mode with: "venv\Scripts\python.exe" scripts\run.py
    "venv\Scripts\python.exe" scripts\run.py
    if !errorlevel! neq 0 (
        echo.
        echo ❌ Debug mode failed to start properly
        echo [DEBUG] Exit code: !errorlevel!
        echo.
        pause
    )
) else (
    echo ⚠️ Virtual environment not found, using system Python...
    echo [DEBUG] Starting debug mode with: %PYTHON_CMD% scripts\run.py
    %PYTHON_CMD% scripts\run.py
    if !errorlevel! neq 0 (
        echo.
        echo ❌ Debug mode failed to start properly
        echo [DEBUG] Exit code: !errorlevel!
        echo.
        pause
    )
)
goto :end

:install_venv_requirements
set "venv_dir=%~1"
set "venv_python=%venv_dir%\Scripts\python.exe"

echo [INSTALL] Installing requirements in virtual environment...

REM Upgrade pip first
echo [INSTALL] Upgrading pip...
"%venv_python%" -m pip install --upgrade pip >nul 2>&1
if !errorlevel! == 0 (
    echo [SUCCESS] pip upgraded successfully
) else (
    echo [WARNING] pip upgrade failed, continuing with existing version
)

REM Install requirements
if exist "requirements.txt" (
    echo [INSTALL] Installing packages from requirements.txt...
    "%venv_python%" -m pip install -r requirements.txt >nul 2>&1
    if !errorlevel! == 0 (
        echo [SUCCESS] Requirements installed successfully
    ) else (
        echo [WARNING] Requirements installation failed, trying with trusted hosts...
        "%venv_python%" -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt >nul 2>&1
        if !errorlevel! == 0 (
            echo [SUCCESS] Requirements installed with trusted hosts
        ) else (
            echo [ERROR] Failed to install requirements in virtual environment
        )
    )
) else (
    echo [WARNING] requirements.txt not found
)
goto :eof

:cleanup_old_venvs
REM Clean up old project-based virtual environments (legacy cleanup)
if exist "venvs" (
    echo [CLEANUP] Cleaning up old project-based virtual environments...
    set "cleaned=0"

    for /d %%d in (venvs\*) do (
        set "venv_name=%%~nd"
        echo [CLEANUP] Removing old project venv: !venv_name!
        rmdir /s /q "%%d" 2>nul
        if !errorlevel! == 0 set /a cleaned+=1
    )

    REM Remove the entire venvs directory if we cleaned anything
    if !cleaned! gtr 0 (
        rmdir "venvs" 2>nul
        echo [SUCCESS] Cleaned up !cleaned! old virtual environment(s)
    ) else (
        echo [INFO] No old virtual environments to clean
    )
) else (
    echo [INFO] No old venvs directory found
)
goto :eof

:help
echo ❓ Help ^& Troubleshooting:
echo.
echo 📋 Common Issues ^& Solutions:
echo.
echo 🐍 Python Issues:
echo • Python not found:
echo   - Download from python.org (check "Add to PATH")
echo   - Microsoft Store: Search for "Python 3.11"
echo   - Anaconda: Download from anaconda.com
echo.
echo 🖥️ GUI Issues:
echo • tkinter not available:
echo   - Reinstall Python from python.org with default options
echo   - tkinter should be included automatically on Windows
echo • GUI launcher won't start:
echo   - Check if you're in SSH/Remote session
echo   - Try option 2 (Quick Start) instead
echo.
echo 📦 Dependency Issues:
echo • pip install fails:
echo   - Corporate network: Run setup (option 3 → 1)
echo   - Permission denied: Run as administrator
echo   - SSL errors: Setup configures trusted hosts automatically
echo • Virtual environment issues:
echo   - Setup will recreate broken environments automatically
echo.
echo 🌐 Network Issues:
echo • Port in use: App finds available port automatically
echo • Can't access app: Try http://localhost:PORT instead of 127.0.0.1
echo • Firewall blocking: Check Windows Firewall settings
echo.
echo 📁 File Structure:
echo • launch_app.bat - This Windows launcher script
echo • launch_app.sh - Unix/Linux launcher script
echo • gui_launcher.py - Graphical interface
echo • scripts\run.py - Command-line application starter
echo • scripts\ - Utility scripts for maintenance
echo • data\ - Your task data (JSON files)
echo • venv\ - Virtual environment (auto-created)
echo.
echo 🔧 Quick Fixes:
echo • Run diagnostics: Choose option 3 → 2
echo • Reset environment: Delete 'venv' folder and run setup
echo • Clear data: Choose option 3 → 4 (⚠️ destructive)
echo • Run as administrator: Right-click → "Run as administrator"
echo.
echo 🆘 Still Need Help?
echo • Check the README.md file for detailed instructions
echo • Run full diagnostics to identify specific issues
echo • Contact your system administrator with diagnostic output
echo • Check Windows Event Viewer for system errors
echo.
pause
goto :0

:end
echo.
echo 🎯 Application finished. Press any key to close this window.
pause >nul
