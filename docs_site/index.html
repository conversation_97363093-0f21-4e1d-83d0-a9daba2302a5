
<!doctype html>
<html lang="en" class="no-js">
  <head>

      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">

        <meta name="description" content="Professional task tracking and management application for enterprise productivity">


        <meta name="author" content="Cardinal Health">


        <link rel="canonical" href="https://localhost:8000/">



        <link rel="next" href="installation/">


      <link rel="icon" href="assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">



        <title>AdhocLog Documentation</title>



      <link rel="stylesheet" href="assets/stylesheets/main.7e37652d.min.css">


        <link rel="stylesheet" href="assets/stylesheets/palette.06af60db.min.css">












        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>



    <script>__md_scope=new URL(".",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>






  </head>









    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">


    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">


        <a href="#adhoclog-documentation" class="md-skip">
          Skip to content
        </a>

    </div>
    <div data-md-component="announce">

    </div>

      <div data-md-color-scheme="default" data-md-component="outdated" hidden>

      </div>




<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">


  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">

              Home

          </span>
        </div>
      </div>
    </div>


        <form class="md-header__option" data-md-component="palette">




    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">

      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>





    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">

      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>


</form>



      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>





        <label class="md-header__button md-icon" for="__search">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">

          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">

            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>

        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>

    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>



      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>

  </nav>

</header>

    <div class="md-container" data-md-component="container">





<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">








    <li class="md-tabs__item md-tabs__item--active">
      <a href="." class="md-tabs__link">





  Home

      </a>
    </li>










      <li class="md-tabs__item">
        <a href="installation/" class="md-tabs__link">



  Getting Started

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="user-guide/basic-usage/" class="md-tabs__link">



  User Guide

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="ai-features/analysis/" class="md-tabs__link">



  AI Features

        </a>
      </li>




    </ul>
  </div>
</nav>



      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">



              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">






<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">


  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>

    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">

    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>

  <ul class="md-nav__list" data-md-scrollfix>









    <li class="md-nav__item md-nav__item--active">

      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">





        <label class="md-nav__link md-nav__link--active" for="__toc">



  <span class="md-ellipsis">
    Home

  </span>


          <span class="md-nav__icon md-icon"></span>
        </label>

      <a href="." class="md-nav__link md-nav__link--active">



  <span class="md-ellipsis">
    Home

  </span>


      </a>



<nav class="md-nav md-nav--secondary" aria-label="Table of contents">






    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#what-is-adhoclog" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 What is AdhocLog?
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      ✨ Key Features
    </span>
  </a>

    <nav class="md-nav" aria-label="✨ Key Features">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#task-management" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Task Management
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#intelligent-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      🤖 Intelligent Analysis
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#analytics-reporting" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Analytics &amp; Reporting
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#enterprise-ready" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Enterprise Ready
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#quick-start" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Quick Start
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ Architecture Overview
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#documentation-sections" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Sections
    </span>
  </a>

    <nav class="md-nav" aria-label="📚 Documentation Sections">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#user-guide" class="md-nav__link">
    <span class="md-ellipsis">
      User Guide
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#ai-features" class="md-nav__link">
    <span class="md-ellipsis">
      AI Features
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#development" class="md-nav__link">
    <span class="md-ellipsis">
      Development
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#reference" class="md-nav__link">
    <span class="md-ellipsis">
      Reference
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#need-help" class="md-nav__link">
    <span class="md-ellipsis">
      🆘 Need Help?
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#license" class="md-nav__link">
    <span class="md-ellipsis">
      📄 License
    </span>
  </a>

</li>

    </ul>

</nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >


          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">



  <span class="md-ellipsis">
    Getting Started

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="installation/" class="md-nav__link">



  <span class="md-ellipsis">
    Installation

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="quick-start/" class="md-nav__link">



  <span class="md-ellipsis">
    Quick Start

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="configuration/" class="md-nav__link">



  <span class="md-ellipsis">
    Configuration

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >


          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">



  <span class="md-ellipsis">
    User Guide

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="user-guide/basic-usage/" class="md-nav__link">



  <span class="md-ellipsis">
    Basic Usage

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="user-guide/task-management/" class="md-nav__link">



  <span class="md-ellipsis">
    Task Management

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="user-guide/analytics/" class="md-nav__link">



  <span class="md-ellipsis">
    Analytics

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="user-guide/import-export/" class="md-nav__link">



  <span class="md-ellipsis">
    Import/Export

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >


          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">



  <span class="md-ellipsis">
    AI Features

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="ai-features/analysis/" class="md-nav__link">



  <span class="md-ellipsis">
    AI Analysis

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="ai-features/chatbot/" class="md-nav__link">



  <span class="md-ellipsis">
    Chatbot

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="ai-features/quick-reference/" class="md-nav__link">



  <span class="md-ellipsis">
    Quick Reference

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



  </ul>
</nav>
                  </div>
                </div>
              </div>



              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">


<nav class="md-nav md-nav--secondary" aria-label="Table of contents">






    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#what-is-adhoclog" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 What is AdhocLog?
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#key-features" class="md-nav__link">
    <span class="md-ellipsis">
      ✨ Key Features
    </span>
  </a>

    <nav class="md-nav" aria-label="✨ Key Features">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#task-management" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Task Management
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#intelligent-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      🤖 Intelligent Analysis
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#analytics-reporting" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Analytics &amp; Reporting
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#enterprise-ready" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Enterprise Ready
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#quick-start" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 Quick Start
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#architecture-overview" class="md-nav__link">
    <span class="md-ellipsis">
      🏗️ Architecture Overview
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#documentation-sections" class="md-nav__link">
    <span class="md-ellipsis">
      📚 Documentation Sections
    </span>
  </a>

    <nav class="md-nav" aria-label="📚 Documentation Sections">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#user-guide" class="md-nav__link">
    <span class="md-ellipsis">
      User Guide
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#ai-features" class="md-nav__link">
    <span class="md-ellipsis">
      AI Features
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#deployment" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#development" class="md-nav__link">
    <span class="md-ellipsis">
      Development
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#reference" class="md-nav__link">
    <span class="md-ellipsis">
      Reference
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#need-help" class="md-nav__link">
    <span class="md-ellipsis">
      🆘 Need Help?
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#license" class="md-nav__link">
    <span class="md-ellipsis">
      📄 License
    </span>
  </a>

</li>

    </ul>

</nav>
                  </div>
                </div>
              </div>



            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">








<h1 id="adhoclog-documentation">AdhocLog Documentation<a class="headerlink" href="#adhoclog-documentation" title="Permanent link">&para;</a></h1>
<p>Welcome to the comprehensive documentation for <strong>AdhocLog</strong>, a professional task tracking and management application designed for modern workplace productivity and time management.</p>
<h2 id="what-is-adhoclog">🚀 What is AdhocLog?<a class="headerlink" href="#what-is-adhoclog" title="Permanent link">&para;</a></h2>
<p>AdhocLog is a comprehensive task management system that helps teams and individuals track their daily work activities, manage time effectively, and gain insights into productivity patterns. Built for enterprise environments, it provides robust task tracking capabilities with intelligent analysis features to help optimize workflow and time allocation.</p>
<p>Key capabilities include:</p>
<ul>
<li><strong>Track and organize</strong> daily tasks with structured business categorization</li>
<li><strong>Analyze productivity patterns</strong> using intelligent analysis features</li>
<li><strong>Get intelligent suggestions</strong> through an integrated conversational assistant</li>
<li><strong>Export and share</strong> data seamlessly in standard formats</li>
<li><strong>Deploy easily</strong> in SharePoint and OneDrive environments</li>
</ul>
<h2 id="key-features">✨ Key Features<a class="headerlink" href="#key-features" title="Permanent link">&para;</a></h2>
<h3 id="task-management">📋 Task Management<a class="headerlink" href="#task-management" title="Permanent link">&para;</a></h3>
<ul>
<li>Create, edit, and organize tasks with rich metadata</li>
<li>Automatic time tracking and duration calculation</li>
<li>Flexible categorization and tagging system</li>
<li>Archive and restore functionality</li>
</ul>
<h3 id="intelligent-analysis">🤖 Intelligent Analysis<a class="headerlink" href="#intelligent-analysis" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Smart Classification</strong>: AI-assisted task categorization into business-relevant categories</li>
<li><strong>Interactive Assistant</strong>: Conversational interface for task management and queries</li>
<li><strong>Pattern Recognition</strong>: Identify trends and optimization opportunities in work patterns</li>
<li><strong>Duration Prediction</strong>: Intelligent time estimation based on task content and history</li>
</ul>
<h3 id="analytics-reporting">📊 Analytics &amp; Reporting<a class="headerlink" href="#analytics-reporting" title="Permanent link">&para;</a></h3>
<ul>
<li>Comprehensive productivity analytics</li>
<li>Time distribution analysis</li>
<li>Task completion trends</li>
<li>Exportable reports in multiple formats</li>
</ul>
<h3 id="enterprise-ready">🌐 Enterprise Ready<a class="headerlink" href="#enterprise-ready" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>SharePoint Integration</strong>: Seamless deployment in corporate environments</li>
<li><strong>Multi-user Support</strong>: User-specific data isolation</li>
<li><strong>Cross-platform</strong>: Works on Windows, macOS, and Linux</li>
<li><strong>Secure</strong>: Enterprise-grade security and data protection</li>
</ul>
<h2 id="quick-start">🎯 Quick Start<a class="headerlink" href="#quick-start" title="Permanent link">&para;</a></h2>
<p>Ready to get started? Choose your path:</p>
<div class="tabbed-set tabbed-alternate" data-tabs="1:3"><input checked="checked" id="__tabbed_1_1" name="__tabbed_1" type="radio" /><input id="__tabbed_1_2" name="__tabbed_1" type="radio" /><input id="__tabbed_1_3" name="__tabbed_1" type="radio" /><div class="tabbed-labels"><label for="__tabbed_1_1">New Users</label><label for="__tabbed_1_2">SharePoint Deployment</label><label for="__tabbed_1_3">Advanced Features</label></div>
<div class="tabbed-content">
<div class="tabbed-block">
<ol>
<li><strong><a href="installation/">Installation Guide</a></strong> - Set up AdhocLog on your system</li>
<li><strong><a href="quick-start/">Quick Start</a></strong> - Get up and running in minutes</li>
<li><strong><a href="user-guide/basic-usage/">Basic Usage</a></strong> - Learn the fundamentals</li>
</ol>
</div>
<div class="tabbed-block">
<ol>
<li><strong><a href="installation/">Installation Guide</a></strong> - Deploy in corporate environments</li>
<li><strong><a href="configuration/">Configuration</a></strong> - Configure for your organization</li>
<li><strong><a href="user-guide/basic-usage/">User Guide</a></strong> - Learn the fundamentals</li>
</ol>
</div>
<div class="tabbed-block">
<ol>
<li><strong><a href="ai-features/analysis/">AI Analysis</a></strong> - Understand intelligent features</li>
<li><strong><a href="user-guide/analytics/">Analytics</a></strong> - Track productivity insights</li>
<li><strong><a href="user-guide/task-management/">Task Management</a></strong> - Advanced task operations</li>
</ol>
</div>
</div>
</div>
<h2 id="architecture-overview">🏗️ Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<p>AdhocLog is built with modern web technologies:</p>
<ul>
<li><strong>Backend</strong>: Python Flask with modular architecture</li>
<li><strong>Frontend</strong>: Responsive HTML5/CSS3/JavaScript</li>
<li><strong>AI Engine</strong>: Custom AI analysis and chatbot systems</li>
<li><strong>Data Storage</strong>: JSON-based with backup/restore capabilities</li>
<li><strong>Deployment</strong>: Cross-platform with SharePoint integration</li>
</ul>
<h2 id="documentation-sections">📚 Documentation Sections<a class="headerlink" href="#documentation-sections" title="Permanent link">&para;</a></h2>
<h3 id="getting-started">Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h3>
<p>Learn how to install, configure, and start using AdhocLog effectively.</p>
<h3 id="user-guide">User Guide<a class="headerlink" href="#user-guide" title="Permanent link">&para;</a></h3>
<p>Comprehensive guides for all user-facing features and functionality.</p>
<h3 id="ai-features">AI Features<a class="headerlink" href="#ai-features" title="Permanent link">&para;</a></h3>
<p>Deep dive into the AI-powered capabilities that make AdhocLog unique.</p>
<h3 id="deployment">Deployment<a class="headerlink" href="#deployment" title="Permanent link">&para;</a></h3>
<p>Enterprise deployment guides for SharePoint, OneDrive, and local environments.</p>
<h3 id="development">Development<a class="headerlink" href="#development" title="Permanent link">&para;</a></h3>
<p>Technical documentation for developers and system administrators.</p>
<h3 id="reference">Reference<a class="headerlink" href="#reference" title="Permanent link">&para;</a></h3>
<p>API documentation, configuration options, and frequently asked questions.</p>
<h2 id="need-help">🆘 Need Help?<a class="headerlink" href="#need-help" title="Permanent link">&para;</a></h2>
<ul>
<li><strong><a href="installation/">Installation Guide</a></strong> - Setup and configuration help</li>
<li><strong><a href="user-guide/basic-usage/">User Guide</a></strong> - Complete usage documentation</li>
<li><strong><a href="https://github.com/jvbalcita/adhoc-log-app/issues">GitHub Issues</a></strong> - Report bugs or request features</li>
</ul>
<h2 id="license">📄 License<a class="headerlink" href="#license" title="Permanent link">&para;</a></h2>
<p>AdhocLog is developed for Cardinal Health. Please refer to your organization's software usage policies.</p>
<hr />
<p><em>Documentation last updated: August 2025</em></p>













              </article>
            </div>


<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>

          <button type="button" class="md-top md-icon" data-md-component="top" hidden>

  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>

      </main>

        <footer class="md-footer">

  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">

    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>


    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>

</div>

        <div class="md-social">








    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>

</div>

    </div>
  </div>
</footer>

    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>




      <script id="__config" type="application/json">{"base": ".", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>


      <script src="assets/javascripts/bundle.50899def.min.js"></script>


  </body>
</html>
