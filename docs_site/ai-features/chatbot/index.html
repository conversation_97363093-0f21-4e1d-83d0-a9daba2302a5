
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Professional task tracking and management application for enterprise productivity">
      
      
        <meta name="author" content="Cardinal Health">
      
      
        <link rel="canonical" href="https://localhost:8000/ai-features/chatbot/">
      
      
        <link rel="prev" href="../analysis/">
      
      
        <link rel="next" href="../quick-reference/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">
    
    
      
        <title>Chatbot - AdhocLog Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.7e37652d.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#ai-chatbot-adbot" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Chatbot
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../user-guide/basic-usage/" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../analysis/" class="md-tabs__link">
          
  
  
  AI Features

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/basic-usage/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/task-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/analytics/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Analytics
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/import-export/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    AI Features
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../analysis/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#accessing-adbot" class="md-nav__link">
    <span class="md-ellipsis">
      Accessing AdBot
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#first-interaction" class="md-nav__link">
    <span class="md-ellipsis">
      First Interaction
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#core-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Core Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Core Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-management" class="md-nav__link">
    <span class="md-ellipsis">
      Task Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-and-schedule-management" class="md-nav__link">
    <span class="md-ellipsis">
      Time and Schedule Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#productivity-insights" class="md-nav__link">
    <span class="md-ellipsis">
      Productivity Insights
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#natural-language-understanding" class="md-nav__link">
    <span class="md-ellipsis">
      Natural Language Understanding
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Natural Language Understanding">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#supported-query-types" class="md-nav__link">
    <span class="md-ellipsis">
      Supported Query Types
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#intent-recognition" class="md-nav__link">
    <span class="md-ellipsis">
      Intent Recognition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#conversation-features" class="md-nav__link">
    <span class="md-ellipsis">
      Conversation Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Conversation Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#context-awareness" class="md-nav__link">
    <span class="md-ellipsis">
      Context Awareness
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#smart-suggestions" class="md-nav__link">
    <span class="md-ellipsis">
      Smart Suggestions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#advanced-features" class="md-nav__link">
    <span class="md-ellipsis">
      Advanced Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Advanced Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-task-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Task Operations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#intelligent-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Intelligent Responses
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#response-types" class="md-nav__link">
    <span class="md-ellipsis">
      Response Types
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Response Types">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#informational-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Informational Responses
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Responses
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#effective-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Effective Communication
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-better-results" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Better Results
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#privacy-and-security" class="md-nav__link">
    <span class="md-ellipsis">
      Privacy and Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Privacy and Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#data-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Data Handling
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-features" class="md-nav__link">
    <span class="md-ellipsis">
      Security Features
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#example-conversations" class="md-nav__link">
    <span class="md-ellipsis">
      Example Conversations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Example Conversations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-planning" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Planning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#weekly-review" class="md-nav__link">
    <span class="md-ellipsis">
      Weekly Review
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../quick-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Reference
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Getting Started">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#accessing-adbot" class="md-nav__link">
    <span class="md-ellipsis">
      Accessing AdBot
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#first-interaction" class="md-nav__link">
    <span class="md-ellipsis">
      First Interaction
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#core-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Core Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Core Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-management" class="md-nav__link">
    <span class="md-ellipsis">
      Task Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-and-schedule-management" class="md-nav__link">
    <span class="md-ellipsis">
      Time and Schedule Management
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#productivity-insights" class="md-nav__link">
    <span class="md-ellipsis">
      Productivity Insights
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#natural-language-understanding" class="md-nav__link">
    <span class="md-ellipsis">
      Natural Language Understanding
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Natural Language Understanding">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#supported-query-types" class="md-nav__link">
    <span class="md-ellipsis">
      Supported Query Types
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#intent-recognition" class="md-nav__link">
    <span class="md-ellipsis">
      Intent Recognition
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#conversation-features" class="md-nav__link">
    <span class="md-ellipsis">
      Conversation Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Conversation Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#context-awareness" class="md-nav__link">
    <span class="md-ellipsis">
      Context Awareness
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#smart-suggestions" class="md-nav__link">
    <span class="md-ellipsis">
      Smart Suggestions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#advanced-features" class="md-nav__link">
    <span class="md-ellipsis">
      Advanced Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Advanced Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#multi-task-operations" class="md-nav__link">
    <span class="md-ellipsis">
      Multi-Task Operations
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#intelligent-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Intelligent Responses
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#response-types" class="md-nav__link">
    <span class="md-ellipsis">
      Response Types
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Response Types">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#informational-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Informational Responses
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#interactive-responses" class="md-nav__link">
    <span class="md-ellipsis">
      Interactive Responses
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#effective-communication" class="md-nav__link">
    <span class="md-ellipsis">
      Effective Communication
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-better-results" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Better Results
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#privacy-and-security" class="md-nav__link">
    <span class="md-ellipsis">
      Privacy and Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Privacy and Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#data-handling" class="md-nav__link">
    <span class="md-ellipsis">
      Data Handling
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security-features" class="md-nav__link">
    <span class="md-ellipsis">
      Security Features
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#example-conversations" class="md-nav__link">
    <span class="md-ellipsis">
      Example Conversations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Example Conversations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-planning" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Planning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#weekly-review" class="md-nav__link">
    <span class="md-ellipsis">
      Weekly Review
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


<h1 id="ai-chatbot-adbot">AI Chatbot - AdBot<a class="headerlink" href="#ai-chatbot-adbot" title="Permanent link">&para;</a></h1>
<p>AdBot is AdhocLog's intelligent conversational assistant that helps you manage tasks, analyze productivity, and get insights through natural language interactions.</p>
<h2 id="getting-started">Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h2>
<h3 id="accessing-adbot">Accessing AdBot<a class="headerlink" href="#accessing-adbot" title="Permanent link">&para;</a></h3>
<p>The chatbot is available throughout the AdhocLog interface:</p>
<ol>
<li><strong>Look</strong> for the chatbot icon (robot symbol) in the interface</li>
<li><strong>Click</strong> to open the chat window</li>
<li><strong>Type</strong> your question or request</li>
<li><strong>Press Enter</strong> or click Send to interact</li>
</ol>
<h3 id="first-interaction">First Interaction<a class="headerlink" href="#first-interaction" title="Permanent link">&para;</a></h3>
<p>When you first open AdBot, you'll see:</p>
<ul>
<li><strong>Welcome message</strong> introducing AdBot's capabilities</li>
<li><strong>Conversation suggestions</strong> to get you started</li>
<li><strong>Input field</strong> for typing your questions</li>
<li><strong>Send button</strong> to submit your messages</li>
</ul>
<h2 id="core-capabilities">Core Capabilities<a class="headerlink" href="#core-capabilities" title="Permanent link">&para;</a></h2>
<h3 id="task-management">Task Management<a class="headerlink" href="#task-management" title="Permanent link">&para;</a></h3>
<p><strong>Creating Tasks</strong>
- "Create a task for budget analysis"
- "Add a meeting task for 30 minutes"
- "I need to track time for report writing"</p>
<p><strong>Viewing Tasks</strong>
- "Show me today's tasks"
- "What tasks do I have this week?"
- "List my planning tasks"</p>
<p><strong>Task Queries</strong>
- "How many tasks did I complete yesterday?"
- "What's my longest task today?"
- "Show me tasks from last Monday"</p>
<h3 id="time-and-schedule-management">Time and Schedule Management<a class="headerlink" href="#time-and-schedule-management" title="Permanent link">&para;</a></h3>
<p><strong>Time Tracking</strong>
- "How much time do I have scheduled today?"
- "What's my total time for this week?"
- "Show me my time breakdown by classification"</p>
<p><strong>Schedule Analysis</strong>
- "When am I most productive?"
- "What's my average task duration?"
- "How much time do I spend in meetings?"</p>
<h3 id="productivity-insights">Productivity Insights<a class="headerlink" href="#productivity-insights" title="Permanent link">&para;</a></h3>
<p><strong>Analytics Queries</strong>
- "Show me my productivity stats"
- "What's my completion rate this month?"
- "Which classification takes most of my time?"</p>
<p><strong>Pattern Recognition</strong>
- "What are my common task patterns?"
- "When do I usually do planning tasks?"
- "What tasks often follow meetings?"</p>
<h2 id="natural-language-understanding">Natural Language Understanding<a class="headerlink" href="#natural-language-understanding" title="Permanent link">&para;</a></h2>
<h3 id="supported-query-types">Supported Query Types<a class="headerlink" href="#supported-query-types" title="Permanent link">&para;</a></h3>
<p><strong>Date References</strong>
- "today", "yesterday", "tomorrow"
- "this week", "last week", "next week"
- "Monday", "last Friday", "next Tuesday"
- Specific dates: "January 15", "2025-01-15"</p>
<p><strong>Time Expressions</strong>
- "morning", "afternoon", "evening"
- "this hour", "last 2 hours"
- Specific times: "9 AM", "2:30 PM"</p>
<p><strong>Classification References</strong>
- "planning tasks", "execution work"
- "meetings", "business support"
- "offline processing", "operational tasks"</p>
<p><strong>Status and Filters</strong>
- "completed tasks", "active tasks"
- "long tasks", "short tasks"
- "high priority", "urgent tasks"</p>
<h3 id="intent-recognition">Intent Recognition<a class="headerlink" href="#intent-recognition" title="Permanent link">&para;</a></h3>
<p>AdBot understands various ways to express the same request:</p>
<p><strong>Task Creation</strong>
- "Create a task", "Add a new task", "I need to track"
- "Log time for", "Record work on", "Start a task"</p>
<p><strong>Information Requests</strong>
- "Show me", "List", "What are", "How many"
- "Tell me about", "Display", "Find"</p>
<p><strong>Analysis Requests</strong>
- "Analyze", "Break down", "Summary of"
- "Trends", "Patterns", "Statistics"</p>
<h2 id="conversation-features">Conversation Features<a class="headerlink" href="#conversation-features" title="Permanent link">&para;</a></h2>
<h3 id="context-awareness">Context Awareness<a class="headerlink" href="#context-awareness" title="Permanent link">&para;</a></h3>
<p>AdBot maintains conversation context:</p>
<p><strong>Follow-up Questions</strong>
- User: "Show me today's tasks"
- AdBot: [Lists tasks]
- User: "How long will these take?"
- AdBot: [Calculates total time from previous context]</p>
<p><strong>Reference Resolution</strong>
- "Show me more details about that task"
- "What about yesterday?"
- "Can you break that down further?"</p>
<h3 id="smart-suggestions">Smart Suggestions<a class="headerlink" href="#smart-suggestions" title="Permanent link">&para;</a></h3>
<p><strong>Proactive Recommendations</strong>
- Task creation suggestions based on patterns
- Productivity tips based on your data
- Optimization recommendations</p>
<p><strong>Conversation Starters</strong>
- Common queries you might want to ask
- Helpful commands for new users
- Quick access to frequent operations</p>
<h2 id="advanced-features">Advanced Features<a class="headerlink" href="#advanced-features" title="Permanent link">&para;</a></h2>
<h3 id="multi-task-operations">Multi-Task Operations<a class="headerlink" href="#multi-task-operations" title="Permanent link">&para;</a></h3>
<p><strong>Bulk Queries</strong>
- "Show me all planning tasks from last week"
- "List every meeting longer than 30 minutes"
- "Find tasks with 'budget' in the title"</p>
<p><strong>Comparative Analysis</strong>
- "Compare this week to last week"
- "How does my Monday compare to Friday?"
- "Show me trends over the last month"</p>
<h3 id="intelligent-responses">Intelligent Responses<a class="headerlink" href="#intelligent-responses" title="Permanent link">&para;</a></h3>
<p><strong>Data Visualization</strong>
- Text-based charts and summaries
- Formatted tables for easy reading
- Highlighted key insights</p>
<p><strong>Actionable Suggestions</strong>
- "Based on your patterns, consider..."
- "You might want to..."
- "Here's what I noticed..."</p>
<h2 id="response-types">Response Types<a class="headerlink" href="#response-types" title="Permanent link">&para;</a></h2>
<h3 id="informational-responses">Informational Responses<a class="headerlink" href="#informational-responses" title="Permanent link">&para;</a></h3>
<p><strong>Task Lists</strong>
<div class="highlight"><pre><span></span><code><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>📋 Today&#39;s Tasks:
<a id="__codelineno-0-2" name="__codelineno-0-2" href="#__codelineno-0-2"></a>• Budget Analysis (Planning) - 60 min
<a id="__codelineno-0-3" name="__codelineno-0-3" href="#__codelineno-0-3"></a>• Team Meeting (Business Support) - 30 min
<a id="__codelineno-0-4" name="__codelineno-0-4" href="#__codelineno-0-4"></a>• Report Writing (Offline Processing) - 90 min
<a id="__codelineno-0-5" name="__codelineno-0-5" href="#__codelineno-0-5"></a>
<a id="__codelineno-0-6" name="__codelineno-0-6" href="#__codelineno-0-6"></a>Total: 3 tasks, 180 minutes
</code></pre></div></p>
<p><strong>Analytics Summaries</strong>
<div class="highlight"><pre><span></span><code><a id="__codelineno-1-1" name="__codelineno-1-1" href="#__codelineno-1-1"></a>📊 This Week&#39;s Productivity:
<a id="__codelineno-1-2" name="__codelineno-1-2" href="#__codelineno-1-2"></a>• Tasks Completed: 15
<a id="__codelineno-1-3" name="__codelineno-1-3" href="#__codelineno-1-3"></a>• Total Time: 12.5 hours
<a id="__codelineno-1-4" name="__codelineno-1-4" href="#__codelineno-1-4"></a>• Top Classification: Execution (40%)
<a id="__codelineno-1-5" name="__codelineno-1-5" href="#__codelineno-1-5"></a>• Completion Rate: 94%
</code></pre></div></p>
<h3 id="interactive-responses">Interactive Responses<a class="headerlink" href="#interactive-responses" title="Permanent link">&para;</a></h3>
<p><strong>Follow-up Questions</strong>
- "Would you like me to break this down by day?"
- "Should I show you the details for any specific task?"
- "Do you want to see trends over a longer period?"</p>
<p><strong>Action Suggestions</strong>
- "I can help you create a similar task"
- "Would you like to set a reminder?"
- "Should I analyze this pattern further?"</p>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="effective-communication">Effective Communication<a class="headerlink" href="#effective-communication" title="Permanent link">&para;</a></h3>
<p><strong>Be Specific</strong>
- "Show me planning tasks from last Tuesday" vs. "Show me tasks"
- "How much time did I spend on meetings this week?" vs. "Time spent?"</p>
<p><strong>Use Natural Language</strong>
- "What did I work on yesterday?" (natural)
- "SELECT * FROM tasks WHERE date = yesterday" (too technical)</p>
<p><strong>Ask Follow-up Questions</strong>
- Build on previous responses
- Drill down into interesting insights
- Explore patterns and trends</p>
<h3 id="getting-better-results">Getting Better Results<a class="headerlink" href="#getting-better-results" title="Permanent link">&para;</a></h3>
<p><strong>Provide Context</strong>
- "I'm planning next week, show me typical Monday tasks"
- "For my performance review, what's my completion rate?"</p>
<p><strong>Use Conversational Flow</strong>
- Start broad, then get specific
- Ask for clarification when needed
- Build on previous answers</p>
<h2 id="privacy-and-security">Privacy and Security<a class="headerlink" href="#privacy-and-security" title="Permanent link">&para;</a></h2>
<h3 id="data-handling">Data Handling<a class="headerlink" href="#data-handling" title="Permanent link">&para;</a></h3>
<p><strong>Local Processing</strong>
- All conversations processed locally
- No external data transmission
- Complete privacy protection</p>
<p><strong>Conversation History</strong>
- Stored locally for context
- Can be cleared if desired
- No cloud storage or backup</p>
<h3 id="security-features">Security Features<a class="headerlink" href="#security-features" title="Permanent link">&para;</a></h3>
<p><strong>Access Control</strong>
- Same security as main application
- User-specific data access only
- No cross-user information sharing</p>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues">Common Issues<a class="headerlink" href="#common-issues" title="Permanent link">&para;</a></h3>
<p><strong>AdBot Not Responding</strong>
- Check internet connection (for interface loading)
- Refresh the page
- Clear browser cache if needed</p>
<p><strong>Unclear Responses</strong>
- Rephrase your question more specifically
- Provide additional context
- Break complex requests into simpler parts</p>
<p><strong>Missing Data in Responses</strong>
- Verify you have tasks in the requested time period
- Check if filters are too restrictive
- Ensure task data is properly saved</p>
<h3 id="getting-help">Getting Help<a class="headerlink" href="#getting-help" title="Permanent link">&para;</a></h3>
<p><strong>Built-in Help</strong>
- Type "help" for available commands
- Ask "What can you do?" for capabilities overview
- Use "examples" to see sample queries</p>
<p><strong>Conversation Tips</strong>
- Start with simple questions
- Build complexity gradually
- Use the suggestion prompts when available</p>
<h2 id="example-conversations">Example Conversations<a class="headerlink" href="#example-conversations" title="Permanent link">&para;</a></h2>
<h3 id="daily-planning">Daily Planning<a class="headerlink" href="#daily-planning" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><a id="__codelineno-2-1" name="__codelineno-2-1" href="#__codelineno-2-1"></a>User: What do I have planned for today?
<a id="__codelineno-2-2" name="__codelineno-2-2" href="#__codelineno-2-2"></a>AdBot: You have 4 tasks scheduled:
<a id="__codelineno-2-3" name="__codelineno-2-3" href="#__codelineno-2-3"></a>• Morning standup (30 min)
<a id="__codelineno-2-4" name="__codelineno-2-4" href="#__codelineno-2-4"></a>• Code review (60 min) 
<a id="__codelineno-2-5" name="__codelineno-2-5" href="#__codelineno-2-5"></a>• Documentation update (45 min)
<a id="__codelineno-2-6" name="__codelineno-2-6" href="#__codelineno-2-6"></a>• Client call (30 min)
<a id="__codelineno-2-7" name="__codelineno-2-7" href="#__codelineno-2-7"></a>Total: 165 minutes
<a id="__codelineno-2-8" name="__codelineno-2-8" href="#__codelineno-2-8"></a>
<a id="__codelineno-2-9" name="__codelineno-2-9" href="#__codelineno-2-9"></a>User: When am I usually most productive?
<a id="__codelineno-2-10" name="__codelineno-2-10" href="#__codelineno-2-10"></a>AdBot: Based on your patterns, you&#39;re most productive between 9-11 AM and 2-4 PM. Consider scheduling complex tasks during these times.
</code></pre></div>
<h3 id="weekly-review">Weekly Review<a class="headerlink" href="#weekly-review" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><a id="__codelineno-3-1" name="__codelineno-3-1" href="#__codelineno-3-1"></a>User: How was my productivity this week?
<a id="__codelineno-3-2" name="__codelineno-3-2" href="#__codelineno-3-2"></a>AdBot: Great week! You completed 18 of 20 tasks (90% completion rate). You spent 32 hours total, with most time on Execution tasks (45%). Your estimation accuracy improved to 85%.
<a id="__codelineno-3-3" name="__codelineno-3-3" href="#__codelineno-3-3"></a>
<a id="__codelineno-3-4" name="__codelineno-3-4" href="#__codelineno-3-4"></a>User: What should I focus on next week?
<a id="__codelineno-3-5" name="__codelineno-3-5" href="#__codelineno-3-5"></a>AdBot: I notice you have fewer Planning tasks lately. Consider dedicating time early in the week for strategic work, as this typically improves your execution efficiency.
</code></pre></div>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.50899def.min.js"></script>
      
    
  </body>
</html>