
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Professional task tracking and management application for enterprise productivity">
      
      
        <meta name="author" content="Cardinal Health">
      
      
        <link rel="canonical" href="https://localhost:8000/ai-features/analysis/">
      
      
        <link rel="prev" href="../../user-guide/import-export/">
      
      
        <link rel="next" href="../chatbot/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">
    
    
      
        <title>AI Analysis - AdhocLog Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.7e37652d.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#ai-analysis-features" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              AI Analysis
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../user-guide/basic-usage/" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="./" class="md-tabs__link">
          
  
  
  AI Features

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/basic-usage/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/task-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/analytics/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Analytics
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../user-guide/import-export/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    AI Features
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#smart-classification" class="md-nav__link">
    <span class="md-ellipsis">
      Smart Classification
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Smart Classification">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#how-it-works" class="md-nav__link">
    <span class="md-ellipsis">
      How It Works
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#classification-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Classification Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#confidence-scoring" class="md-nav__link">
    <span class="md-ellipsis">
      Confidence Scoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#duration-prediction" class="md-nav__link">
    <span class="md-ellipsis">
      Duration Prediction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duration Prediction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intelligent-time-estimation" class="md-nav__link">
    <span class="md-ellipsis">
      Intelligent Time Estimation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#accuracy-improvement" class="md-nav__link">
    <span class="md-ellipsis">
      Accuracy Improvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#priority-detection" class="md-nav__link">
    <span class="md-ellipsis">
      Priority Detection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Priority Detection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#urgency-assessment" class="md-nav__link">
    <span class="md-ellipsis">
      Urgency Assessment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#priority-scoring" class="md-nav__link">
    <span class="md-ellipsis">
      Priority Scoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#similar-task-matching" class="md-nav__link">
    <span class="md-ellipsis">
      Similar Task Matching
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Similar Task Matching">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#semantic-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Semantic Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#usage-examples" class="md-nav__link">
    <span class="md-ellipsis">
      Usage Examples
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pattern-recognition" class="md-nav__link">
    <span class="md-ellipsis">
      Pattern Recognition
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Pattern Recognition">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#workflow-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Workflow Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#predictive-suggestions" class="md-nav__link">
    <span class="md-ellipsis">
      Predictive Suggestions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#conversational-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Conversational Interface
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Conversational Interface">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#natural-language-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Natural Language Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#conversation-features" class="md-nav__link">
    <span class="md-ellipsis">
      Conversation Features
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#privacy-and-data-security" class="md-nav__link">
    <span class="md-ellipsis">
      Privacy and Data Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Privacy and Data Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#local-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Local Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-usage" class="md-nav__link">
    <span class="md-ellipsis">
      Data Usage
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maximizing-ai-benefits" class="md-nav__link">
    <span class="md-ellipsis">
      Maximizing AI Benefits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#continuous-improvement" class="md-nav__link">
    <span class="md-ellipsis">
      Continuous Improvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ai-suggestions-not-accurate" class="md-nav__link">
    <span class="md-ellipsis">
      AI Suggestions Not Accurate
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../chatbot/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../quick-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Reference
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#overview" class="md-nav__link">
    <span class="md-ellipsis">
      Overview
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#smart-classification" class="md-nav__link">
    <span class="md-ellipsis">
      Smart Classification
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Smart Classification">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#how-it-works" class="md-nav__link">
    <span class="md-ellipsis">
      How It Works
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#classification-logic" class="md-nav__link">
    <span class="md-ellipsis">
      Classification Logic
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#confidence-scoring" class="md-nav__link">
    <span class="md-ellipsis">
      Confidence Scoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#duration-prediction" class="md-nav__link">
    <span class="md-ellipsis">
      Duration Prediction
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Duration Prediction">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#intelligent-time-estimation" class="md-nav__link">
    <span class="md-ellipsis">
      Intelligent Time Estimation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#accuracy-improvement" class="md-nav__link">
    <span class="md-ellipsis">
      Accuracy Improvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#priority-detection" class="md-nav__link">
    <span class="md-ellipsis">
      Priority Detection
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Priority Detection">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#urgency-assessment" class="md-nav__link">
    <span class="md-ellipsis">
      Urgency Assessment
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#priority-scoring" class="md-nav__link">
    <span class="md-ellipsis">
      Priority Scoring
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#similar-task-matching" class="md-nav__link">
    <span class="md-ellipsis">
      Similar Task Matching
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Similar Task Matching">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#semantic-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Semantic Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#usage-examples" class="md-nav__link">
    <span class="md-ellipsis">
      Usage Examples
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#pattern-recognition" class="md-nav__link">
    <span class="md-ellipsis">
      Pattern Recognition
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Pattern Recognition">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#workflow-analysis" class="md-nav__link">
    <span class="md-ellipsis">
      Workflow Analysis
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#predictive-suggestions" class="md-nav__link">
    <span class="md-ellipsis">
      Predictive Suggestions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#conversational-interface" class="md-nav__link">
    <span class="md-ellipsis">
      Conversational Interface
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Conversational Interface">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#natural-language-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Natural Language Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#conversation-features" class="md-nav__link">
    <span class="md-ellipsis">
      Conversation Features
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#privacy-and-data-security" class="md-nav__link">
    <span class="md-ellipsis">
      Privacy and Data Security
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Privacy and Data Security">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#local-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Local Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-usage" class="md-nav__link">
    <span class="md-ellipsis">
      Data Usage
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#maximizing-ai-benefits" class="md-nav__link">
    <span class="md-ellipsis">
      Maximizing AI Benefits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#continuous-improvement" class="md-nav__link">
    <span class="md-ellipsis">
      Continuous Improvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#ai-suggestions-not-accurate" class="md-nav__link">
    <span class="md-ellipsis">
      AI Suggestions Not Accurate
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Issues
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


<h1 id="ai-analysis-features">AI Analysis Features<a class="headerlink" href="#ai-analysis-features" title="Permanent link">&para;</a></h1>
<p>AdhocLog includes intelligent analysis capabilities that help optimize your task management and productivity through natural language processing and pattern recognition.</p>
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>The AI analysis engine provides:</p>
<ul>
<li><strong>Smart Classification</strong>: Automatic task categorization assistance</li>
<li><strong>Duration Prediction</strong>: Intelligent time estimation based on task content</li>
<li><strong>Priority Detection</strong>: Automatic urgency assessment using natural language processing</li>
<li><strong>Similar Task Matching</strong>: Find related tasks from your history using semantic analysis</li>
<li><strong>Pattern Recognition</strong>: Identify trends and optimization opportunities</li>
</ul>
<h2 id="smart-classification">Smart Classification<a class="headerlink" href="#smart-classification" title="Permanent link">&para;</a></h2>
<h3 id="how-it-works">How It Works<a class="headerlink" href="#how-it-works" title="Permanent link">&para;</a></h3>
<p>The AI analysis engine examines your task title and description to suggest the most appropriate classification from the five business categories:</p>
<p><strong>Classification Options</strong>
- Planning
- Offline Processing<br />
- Execution
- Business Support Activities
- Operational Project Involvement</p>
<h3 id="classification-logic">Classification Logic<a class="headerlink" href="#classification-logic" title="Permanent link">&para;</a></h3>
<p><strong>Planning Tasks</strong>
- Keywords: strategy, design, architecture, plan, blueprint
- Examples: "Design system architecture", "Strategic planning session"</p>
<p><strong>Offline Processing</strong>
- Keywords: analysis, report, data, process, document
- Examples: "Generate monthly report", "Analyze sales data"</p>
<p><strong>Execution</strong>
- Keywords: implement, build, develop, create, execute
- Examples: "Develop new feature", "Implement solution"</p>
<p><strong>Business Support Activities</strong>
- Keywords: meeting, coordinate, admin, support, communicate
- Examples: "Team meeting", "Coordinate project resources"</p>
<p><strong>Operational Project Involvement</strong>
- Keywords: monitor, manage, oversee, track, project
- Examples: "Monitor system performance", "Project status review"</p>
<h3 id="confidence-scoring">Confidence Scoring<a class="headerlink" href="#confidence-scoring" title="Permanent link">&para;</a></h3>
<p>The AI provides confidence scores for classification suggestions:</p>
<ul>
<li><strong>High Confidence (80-100%)</strong>: Strong keyword matches and clear context</li>
<li><strong>Medium Confidence (60-79%)</strong>: Some indicators present, manual review recommended</li>
<li><strong>Low Confidence (0-59%)</strong>: Unclear context, manual classification recommended</li>
</ul>
<h2 id="duration-prediction">Duration Prediction<a class="headerlink" href="#duration-prediction" title="Permanent link">&para;</a></h2>
<h3 id="intelligent-time-estimation">Intelligent Time Estimation<a class="headerlink" href="#intelligent-time-estimation" title="Permanent link">&para;</a></h3>
<p>The AI analyzes task content to predict realistic duration estimates:</p>
<p><strong>Factors Considered</strong>
- Task complexity indicators in title and description
- Historical data from similar tasks
- Keyword analysis for scope and effort
- User's past estimation accuracy</p>
<p><strong>Estimation Categories</strong>
- <strong>Quick Tasks</strong>: 15-30 minutes (simple updates, brief communications)
- <strong>Standard Tasks</strong>: 30-90 minutes (meetings, basic analysis, routine work)
- <strong>Complex Tasks</strong>: 90-240 minutes (detailed analysis, development, planning)</p>
<h3 id="accuracy-improvement">Accuracy Improvement<a class="headerlink" href="#accuracy-improvement" title="Permanent link">&para;</a></h3>
<p>The system learns from your actual vs. estimated time patterns:</p>
<ul>
<li><strong>Tracks</strong> estimation accuracy over time</li>
<li><strong>Adjusts</strong> future predictions based on your patterns</li>
<li><strong>Identifies</strong> consistently under/over-estimated task types</li>
<li><strong>Provides</strong> feedback to improve estimation skills</li>
</ul>
<h2 id="priority-detection">Priority Detection<a class="headerlink" href="#priority-detection" title="Permanent link">&para;</a></h2>
<h3 id="urgency-assessment">Urgency Assessment<a class="headerlink" href="#urgency-assessment" title="Permanent link">&para;</a></h3>
<p>The AI automatically detects priority levels based on natural language cues:</p>
<p><strong>High Priority Indicators</strong>
- Keywords: urgent, asap, critical, emergency, immediate
- Time phrases: "due today", "by end of day", "ASAP"
- Context clues: system issues, bugs, client escalations</p>
<p><strong>Medium Priority Indicators</strong>
- Keywords: important, soon, needed, deadline
- Time phrases: "this week", "by Friday", "upcoming"
- Context clues: project milestones, scheduled deliverables</p>
<p><strong>Low Priority Indicators</strong>
- Keywords: when possible, eventually, nice to have
- Time phrases: "next month", "future", "someday"
- Context clues: improvements, optimizations, research</p>
<h3 id="priority-scoring">Priority Scoring<a class="headerlink" href="#priority-scoring" title="Permanent link">&para;</a></h3>
<p>Priority is calculated on a scale of 0.0 to 1.0:</p>
<ul>
<li><strong>0.8-1.0</strong>: High priority (urgent action required)</li>
<li><strong>0.4-0.7</strong>: Medium priority (standard importance)</li>
<li><strong>0.0-0.3</strong>: Low priority (when time permits)</li>
</ul>
<h2 id="similar-task-matching">Similar Task Matching<a class="headerlink" href="#similar-task-matching" title="Permanent link">&para;</a></h2>
<h3 id="semantic-analysis">Semantic Analysis<a class="headerlink" href="#semantic-analysis" title="Permanent link">&para;</a></h3>
<p>The AI finds related tasks from your history using advanced text analysis:</p>
<p><strong>Matching Criteria</strong>
- <strong>Semantic similarity</strong>: Understanding meaning beyond exact word matches
- <strong>Classification alignment</strong>: Tasks in the same or related categories
- <strong>Context analysis</strong>: Similar work patterns and descriptions
- <strong>Temporal patterns</strong>: Tasks that often occur together</p>
<p><strong>Benefits</strong>
- <strong>Learn</strong> from past experiences
- <strong>Reuse</strong> successful approaches
- <strong>Estimate</strong> time based on similar tasks
- <strong>Identify</strong> recurring work patterns</p>
<h3 id="usage-examples">Usage Examples<a class="headerlink" href="#usage-examples" title="Permanent link">&para;</a></h3>
<p><strong>Finding Similar Tasks</strong>
- Input: "Quarterly budget analysis"
- Matches: "Monthly budget review", "Annual financial analysis", "Budget variance report"</p>
<p><strong>Time Estimation</strong>
- Uses duration from similar tasks to improve estimates
- Weighted average of 3-5 most similar tasks
- Adjusts for complexity differences</p>
<h2 id="pattern-recognition">Pattern Recognition<a class="headerlink" href="#pattern-recognition" title="Permanent link">&para;</a></h2>
<h3 id="workflow-analysis">Workflow Analysis<a class="headerlink" href="#workflow-analysis" title="Permanent link">&para;</a></h3>
<p>The AI identifies patterns in your work habits:</p>
<p><strong>Task Sequences</strong>
- Common task combinations
- Typical follow-up activities
- Project workflow patterns</p>
<p><strong>Time Patterns</strong>
- Peak productivity hours
- Optimal task scheduling
- Energy level correlations</p>
<p><strong>Efficiency Insights</strong>
- Tasks that consistently take longer than estimated
- Work types that are most/least efficient
- Opportunities for process improvement</p>
<h3 id="predictive-suggestions">Predictive Suggestions<a class="headerlink" href="#predictive-suggestions" title="Permanent link">&para;</a></h3>
<p>Based on pattern analysis, the AI can suggest:</p>
<p><strong>Next Task Predictions</strong>
- Likely follow-up tasks based on current work
- Common task sequences in your workflow
- Project completion requirements</p>
<p><strong>Optimization Recommendations</strong>
- Better task scheduling based on energy patterns
- Process improvements for recurring tasks
- Time management suggestions</p>
<h2 id="conversational-interface">Conversational Interface<a class="headerlink" href="#conversational-interface" title="Permanent link">&para;</a></h2>
<h3 id="natural-language-processing">Natural Language Processing<a class="headerlink" href="#natural-language-processing" title="Permanent link">&para;</a></h3>
<p>The AI chatbot understands natural language queries about your tasks:</p>
<p><strong>Supported Queries</strong>
- "Show me tasks from last week"
- "How much time did I spend on planning tasks?"
- "What meetings do I have today?"
- "Create a task for budget analysis"</p>
<p><strong>Response Types</strong>
- <strong>Informational</strong>: Answers about your tasks and productivity
- <strong>Actionable</strong>: Suggestions for task creation or optimization
- <strong>Analytical</strong>: Insights about work patterns and efficiency</p>
<h3 id="conversation-features">Conversation Features<a class="headerlink" href="#conversation-features" title="Permanent link">&para;</a></h3>
<p><strong>Context Awareness</strong>
- Remembers conversation history
- Understands follow-up questions
- Maintains context across interactions</p>
<p><strong>Smart Suggestions</strong>
- Proactive recommendations based on your work patterns
- Helpful tips for productivity improvement
- Reminders about important tasks or deadlines</p>
<h2 id="privacy-and-data-security">Privacy and Data Security<a class="headerlink" href="#privacy-and-data-security" title="Permanent link">&para;</a></h2>
<h3 id="local-processing">Local Processing<a class="headerlink" href="#local-processing" title="Permanent link">&para;</a></h3>
<p>All AI analysis is performed locally:</p>
<ul>
<li><strong>No external API calls</strong> for sensitive data</li>
<li><strong>No cloud processing</strong> of your task information</li>
<li><strong>Complete data privacy</strong> within your environment</li>
<li><strong>Offline capability</strong> for all AI features</li>
</ul>
<h3 id="data-usage">Data Usage<a class="headerlink" href="#data-usage" title="Permanent link">&para;</a></h3>
<p>The AI only uses:</p>
<ul>
<li>Your own task data for analysis</li>
<li>Local pattern recognition</li>
<li>Historical performance for improvement</li>
<li>No external data sources or comparisons</li>
</ul>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="maximizing-ai-benefits">Maximizing AI Benefits<a class="headerlink" href="#maximizing-ai-benefits" title="Permanent link">&para;</a></h3>
<p><strong>Provide Detailed Descriptions</strong>
- Include specific details about tasks
- Use clear, descriptive language
- Mention key requirements or constraints</p>
<p><strong>Consistent Classification</strong>
- Use AI suggestions as starting points
- Maintain consistency in your choices
- Review and adjust classifications as needed</p>
<p><strong>Track Accuracy</strong>
- Monitor AI prediction accuracy
- Provide feedback through actual time tracking
- Adjust estimates based on experience</p>
<h3 id="continuous-improvement">Continuous Improvement<a class="headerlink" href="#continuous-improvement" title="Permanent link">&para;</a></h3>
<p><strong>Regular Review</strong>
- Check AI suggestions against your experience
- Identify areas where AI is most/least helpful
- Adjust your task description style for better AI analysis</p>
<p><strong>Pattern Awareness</strong>
- Pay attention to identified patterns
- Use insights to optimize your workflow
- Implement suggested improvements gradually</p>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="ai-suggestions-not-accurate">AI Suggestions Not Accurate<a class="headerlink" href="#ai-suggestions-not-accurate" title="Permanent link">&para;</a></h3>
<p><strong>Improve Input Quality</strong>
- Provide more detailed task descriptions
- Use specific, descriptive language
- Include context about task complexity</p>
<p><strong>Review Historical Data</strong>
- Ensure sufficient task history for pattern recognition
- Verify accuracy of past task classifications
- Update incorrect historical data if needed</p>
<h3 id="performance-issues">Performance Issues<a class="headerlink" href="#performance-issues" title="Permanent link">&para;</a></h3>
<p><strong>Large Data Sets</strong>
- AI analysis may be slower with extensive task history
- Consider archiving very old tasks
- Monitor system performance during analysis</p>
<p><strong>Memory Usage</strong>
- AI processing requires adequate system memory
- Close unnecessary applications during analysis
- Consider system upgrades for better performance</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.50899def.min.js"></script>
      
    
  </body>
</html>