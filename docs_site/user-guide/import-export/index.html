
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Professional task tracking and management application for enterprise productivity">
      
      
        <meta name="author" content="Cardinal Health">
      
      
        <link rel="canonical" href="https://localhost:8000/user-guide/import-export/">
      
      
        <link rel="prev" href="../analytics/">
      
      
        <link rel="next" href="../../ai-features/analysis/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">
    
    
      
        <title>Import/Export - AdhocLog Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.7e37652d.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#import-and-export" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Import/Export
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../basic-usage/" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../ai-features/analysis/" class="md-tabs__link">
          
  
  
  AI Features

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../basic-usage/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../task-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../analytics/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Analytics
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#export-features" class="md-nav__link">
    <span class="md-ellipsis">
      Export Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Export Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-export" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Export
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#filtered-exports" class="md-nav__link">
    <span class="md-ellipsis">
      Filtered Exports
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#report-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Report Generation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#import-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Import Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Import Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-import" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Import
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Data Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#backup-and-restore" class="md-nav__link">
    <span class="md-ellipsis">
      Backup and Restore
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backup and Restore">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-backups" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Backups
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#restoring-data" class="md-nav__link">
    <span class="md-ellipsis">
      Restoring Data
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-migration" class="md-nav__link">
    <span class="md-ellipsis">
      Data Migration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Migration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#moving-between-systems" class="md-nav__link">
    <span class="md-ellipsis">
      Moving Between Systems
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-formats" class="md-nav__link">
    <span class="md-ellipsis">
      Data Formats
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Formats">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Specifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#field-definitions" class="md-nav__link">
    <span class="md-ellipsis">
      Field Definitions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#export-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Export Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#import-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Import Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Considerations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#regular-exports" class="md-nav__link">
    <span class="md-ellipsis">
      Regular Exports
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-quality" class="md-nav__link">
    <span class="md-ellipsis">
      Data Quality
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security" class="md-nav__link">
    <span class="md-ellipsis">
      Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    AI Features
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/analysis/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/chatbot/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/quick-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Reference
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#export-features" class="md-nav__link">
    <span class="md-ellipsis">
      Export Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Export Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-export" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Export
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#filtered-exports" class="md-nav__link">
    <span class="md-ellipsis">
      Filtered Exports
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#report-generation" class="md-nav__link">
    <span class="md-ellipsis">
      Report Generation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#import-capabilities" class="md-nav__link">
    <span class="md-ellipsis">
      Import Capabilities
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Import Capabilities">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-import" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Import
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-validation" class="md-nav__link">
    <span class="md-ellipsis">
      Data Validation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#backup-and-restore" class="md-nav__link">
    <span class="md-ellipsis">
      Backup and Restore
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Backup and Restore">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-backups" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Backups
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#restoring-data" class="md-nav__link">
    <span class="md-ellipsis">
      Restoring Data
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-migration" class="md-nav__link">
    <span class="md-ellipsis">
      Data Migration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Migration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#moving-between-systems" class="md-nav__link">
    <span class="md-ellipsis">
      Moving Between Systems
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#system-integration" class="md-nav__link">
    <span class="md-ellipsis">
      System Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#data-formats" class="md-nav__link">
    <span class="md-ellipsis">
      Data Formats
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Data Formats">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#csv-specifications" class="md-nav__link">
    <span class="md-ellipsis">
      CSV Specifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#field-definitions" class="md-nav__link">
    <span class="md-ellipsis">
      Field Definitions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#export-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Export Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#import-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Import Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#performance-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Performance Considerations
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#regular-exports" class="md-nav__link">
    <span class="md-ellipsis">
      Regular Exports
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-quality" class="md-nav__link">
    <span class="md-ellipsis">
      Data Quality
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#security" class="md-nav__link">
    <span class="md-ellipsis">
      Security
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


<h1 id="import-and-export">Import and Export<a class="headerlink" href="#import-and-export" title="Permanent link">&para;</a></h1>
<p>AdhocLog provides comprehensive data import and export capabilities to help you integrate with other systems, backup your data, and perform external analysis.</p>
<h2 id="export-features">Export Features<a class="headerlink" href="#export-features" title="Permanent link">&para;</a></h2>
<h3 id="csv-export">CSV Export<a class="headerlink" href="#csv-export" title="Permanent link">&para;</a></h3>
<p>Export your task data in CSV format for analysis in spreadsheet applications or integration with other systems.</p>
<p><strong>Available Data</strong>
- All task fields (ID, date, title, classification, description, estimated time)
- User information and timestamps
- Category mappings
- Archive status</p>
<p><strong>Export Process</strong>
1. <strong>Navigate</strong> to the Analytics or Export section
2. <strong>Select</strong> date range (optional)
3. <strong>Choose</strong> export format (CSV)
4. <strong>Click</strong> Export button
5. <strong>Download</strong> the generated file</p>
<p><strong>File Format</strong>
<div class="highlight"><pre><span></span><code><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>id,date,team_member,title,classification,category,description,est_time
<a id="__codelineno-0-2" name="__codelineno-0-2" href="#__codelineno-0-2"></a>1,2025-01-15,username,&quot;Review budget&quot;,&quot;Planning&quot;,&quot;Adhoc&quot;,&quot;Analyzed Q4 budget variance&quot;,60
<a id="__codelineno-0-3" name="__codelineno-0-3" href="#__codelineno-0-3"></a>2,2025-01-15,username,&quot;Team meeting&quot;,&quot;Business Support Activities&quot;,&quot;Business Support Activities&quot;,&quot;Weekly team sync&quot;,30
</code></pre></div></p>
<h3 id="filtered-exports">Filtered Exports<a class="headerlink" href="#filtered-exports" title="Permanent link">&para;</a></h3>
<p><strong>By Date Range</strong>
- Export tasks from specific time periods
- Useful for monthly/quarterly reporting
- Supports custom date selection</p>
<p><strong>By Classification</strong>
- Export only specific types of work
- Analyze particular work categories
- Generate focused reports</p>
<p><strong>By Status</strong>
- Active tasks only
- Archived tasks only
- All tasks (default)</p>
<h3 id="report-generation">Report Generation<a class="headerlink" href="#report-generation" title="Permanent link">&para;</a></h3>
<p><strong>Summary Reports</strong>
- Pre-formatted productivity summaries
- Key metrics and insights
- Professional presentation format</p>
<p><strong>Time Tracking Reports</strong>
- Detailed time allocation by classification
- Project-specific breakdowns
- Compliance and billing documentation</p>
<p><strong>Analytics Reports</strong>
- Productivity trends and patterns
- Efficiency metrics
- Performance indicators</p>
<h2 id="import-capabilities">Import Capabilities<a class="headerlink" href="#import-capabilities" title="Permanent link">&para;</a></h2>
<h3 id="csv-import">CSV Import<a class="headerlink" href="#csv-import" title="Permanent link">&para;</a></h3>
<p>Import task data from external systems or backup files.</p>
<p><strong>Supported Format</strong>
The import file must follow the standard CSV format with these required columns:
- <code>title</code> (required)
- <code>classification</code> (required)
- <code>description</code> (required)
- <code>est_time</code> (required, in minutes)
- <code>date</code> (optional, defaults to today)</p>
<p><strong>Sample Import File</strong>
<div class="highlight"><pre><span></span><code><a id="__codelineno-1-1" name="__codelineno-1-1" href="#__codelineno-1-1"></a>title,classification,description,est_time,date
<a id="__codelineno-1-2" name="__codelineno-1-2" href="#__codelineno-1-2"></a>&quot;Budget Analysis&quot;,&quot;Planning&quot;,&quot;Quarterly budget review and variance analysis&quot;,90,2025-01-15
<a id="__codelineno-1-3" name="__codelineno-1-3" href="#__codelineno-1-3"></a>&quot;Team Meeting&quot;,&quot;Business Support Activities&quot;,&quot;Weekly team synchronization meeting&quot;,30,2025-01-15
<a id="__codelineno-1-4" name="__codelineno-1-4" href="#__codelineno-1-4"></a>&quot;Report Generation&quot;,&quot;Offline Processing&quot;,&quot;Created monthly performance report&quot;,60,2025-01-15
</code></pre></div></p>
<p><strong>Import Process</strong>
1. <strong>Prepare</strong> your CSV file with required columns
2. <strong>Navigate</strong> to the Import section
3. <strong>Select</strong> your CSV file
4. <strong>Review</strong> the preview of data to be imported
5. <strong>Confirm</strong> the import operation
6. <strong>Verify</strong> imported tasks in the task list</p>
<h3 id="data-validation">Data Validation<a class="headerlink" href="#data-validation" title="Permanent link">&para;</a></h3>
<p><strong>Required Field Checking</strong>
- Ensures all required fields are present
- Validates data types and formats
- Reports any errors before import</p>
<p><strong>Classification Validation</strong>
- Verifies classifications match system options
- Suggests corrections for invalid entries
- Maintains data consistency</p>
<p><strong>Time Format Validation</strong>
- Ensures estimated time is in minutes
- Validates numeric format
- Provides error messages for invalid entries</p>
<h2 id="backup-and-restore">Backup and Restore<a class="headerlink" href="#backup-and-restore" title="Permanent link">&para;</a></h2>
<h3 id="creating-backups">Creating Backups<a class="headerlink" href="#creating-backups" title="Permanent link">&para;</a></h3>
<p><strong>Manual Backup</strong>
1. <strong>Export</strong> all tasks using CSV export
2. <strong>Save</strong> the file with a descriptive name (e.g., "adhoclog_backup_2025-01-15.csv")
3. <strong>Store</strong> in a secure location</p>
<p><strong>Automated Backup</strong>
- AdhocLog automatically maintains backup files
- Located in the data directory
- Includes both active and archived tasks</p>
<p><strong>Backup Best Practices</strong>
- Create regular backups (weekly/monthly)
- Store backups in multiple locations
- Test restore procedures periodically
- Document backup procedures</p>
<h3 id="restoring-data">Restoring Data<a class="headerlink" href="#restoring-data" title="Permanent link">&para;</a></h3>
<p><strong>From CSV Backup</strong>
1. <strong>Prepare</strong> the backup CSV file
2. <strong>Clear</strong> existing data if performing full restore
3. <strong>Import</strong> the backup file using standard import process
4. <strong>Verify</strong> data integrity after restore</p>
<p><strong>Partial Restore</strong>
- Import specific date ranges
- Restore only certain classifications
- Merge with existing data</p>
<h2 id="data-migration">Data Migration<a class="headerlink" href="#data-migration" title="Permanent link">&para;</a></h2>
<h3 id="moving-between-systems">Moving Between Systems<a class="headerlink" href="#moving-between-systems" title="Permanent link">&para;</a></h3>
<p><strong>Exporting for Migration</strong>
1. <strong>Export</strong> all data using comprehensive CSV export
2. <strong>Include</strong> archived tasks if needed
3. <strong>Document</strong> any custom configurations
4. <strong>Verify</strong> export completeness</p>
<p><strong>Importing to New System</strong>
1. <strong>Install</strong> AdhocLog on new system
2. <strong>Configure</strong> classifications to match source system
3. <strong>Import</strong> data using CSV import
4. <strong>Verify</strong> data integrity and completeness</p>
<h3 id="system-integration">System Integration<a class="headerlink" href="#system-integration" title="Permanent link">&para;</a></h3>
<p><strong>External Analytics Tools</strong>
- Export data for analysis in BI tools
- Integration with reporting systems
- Custom dashboard creation</p>
<p><strong>Project Management Systems</strong>
- Export task data for project tracking
- Integration with enterprise PM tools
- Resource allocation analysis</p>
<p><strong>Time Tracking Systems</strong>
- Export for payroll and billing
- Integration with HR systems
- Compliance reporting</p>
<h2 id="data-formats">Data Formats<a class="headerlink" href="#data-formats" title="Permanent link">&para;</a></h2>
<h3 id="csv-specifications">CSV Specifications<a class="headerlink" href="#csv-specifications" title="Permanent link">&para;</a></h3>
<p><strong>Character Encoding</strong>: UTF-8
<strong>Delimiter</strong>: Comma (,)
<strong>Text Qualifier</strong>: Double quotes (")
<strong>Date Format</strong>: YYYY-MM-DD
<strong>Time Format</strong>: Minutes (integer)</p>
<p><strong>Special Characters</strong>
- Commas in text fields must be quoted
- Line breaks in descriptions should be escaped
- Unicode characters are supported</p>
<h3 id="field-definitions">Field Definitions<a class="headerlink" href="#field-definitions" title="Permanent link">&para;</a></h3>
<p><strong>Required Fields</strong>
- <code>title</code>: Task title (string, max 255 characters)
- <code>classification</code>: Must match system classifications
- <code>description</code>: Task description (string, unlimited)
- <code>est_time</code>: Estimated time in minutes (integer, 1-999)</p>
<p><strong>Optional Fields</strong>
- <code>date</code>: Task date (YYYY-MM-DD format, defaults to today)
- <code>team_member</code>: User identifier (auto-detected if not provided)
- <code>category</code>: Auto-mapped from classification</p>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="export-issues">Export Issues<a class="headerlink" href="#export-issues" title="Permanent link">&para;</a></h3>
<p><strong>File Not Downloading</strong>
- Check browser download settings
- Verify popup blockers are disabled
- Try different browser if needed</p>
<p><strong>Incomplete Data</strong>
- Verify date range selection
- Check filter settings
- Ensure all required data exists</p>
<p><strong>Format Problems</strong>
- Verify CSV format compatibility
- Check character encoding
- Test with small data sample</p>
<h3 id="import-issues">Import Issues<a class="headerlink" href="#import-issues" title="Permanent link">&para;</a></h3>
<p><strong>File Format Errors</strong>
- Verify CSV format and structure
- Check required columns are present
- Validate data types and formats</p>
<p><strong>Data Validation Failures</strong>
- Review error messages carefully
- Correct invalid classifications
- Fix missing required fields</p>
<p><strong>Duplicate Data</strong>
- Check for existing tasks with same details
- Consider using date filters
- Review import preview carefully</p>
<h3 id="performance-considerations">Performance Considerations<a class="headerlink" href="#performance-considerations" title="Permanent link">&para;</a></h3>
<p><strong>Large Data Sets</strong>
- Break large imports into smaller batches
- Allow extra time for processing
- Monitor system resources during import</p>
<p><strong>Network Issues</strong>
- Ensure stable internet connection
- Retry failed operations
- Consider local backup options</p>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="regular-exports">Regular Exports<a class="headerlink" href="#regular-exports" title="Permanent link">&para;</a></h3>
<p><strong>Scheduled Backups</strong>
- Export data weekly or monthly
- Automate where possible
- Store in secure, accessible location</p>
<p><strong>Version Control</strong>
- Use descriptive file names with dates
- Maintain multiple backup versions
- Document any data changes</p>
<h3 id="data-quality">Data Quality<a class="headerlink" href="#data-quality" title="Permanent link">&para;</a></h3>
<p><strong>Clean Data</strong>
- Review data before export
- Correct any inconsistencies
- Standardize classifications and descriptions</p>
<p><strong>Validation</strong>
- Test import/export procedures regularly
- Verify data integrity after operations
- Maintain documentation of processes</p>
<h3 id="security">Security<a class="headerlink" href="#security" title="Permanent link">&para;</a></h3>
<p><strong>Data Protection</strong>
- Encrypt sensitive backup files
- Use secure storage locations
- Follow organizational data policies</p>
<p><strong>Access Control</strong>
- Limit export capabilities to authorized users
- Monitor data access and usage
- Maintain audit trails where required</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.50899def.min.js"></script>
      
    
  </body>
</html>