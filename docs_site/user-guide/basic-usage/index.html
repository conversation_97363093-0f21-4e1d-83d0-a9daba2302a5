
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Professional task tracking and management application for enterprise productivity">
      
      
        <meta name="author" content="Cardinal Health">
      
      
        <link rel="canonical" href="https://localhost:8000/user-guide/basic-usage/">
      
      
        <link rel="prev" href="../../configuration/">
      
      
        <link rel="next" href="../task-management/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">
    
    
      
        <title>Basic Usage - AdhocLog Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.7e37652d.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#basic-usage" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Basic Usage
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="./" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../ai-features/analysis/" class="md-tabs__link">
          
  
  
  AI Features

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#task-management-basics" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Task Management Basics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Task Management Basics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-your-first-task" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Your First Task
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#task-information-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Task Information Fields
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#working-with-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Working with Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Working with Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-task" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a Task
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#task-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Task Classifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#editing-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Editing Tasks
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#classifications-and-categories" class="md-nav__link">
    <span class="md-ellipsis">
      🏷️ Classifications and Categories
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏷️ Classifications and Categories">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#using-classifications-effectively" class="md-nav__link">
    <span class="md-ellipsis">
      Using Classifications Effectively
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-estimation-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Time Estimation Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#time-tracking" class="md-nav__link">
    <span class="md-ellipsis">
      ⏰ Time Tracking
    </span>
  </a>
  
    <nav class="md-nav" aria-label="⏰ Time Tracking">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#automatic-time-tracking" class="md-nav__link">
    <span class="md-ellipsis">
      Automatic Time Tracking
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-time-adjustments" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Time Adjustments
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-tracking-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Time Tracking Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#notes-and-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      📝 Notes and Documentation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📝 Notes and Documentation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#effective-note-taking" class="md-nav__link">
    <span class="md-ellipsis">
      Effective Note-Taking
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#note-taking-tips" class="md-nav__link">
    <span class="md-ellipsis">
      Note-Taking Tips
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#example-notes" class="md-nav__link">
    <span class="md-ellipsis">
      Example Notes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#finding-and-organizing-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Finding and Organizing Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔍 Finding and Organizing Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-list-views" class="md-nav__link">
    <span class="md-ellipsis">
      Task List Views
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#searching-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Searching Tasks
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sorting-options" class="md-nav__link">
    <span class="md-ellipsis">
      Sorting Options
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#basic-analytics" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Basic Analytics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📊 Basic Analytics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Task Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-distribution" class="md-nav__link">
    <span class="md-ellipsis">
      Time Distribution
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#completion-rates" class="md-nav__link">
    <span class="md-ellipsis">
      Completion Rates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#daily-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      🔄 Daily Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔄 Daily Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#morning-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Morning Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#during-work" class="md-nav__link">
    <span class="md-ellipsis">
      During Work
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#end-of-day-review" class="md-nav__link">
    <span class="md-ellipsis">
      End of Day Review
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tips-for-success" class="md-nav__link">
    <span class="md-ellipsis">
      💡 Tips for Success
    </span>
  </a>
  
    <nav class="md-nav" aria-label="💡 Tips for Success">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#building-good-habits" class="md-nav__link">
    <span class="md-ellipsis">
      Building Good Habits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-mistakes-to-avoid" class="md-nav__link">
    <span class="md-ellipsis">
      Common Mistakes to Avoid
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      🆘 Getting Help
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🆘 Getting Help">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quick-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Reference
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-questions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Questions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#need-more-help" class="md-nav__link">
    <span class="md-ellipsis">
      Need More Help?
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../task-management/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../analytics/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Analytics
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../import-export/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    AI Features
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/analysis/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/chatbot/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/quick-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Reference
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#task-management-basics" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Task Management Basics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📋 Task Management Basics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-your-first-task" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Your First Task
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#task-information-fields" class="md-nav__link">
    <span class="md-ellipsis">
      Task Information Fields
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#working-with-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Working with Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Working with Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#creating-a-task" class="md-nav__link">
    <span class="md-ellipsis">
      Creating a Task
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#task-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Task Classifications
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#editing-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Editing Tasks
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#classifications-and-categories" class="md-nav__link">
    <span class="md-ellipsis">
      🏷️ Classifications and Categories
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🏷️ Classifications and Categories">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#using-classifications-effectively" class="md-nav__link">
    <span class="md-ellipsis">
      Using Classifications Effectively
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-estimation-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Time Estimation Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#time-tracking" class="md-nav__link">
    <span class="md-ellipsis">
      ⏰ Time Tracking
    </span>
  </a>
  
    <nav class="md-nav" aria-label="⏰ Time Tracking">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#automatic-time-tracking" class="md-nav__link">
    <span class="md-ellipsis">
      Automatic Time Tracking
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#manual-time-adjustments" class="md-nav__link">
    <span class="md-ellipsis">
      Manual Time Adjustments
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-tracking-best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Time Tracking Best Practices
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#notes-and-documentation" class="md-nav__link">
    <span class="md-ellipsis">
      📝 Notes and Documentation
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📝 Notes and Documentation">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#effective-note-taking" class="md-nav__link">
    <span class="md-ellipsis">
      Effective Note-Taking
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#note-taking-tips" class="md-nav__link">
    <span class="md-ellipsis">
      Note-Taking Tips
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#example-notes" class="md-nav__link">
    <span class="md-ellipsis">
      Example Notes
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#finding-and-organizing-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Finding and Organizing Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔍 Finding and Organizing Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-list-views" class="md-nav__link">
    <span class="md-ellipsis">
      Task List Views
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#searching-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Searching Tasks
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#sorting-options" class="md-nav__link">
    <span class="md-ellipsis">
      Sorting Options
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#basic-analytics" class="md-nav__link">
    <span class="md-ellipsis">
      📊 Basic Analytics
    </span>
  </a>
  
    <nav class="md-nav" aria-label="📊 Basic Analytics">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Task Overview
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-distribution" class="md-nav__link">
    <span class="md-ellipsis">
      Time Distribution
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#completion-rates" class="md-nav__link">
    <span class="md-ellipsis">
      Completion Rates
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#daily-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      🔄 Daily Workflow
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🔄 Daily Workflow">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#morning-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Morning Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#during-work" class="md-nav__link">
    <span class="md-ellipsis">
      During Work
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#end-of-day-review" class="md-nav__link">
    <span class="md-ellipsis">
      End of Day Review
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#tips-for-success" class="md-nav__link">
    <span class="md-ellipsis">
      💡 Tips for Success
    </span>
  </a>
  
    <nav class="md-nav" aria-label="💡 Tips for Success">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#getting-started" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Started
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#building-good-habits" class="md-nav__link">
    <span class="md-ellipsis">
      Building Good Habits
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-mistakes-to-avoid" class="md-nav__link">
    <span class="md-ellipsis">
      Common Mistakes to Avoid
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      🆘 Getting Help
    </span>
  </a>
  
    <nav class="md-nav" aria-label="🆘 Getting Help">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#quick-reference" class="md-nav__link">
    <span class="md-ellipsis">
      Quick Reference
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#common-questions" class="md-nav__link">
    <span class="md-ellipsis">
      Common Questions
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#need-more-help" class="md-nav__link">
    <span class="md-ellipsis">
      Need More Help?
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


<h1 id="basic-usage">Basic Usage<a class="headerlink" href="#basic-usage" title="Permanent link">&para;</a></h1>
<p>Learn the fundamentals of using AdhocLog for effective task tracking and productivity management.</p>
<h2 id="task-management-basics">📋 Task Management Basics<a class="headerlink" href="#task-management-basics" title="Permanent link">&para;</a></h2>
<h3 id="creating-your-first-task">Creating Your First Task<a class="headerlink" href="#creating-your-first-task" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Navigate</strong> to the main dashboard</li>
<li><strong>Click</strong> "Add New Task" or use the "+" button</li>
<li><strong>Fill in</strong> the required information:</li>
<li><strong>Task Name</strong>: Clear, descriptive title</li>
<li><strong>Classification</strong>: Category or type of work</li>
<li><strong>Start Time</strong>: When you began (auto-filled)</li>
<li><strong>Notes</strong>: Additional context or details</li>
</ol>
<h3 id="task-information-fields">Task Information Fields<a class="headerlink" href="#task-information-fields" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Field</th>
<th>Description</th>
<th>Required</th>
<th>Example</th>
</tr>
</thead>
<tbody>
<tr>
<td>Title</td>
<td>Brief description of the task</td>
<td>Yes</td>
<td>"Review quarterly budget"</td>
</tr>
<tr>
<td>Classification</td>
<td>Category or type of work</td>
<td>Yes</td>
<td>"Planning", "Execution", "Business Support Activities"</td>
</tr>
<tr>
<td>Description</td>
<td>Detailed description of actions taken</td>
<td>Yes</td>
<td>"Analyzed Q4 budget variance and prepared summary report"</td>
</tr>
<tr>
<td>Estimated Time</td>
<td>Expected duration in minutes</td>
<td>Yes</td>
<td>60 (for 1 hour)</td>
</tr>
<tr>
<td>Date</td>
<td>Date when task was performed</td>
<td>No</td>
<td>Auto-filled with today's date</td>
</tr>
</tbody>
</table>
<h3 id="working-with-tasks">Working with Tasks<a class="headerlink" href="#working-with-tasks" title="Permanent link">&para;</a></h3>
<h4 id="creating-a-task">Creating a Task<a class="headerlink" href="#creating-a-task" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Navigate</strong> to the "Add Task" page</li>
<li><strong>Fill in required fields</strong>:</li>
<li>Title: Brief description of what you're doing</li>
<li>Classification: Select from predefined business categories</li>
<li>Description: Detailed explanation of actions taken</li>
<li>Estimated Time: Duration in minutes</li>
<li><strong>Submit</strong> to save the task</li>
</ol>
<h4 id="task-classifications">Task Classifications<a class="headerlink" href="#task-classifications" title="Permanent link">&para;</a></h4>
<p>AdhocLog uses five predefined business classifications:</p>
<ul>
<li><strong>Planning</strong>: Strategy, design, architecture, and planning activities</li>
<li><strong>Offline Processing</strong>: Data analysis, reporting, and document processing</li>
<li><strong>Execution</strong>: Implementation, development, and hands-on work</li>
<li><strong>Business Support Activities</strong>: Meetings, coordination, and administrative tasks</li>
<li><strong>Operational Project Involvement</strong>: Project management and monitoring activities</li>
</ul>
<h4 id="editing-tasks">Editing Tasks<a class="headerlink" href="#editing-tasks" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Click</strong> on any task title to edit details</li>
<li><strong>Update</strong> any field as needed</li>
<li><strong>Save</strong> changes to preserve modifications</li>
</ul>
<h2 id="classifications-and-categories">🏷️ Classifications and Categories<a class="headerlink" href="#classifications-and-categories" title="Permanent link">&para;</a></h2>
<h3 id="using-classifications-effectively">Using Classifications Effectively<a class="headerlink" href="#using-classifications-effectively" title="Permanent link">&para;</a></h3>
<p>Classifications help organize and analyze your work patterns:</p>
<ul>
<li><strong>Be consistent</strong> with naming conventions</li>
<li><strong>Use broad categories</strong> that apply to multiple tasks</li>
<li><strong>Create meaningful groupings</strong> for better analytics</li>
</ul>
<h3 id="time-estimation-guidelines">Time Estimation Guidelines<a class="headerlink" href="#time-estimation-guidelines" title="Permanent link">&para;</a></h3>
<p>When entering estimated time in minutes, consider these guidelines:</p>
<ul>
<li><strong>Simple tasks</strong>: 15-30 minutes (email responses, quick updates)</li>
<li><strong>Standard tasks</strong>: 30-90 minutes (analysis, documentation, meetings)</li>
<li><strong>Complex tasks</strong>: 90-240 minutes (development, detailed analysis)</li>
<li><strong>Large projects</strong>: Break into smaller tasks of 60-120 minutes each</li>
</ul>
<h3 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Type</strong> a new classification name when creating a task</li>
<li><strong>Use</strong> consistent naming for similar tasks</li>
<li><strong>Review</strong> existing classifications to avoid duplicates</li>
</ol>
<h2 id="time-tracking">⏰ Time Tracking<a class="headerlink" href="#time-tracking" title="Permanent link">&para;</a></h2>
<h3 id="automatic-time-tracking">Automatic Time Tracking<a class="headerlink" href="#automatic-time-tracking" title="Permanent link">&para;</a></h3>
<p>AdhocLog automatically tracks time for each task:</p>
<ul>
<li><strong>Start time</strong> recorded when task is created</li>
<li><strong>Duration</strong> calculated when task is completed</li>
<li><strong>Running time</strong> shown for active tasks</li>
</ul>
<h3 id="manual-time-adjustments">Manual Time Adjustments<a class="headerlink" href="#manual-time-adjustments" title="Permanent link">&para;</a></h3>
<p>If you need to adjust times:</p>
<ol>
<li><strong>Edit</strong> the task</li>
<li><strong>Modify</strong> start or end times</li>
<li><strong>Save</strong> changes to update duration</li>
</ol>
<h3 id="time-tracking-best-practices">Time Tracking Best Practices<a class="headerlink" href="#time-tracking-best-practices" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Start tasks</strong> when you actually begin working</li>
<li><strong>End tasks</strong> promptly when finished</li>
<li><strong>Use notes</strong> to explain any time gaps</li>
<li><strong>Be honest</strong> about actual time spent</li>
</ul>
<h2 id="notes-and-documentation">📝 Notes and Documentation<a class="headerlink" href="#notes-and-documentation" title="Permanent link">&para;</a></h2>
<h3 id="effective-note-taking">Effective Note-Taking<a class="headerlink" href="#effective-note-taking" title="Permanent link">&para;</a></h3>
<p>Use the notes field to capture:</p>
<ul>
<li><strong>Key decisions</strong> made during the task</li>
<li><strong>Important findings</strong> or discoveries</li>
<li><strong>Next steps</strong> or follow-up actions</li>
<li><strong>Resources used</strong> or people consulted</li>
<li><strong>Challenges encountered</strong> and solutions</li>
</ul>
<h3 id="note-taking-tips">Note-Taking Tips<a class="headerlink" href="#note-taking-tips" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Be specific</strong> rather than vague</li>
<li><strong>Use bullet points</strong> for easy reading</li>
<li><strong>Include relevant details</strong> for future reference</li>
<li><strong>Update notes</strong> throughout the task</li>
</ul>
<h3 id="example-notes">Example Notes<a class="headerlink" href="#example-notes" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><a id="__codelineno-0-1" name="__codelineno-0-1" href="#__codelineno-0-1"></a>✅ Completed Q4 budget analysis
<a id="__codelineno-0-2" name="__codelineno-0-2" href="#__codelineno-0-2"></a>📊 Key findings:
<a id="__codelineno-0-3" name="__codelineno-0-3" href="#__codelineno-0-3"></a>  - 15% variance in marketing spend
<a id="__codelineno-0-4" name="__codelineno-0-4" href="#__codelineno-0-4"></a>  - IT costs under budget by 8%
<a id="__codelineno-0-5" name="__codelineno-0-5" href="#__codelineno-0-5"></a>  - Travel expenses exceeded by 12%
<a id="__codelineno-0-6" name="__codelineno-0-6" href="#__codelineno-0-6"></a>🔄 Next steps:
<a id="__codelineno-0-7" name="__codelineno-0-7" href="#__codelineno-0-7"></a>  - Schedule meeting with marketing team
<a id="__codelineno-0-8" name="__codelineno-0-8" href="#__codelineno-0-8"></a>  - Review IT cost savings opportunities
<a id="__codelineno-0-9" name="__codelineno-0-9" href="#__codelineno-0-9"></a>  - Update travel policy recommendations
<a id="__codelineno-0-10" name="__codelineno-0-10" href="#__codelineno-0-10"></a>📎 Resources: Q4_Budget_Report.xlsx, expense_data.csv
</code></pre></div>
<h2 id="finding-and-organizing-tasks">🔍 Finding and Organizing Tasks<a class="headerlink" href="#finding-and-organizing-tasks" title="Permanent link">&para;</a></h2>
<h3 id="task-list-views">Task List Views<a class="headerlink" href="#task-list-views" title="Permanent link">&para;</a></h3>
<p>The main task list provides several views:</p>
<ul>
<li><strong>All Tasks</strong> - Complete list of all tasks</li>
<li><strong>Recent</strong> - Most recently created or updated</li>
<li><strong>Active</strong> - Currently in progress</li>
<li><strong>Completed</strong> - Finished tasks</li>
<li><strong>By Classification</strong> - Grouped by category</li>
</ul>
<h3 id="searching-tasks">Searching Tasks<a class="headerlink" href="#searching-tasks" title="Permanent link">&para;</a></h3>
<p>Use the search functionality to find specific tasks:</p>
<ul>
<li><strong>Search by name</strong> - Find tasks with specific keywords</li>
<li><strong>Filter by classification</strong> - Show only certain categories</li>
<li><strong>Date range</strong> - Find tasks from specific time periods</li>
<li><strong>Status filter</strong> - Active, completed, or all tasks</li>
</ul>
<h3 id="sorting-options">Sorting Options<a class="headerlink" href="#sorting-options" title="Permanent link">&para;</a></h3>
<p>Sort tasks by:
- <strong>Date</strong> (newest or oldest first)
- <strong>Duration</strong> (longest or shortest first)
- <strong>Classification</strong> (alphabetical)
- <strong>Status</strong> (active, completed)</p>
<h2 id="basic-analytics">📊 Basic Analytics<a class="headerlink" href="#basic-analytics" title="Permanent link">&para;</a></h2>
<h3 id="task-overview">Task Overview<a class="headerlink" href="#task-overview" title="Permanent link">&para;</a></h3>
<p>The dashboard provides quick insights:</p>
<ul>
<li><strong>Total tasks</strong> created</li>
<li><strong>Time spent</strong> today/this week</li>
<li><strong>Most common</strong> classifications</li>
<li><strong>Productivity trends</strong></li>
</ul>
<h3 id="time-distribution">Time Distribution<a class="headerlink" href="#time-distribution" title="Permanent link">&para;</a></h3>
<p>View how your time is distributed across:
- <strong>Classifications</strong> - Which types of work take most time
- <strong>Days of week</strong> - When you're most productive
- <strong>Time of day</strong> - Peak productivity hours</p>
<h3 id="completion-rates">Completion Rates<a class="headerlink" href="#completion-rates" title="Permanent link">&para;</a></h3>
<p>Track your task completion patterns:
- <strong>Tasks completed</strong> vs. started
- <strong>Average task duration</strong> by classification
- <strong>Productivity trends</strong> over time</p>
<h2 id="daily-workflow">🔄 Daily Workflow<a class="headerlink" href="#daily-workflow" title="Permanent link">&para;</a></h2>
<h3 id="morning-setup">Morning Setup<a class="headerlink" href="#morning-setup" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Review</strong> yesterday's completed tasks</li>
<li><strong>Plan</strong> today's priorities</li>
<li><strong>Create</strong> initial tasks for the day</li>
<li><strong>Check</strong> any pending items</li>
</ol>
<h3 id="during-work">During Work<a class="headerlink" href="#during-work" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Start</strong> each task when you begin</li>
<li><strong>Add notes</strong> as you work</li>
<li><strong>Update</strong> task details if scope changes</li>
<li><strong>Complete</strong> tasks when finished</li>
</ol>
<h3 id="end-of-day-review">End of Day Review<a class="headerlink" href="#end-of-day-review" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Complete</strong> any unfinished tasks</li>
<li><strong>Review</strong> the day's accomplishments</li>
<li><strong>Add final notes</strong> to tasks</li>
<li><strong>Plan</strong> for tomorrow</li>
</ol>
<h2 id="tips-for-success">💡 Tips for Success<a class="headerlink" href="#tips-for-success" title="Permanent link">&para;</a></h2>
<h3 id="getting-started">Getting Started<a class="headerlink" href="#getting-started" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Start simple</strong> - Don't overcomplicate initially</li>
<li><strong>Be consistent</strong> - Use AdhocLog daily for best results</li>
<li><strong>Experiment</strong> with classifications to find what works</li>
<li><strong>Use notes</strong> liberally to capture context</li>
</ul>
<h3 id="building-good-habits">Building Good Habits<a class="headerlink" href="#building-good-habits" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Track everything</strong> - Even small tasks add up</li>
<li><strong>Be honest</strong> about time spent</li>
<li><strong>Review regularly</strong> to identify patterns</li>
<li><strong>Adjust</strong> your approach based on insights</li>
</ul>
<h3 id="common-mistakes-to-avoid">Common Mistakes to Avoid<a class="headerlink" href="#common-mistakes-to-avoid" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Don't</strong> create too many classifications initially</li>
<li><strong>Don't</strong> forget to end tasks when finished</li>
<li><strong>Don't</strong> leave notes empty - future you will thank you</li>
<li><strong>Don't</strong> batch-enter tasks - real-time tracking is more accurate</li>
</ul>
<h2 id="getting-help">🆘 Getting Help<a class="headerlink" href="#getting-help" title="Permanent link">&para;</a></h2>
<h3 id="quick-reference">Quick Reference<a class="headerlink" href="#quick-reference" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>F1</strong> - Help documentation</li>
<li><strong>Ctrl+N</strong> - New task</li>
<li><strong>Ctrl+S</strong> - Save current task</li>
<li><strong>Ctrl+E</strong> - Export data</li>
</ul>
<h3 id="common-questions">Common Questions<a class="headerlink" href="#common-questions" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>How do I edit a task?</strong> Click on the task name</li>
<li><strong>How do I delete a task?</strong> Use the delete button (⚠️ permanent)</li>
<li><strong>How do I change classifications?</strong> Edit the task and update the field</li>
<li><strong>How do I export my data?</strong> Use the Export button on the main page</li>
</ul>
<h3 id="need-more-help">Need More Help?<a class="headerlink" href="#need-more-help" title="Permanent link">&para;</a></h3>
<ul>
<li>Use the AI chatbot for quick questions</li>
<li>Review the <a href="../task-management/">Task Management</a> guide for advanced features</li>
<li>Check the <a href="../analytics/">Analytics</a> section for productivity insights</li>
<li>Contact your system administrator for technical issues</li>
</ul>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.50899def.min.js"></script>
      
    
  </body>
</html>