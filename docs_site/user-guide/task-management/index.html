
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Professional task tracking and management application for enterprise productivity">
      
      
        <meta name="author" content="Cardinal Health">
      
      
        <link rel="canonical" href="https://localhost:8000/user-guide/task-management/">
      
      
        <link rel="prev" href="../basic-usage/">
      
      
        <link rel="next" href="../analytics/">
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.16">
    
    
      
        <title>Task Management - AdhocLog Documentation</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.7e37652d.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#task-management" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
      <div data-md-color-scheme="default" data-md-component="outdated" hidden>
        
      </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="AdhocLog Documentation" class="md-header__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            AdhocLog Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Task Management
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">
    
      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="blue" data-md-color-accent="blue"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">
    
      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
      
      
        <label class="md-header__button md-icon" for="__search">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">
            
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
      
    
    
      <div class="md-header__source">
        <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">
        
  
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../installation/" class="md-tabs__link">
          
  
  
  Getting Started

        </a>
      </li>
    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="../basic-usage/" class="md-tabs__link">
          
  
  
  User Guide

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../ai-features/analysis/" class="md-tabs__link">
          
  
  
  AI Features

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="AdhocLog Documentation" class="md-nav__button md-logo" aria-label="AdhocLog Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    AdhocLog Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/jvbalcita/adhoc-log-app" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M439.6 236.1 244 40.5c-5.4-5.5-12.8-8.5-20.4-8.5s-15 3-20.4 8.4L162.5 81l51.5 51.5c27.1-9.1 52.7 16.8 43.4 43.7l49.7 49.7c34.2-11.8 61.2 31 35.5 56.7-26.5 26.5-70.2-2.9-56-37.3L240.3 199v121.9c25.3 12.5 22.3 41.8 9.1 55-6.4 6.4-15.2 10.1-24.3 10.1s-17.8-3.6-24.3-10.1c-17.6-17.6-11.1-46.9 11.2-56v-123c-20.8-8.5-24.6-30.7-18.6-45L142.6 101 8.5 235.1C3 240.6 0 247.9 0 255.5s3 15 8.5 20.4l195.6 195.7c5.4 5.4 12.7 8.4 20.4 8.4s15-3 20.4-8.4l194.7-194.7c5.4-5.4 8.4-12.8 8.4-20.4s-3-15-8.4-20.4"/></svg>
  </div>
  <div class="md-source__repository">
    adhoc-log-app
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Home
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    Getting Started
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Getting Started
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../installation/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Installation
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../quick-start/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Start
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../configuration/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Configuration
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    User Guide
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            User Guide
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../basic-usage/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Basic Usage
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    Task Management
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#creating-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Creating Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#single-task-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Single Task Creation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bulk-task-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Task Creation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#task-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Task Classifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Task Classifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#planning" class="md-nav__link">
    <span class="md-ellipsis">
      Planning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#offline-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Offline Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#execution" class="md-nav__link">
    <span class="md-ellipsis">
      Execution
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-support-activities" class="md-nav__link">
    <span class="md-ellipsis">
      Business Support Activities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#operational-project-involvement" class="md-nav__link">
    <span class="md-ellipsis">
      Operational Project Involvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#time-management" class="md-nav__link">
    <span class="md-ellipsis">
      Time Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Time Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#estimation-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Estimation Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-tracking-tips" class="md-nav__link">
    <span class="md-ellipsis">
      Time Tracking Tips
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#task-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Task Organization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Task Organization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#filtering-and-searching" class="md-nav__link">
    <span class="md-ellipsis">
      Filtering and Searching
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#archiving-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Archiving Tasks
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#productivity-features" class="md-nav__link">
    <span class="md-ellipsis">
      Productivity Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Productivity Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-templates" class="md-nav__link">
    <span class="md-ellipsis">
      Task Templates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#analytics-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Analytics Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-quality" class="md-nav__link">
    <span class="md-ellipsis">
      Data Quality
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#team-coordination" class="md-nav__link">
    <span class="md-ellipsis">
      Team Coordination
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../analytics/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Analytics
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../import-export/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Import/Export
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    AI Features
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            AI Features
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/analysis/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AI Analysis
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/chatbot/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Chatbot
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../ai-features/quick-reference/" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Quick Reference
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              
              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#creating-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Creating Tasks
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Creating Tasks">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#single-task-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Single Task Creation
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#bulk-task-creation" class="md-nav__link">
    <span class="md-ellipsis">
      Bulk Task Creation
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#task-classifications" class="md-nav__link">
    <span class="md-ellipsis">
      Task Classifications
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Task Classifications">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#planning" class="md-nav__link">
    <span class="md-ellipsis">
      Planning
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#offline-processing" class="md-nav__link">
    <span class="md-ellipsis">
      Offline Processing
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#execution" class="md-nav__link">
    <span class="md-ellipsis">
      Execution
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#business-support-activities" class="md-nav__link">
    <span class="md-ellipsis">
      Business Support Activities
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#operational-project-involvement" class="md-nav__link">
    <span class="md-ellipsis">
      Operational Project Involvement
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#time-management" class="md-nav__link">
    <span class="md-ellipsis">
      Time Management
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Time Management">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#estimation-guidelines" class="md-nav__link">
    <span class="md-ellipsis">
      Estimation Guidelines
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#time-tracking-tips" class="md-nav__link">
    <span class="md-ellipsis">
      Time Tracking Tips
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#task-organization" class="md-nav__link">
    <span class="md-ellipsis">
      Task Organization
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Task Organization">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#filtering-and-searching" class="md-nav__link">
    <span class="md-ellipsis">
      Filtering and Searching
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#archiving-tasks" class="md-nav__link">
    <span class="md-ellipsis">
      Archiving Tasks
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#productivity-features" class="md-nav__link">
    <span class="md-ellipsis">
      Productivity Features
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Productivity Features">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#task-templates" class="md-nav__link">
    <span class="md-ellipsis">
      Task Templates
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#analytics-integration" class="md-nav__link">
    <span class="md-ellipsis">
      Analytics Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#best-practices" class="md-nav__link">
    <span class="md-ellipsis">
      Best Practices
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Best Practices">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#daily-workflow" class="md-nav__link">
    <span class="md-ellipsis">
      Daily Workflow
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#data-quality" class="md-nav__link">
    <span class="md-ellipsis">
      Data Quality
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#team-coordination" class="md-nav__link">
    <span class="md-ellipsis">
      Team Coordination
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#troubleshooting" class="md-nav__link">
    <span class="md-ellipsis">
      Troubleshooting
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Troubleshooting">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#common-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Common Issues
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#getting-help" class="md-nav__link">
    <span class="md-ellipsis">
      Getting Help
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  


  
  


<h1 id="task-management">Task Management<a class="headerlink" href="#task-management" title="Permanent link">&para;</a></h1>
<p>This guide covers advanced task management features in AdhocLog, including bulk operations, templates, and productivity optimization.</p>
<h2 id="creating-tasks">Creating Tasks<a class="headerlink" href="#creating-tasks" title="Permanent link">&para;</a></h2>
<h3 id="single-task-creation">Single Task Creation<a class="headerlink" href="#single-task-creation" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Navigate</strong> to the "Add Task" page</li>
<li><strong>Complete required fields</strong>:</li>
<li><strong>Title</strong>: Brief, descriptive name for the task</li>
<li><strong>Classification</strong>: Select from business categories</li>
<li><strong>Description</strong>: Detailed explanation of work performed</li>
<li><strong>Estimated Time</strong>: Duration in minutes</li>
<li><strong>Optional fields</strong>:</li>
<li><strong>Date</strong>: Defaults to today's date</li>
<li><strong>Submit</strong> to save the task</li>
</ol>
<h3 id="bulk-task-creation">Bulk Task Creation<a class="headerlink" href="#bulk-task-creation" title="Permanent link">&para;</a></h3>
<p>For creating multiple similar tasks:</p>
<ol>
<li><strong>Click</strong> the "Bulk Entry" button on the Add Task page</li>
<li><strong>Fill in</strong> the template with common information</li>
<li><strong>Add rows</strong> for each individual task</li>
<li><strong>Customize</strong> each task's specific details</li>
<li><strong>Submit</strong> to create all tasks at once</li>
</ol>
<h2 id="task-classifications">Task Classifications<a class="headerlink" href="#task-classifications" title="Permanent link">&para;</a></h2>
<p>AdhocLog uses five predefined business classifications that map to specific categories:</p>
<h3 id="planning">Planning<a class="headerlink" href="#planning" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Category</strong>: Adhoc</li>
<li><strong>Examples</strong>: Strategic planning, project design, architecture planning</li>
<li><strong>Typical Duration</strong>: 60-120 minutes</li>
</ul>
<h3 id="offline-processing">Offline Processing<a class="headerlink" href="#offline-processing" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Category</strong>: Adhoc</li>
<li><strong>Examples</strong>: Data analysis, report generation, document processing</li>
<li><strong>Typical Duration</strong>: 30-90 minutes</li>
</ul>
<h3 id="execution">Execution<a class="headerlink" href="#execution" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Category</strong>: Adhoc</li>
<li><strong>Examples</strong>: Implementation, development, hands-on work</li>
<li><strong>Typical Duration</strong>: 60-240 minutes</li>
</ul>
<h3 id="business-support-activities">Business Support Activities<a class="headerlink" href="#business-support-activities" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Category</strong>: Business Support Activities</li>
<li><strong>Examples</strong>: Meetings, coordination, administrative tasks</li>
<li><strong>Typical Duration</strong>: 30-60 minutes</li>
</ul>
<h3 id="operational-project-involvement">Operational Project Involvement<a class="headerlink" href="#operational-project-involvement" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Category</strong>: Adhoc</li>
<li><strong>Examples</strong>: Project management, monitoring, oversight</li>
<li><strong>Typical Duration</strong>: 30-90 minutes</li>
</ul>
<h2 id="time-management">Time Management<a class="headerlink" href="#time-management" title="Permanent link">&para;</a></h2>
<h3 id="estimation-guidelines">Estimation Guidelines<a class="headerlink" href="#estimation-guidelines" title="Permanent link">&para;</a></h3>
<p><strong>Short Tasks (15-30 minutes)</strong>
- Email responses
- Quick status updates
- Brief reviews</p>
<p><strong>Medium Tasks (30-90 minutes)</strong>
- Standard meetings
- Document reviews
- Basic analysis</p>
<p><strong>Long Tasks (90-240 minutes)</strong>
- Complex analysis
- Development work
- Detailed planning</p>
<p><strong>Best Practice</strong>: Break tasks longer than 2 hours into smaller, manageable chunks.</p>
<h3 id="time-tracking-tips">Time Tracking Tips<a class="headerlink" href="#time-tracking-tips" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Be realistic</strong> with estimates</li>
<li><strong>Track actual time</strong> for future reference</li>
<li><strong>Review patterns</strong> to improve estimation accuracy</li>
<li><strong>Account for interruptions</strong> in your estimates</li>
</ol>
<h2 id="task-organization">Task Organization<a class="headerlink" href="#task-organization" title="Permanent link">&para;</a></h2>
<h3 id="filtering-and-searching">Filtering and Searching<a class="headerlink" href="#filtering-and-searching" title="Permanent link">&para;</a></h3>
<p><strong>By Date</strong>
- View tasks for specific dates
- Filter by date ranges
- Sort chronologically</p>
<p><strong>By Classification</strong>
- Group by work type
- Analyze time distribution
- Identify patterns</p>
<p><strong>By Status</strong>
- Active vs. completed tasks
- Track progress
- Manage workload</p>
<h3 id="archiving-tasks">Archiving Tasks<a class="headerlink" href="#archiving-tasks" title="Permanent link">&para;</a></h3>
<p>Tasks can be archived (soft deleted) when no longer needed:</p>
<ol>
<li><strong>Select</strong> the task to archive</li>
<li><strong>Click</strong> the delete/archive button</li>
<li><strong>Confirm</strong> the action</li>
</ol>
<p>Archived tasks are preserved with timestamps and can be restored if needed.</p>
<h2 id="productivity-features">Productivity Features<a class="headerlink" href="#productivity-features" title="Permanent link">&para;</a></h2>
<h3 id="task-templates">Task Templates<a class="headerlink" href="#task-templates" title="Permanent link">&para;</a></h3>
<p>Create reusable templates for common work patterns:</p>
<ol>
<li><strong>Identify</strong> frequently repeated tasks</li>
<li><strong>Create</strong> a template with standard information</li>
<li><strong>Reuse</strong> for similar future tasks</li>
<li><strong>Customize</strong> as needed for specific instances</li>
</ol>
<h3 id="analytics-integration">Analytics Integration<a class="headerlink" href="#analytics-integration" title="Permanent link">&para;</a></h3>
<p>Use task data for productivity insights:</p>
<ul>
<li><strong>Time distribution</strong> across classifications</li>
<li><strong>Completion patterns</strong> and trends</li>
<li><strong>Workload analysis</strong> and optimization</li>
<li><strong>Efficiency metrics</strong> and improvements</li>
</ul>
<h2 id="best-practices">Best Practices<a class="headerlink" href="#best-practices" title="Permanent link">&para;</a></h2>
<h3 id="daily-workflow">Daily Workflow<a class="headerlink" href="#daily-workflow" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Start each day</strong> by reviewing planned tasks</li>
<li><strong>Create tasks</strong> as work is performed</li>
<li><strong>Update estimates</strong> based on actual time</li>
<li><strong>Review completed work</strong> at day's end</li>
</ol>
<h3 id="data-quality">Data Quality<a class="headerlink" href="#data-quality" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Use consistent</strong> classification choices</li>
<li><strong>Write descriptive</strong> titles and descriptions</li>
<li><strong>Provide accurate</strong> time estimates</li>
<li><strong>Include relevant</strong> context in descriptions</li>
</ol>
<h3 id="team-coordination">Team Coordination<a class="headerlink" href="#team-coordination" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Standardize</strong> classification usage across team</li>
<li><strong>Share</strong> productivity insights and patterns</li>
<li><strong>Coordinate</strong> on common task templates</li>
<li><strong>Review</strong> team efficiency metrics regularly</li>
</ol>
<h2 id="troubleshooting">Troubleshooting<a class="headerlink" href="#troubleshooting" title="Permanent link">&para;</a></h2>
<h3 id="common-issues">Common Issues<a class="headerlink" href="#common-issues" title="Permanent link">&para;</a></h3>
<p><strong>Tasks not saving</strong>
- Check required fields are completed
- Verify network connectivity
- Refresh page and try again</p>
<p><strong>Classification confusion</strong>
- Review classification definitions
- Use consistent terminology
- Ask team lead for guidance</p>
<p><strong>Time estimation accuracy</strong>
- Track actual vs. estimated time
- Adjust future estimates based on patterns
- Account for complexity and interruptions</p>
<h3 id="getting-help">Getting Help<a class="headerlink" href="#getting-help" title="Permanent link">&para;</a></h3>
<ul>
<li>Review this documentation</li>
<li>Check the FAQ section</li>
<li>Contact your system administrator</li>
<li>Report issues through proper channels</li>
</ul>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright &copy; 2025 Cardinal Health
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
        <div class="md-social">
  
    
    
    
    
      
      
    
    <a href="https://github.com/jvbalcita/adhoc-log-app" target="_blank" rel="noopener" title="github.com" class="md-social__link">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--! Font Awesome Free 7.0.0 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2025 Fonticons, Inc.--><path fill="currentColor" d="M173.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6m-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3m44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9M252.8 8C114.1 8 8 113.3 8 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C436.2 457.8 504 362.9 504 252 504 113.3 391.5 8 252.8 8M105.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1m-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7m32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1m-11.4-14.7c-1.6 1-1.6 3.6 0 5.9s4.3 3.3 5.6 2.3c1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2"/></svg>
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>
    
    
      <script src="../../assets/javascripts/bundle.50899def.min.js"></script>
      
    
  </body>
</html>