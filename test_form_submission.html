<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Submission Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Form Submission Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Form (No JavaScript)</h2>
        <form action="http://127.0.0.1:5000/tasks/add" method="POST">
            <input type="text" name="title" value="Test Task Basic" required>
            <input type="text" name="classification" value="Planning" required>
            <input type="text" name="description" value="Basic test">
            <input type="number" name="est_time" value="30">
            <input type="date" name="date" value="2025-08-01">
            <button type="submit">Submit Basic Form</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Test 2: Form with Event Listener (Return True)</h2>
        <form id="form-return-true" action="http://127.0.0.1:5000/tasks/add" method="POST">
            <input type="text" name="title" value="Test Task Return True" required>
            <input type="text" name="classification" value="Planning" required>
            <input type="text" name="description" value="Return true test">
            <input type="number" name="est_time" value="30">
            <input type="date" name="date" value="2025-08-01">
            <button type="submit">Submit Return True Form</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Test 3: Form with Event Listener (No Return)</h2>
        <form id="form-no-return" action="http://127.0.0.1:5000/tasks/add" method="POST">
            <input type="text" name="title" value="Test Task No Return" required>
            <input type="text" name="classification" value="Planning" required>
            <input type="text" name="description" value="No return test">
            <input type="number" name="est_time" value="30">
            <input type="date" name="date" value="2025-08-01">
            <button type="submit">Submit No Return Form</button>
        </form>
    </div>

    <div class="test-section">
        <h2>Test 4: Form with Event Listener (Prevent Default)</h2>
        <form id="form-prevent-default" action="http://127.0.0.1:5000/tasks/add" method="POST">
            <input type="text" name="title" value="Test Task Prevent Default" required>
            <input type="text" name="classification" value="Planning" required>
            <input type="text" name="description" value="Prevent default test">
            <input type="number" name="est_time" value="30">
            <input type="date" name="date" value="2025-08-01">
            <button type="submit">Submit Prevent Default Form</button>
        </form>
    </div>

    <div id="console-output">
        <h3>Console Output:</h3>
        <div id="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Test 2: Return true
        document.getElementById('form-return-true').addEventListener('submit', function(event) {
            log('Form Return True: Submit event triggered');
            log('Form Return True: Returning true');
            return true;
        });

        // Test 3: No return
        document.getElementById('form-no-return').addEventListener('submit', function(event) {
            log('Form No Return: Submit event triggered');
            log('Form No Return: No return statement');
        });

        // Test 4: Prevent default
        document.getElementById('form-prevent-default').addEventListener('submit', function(event) {
            log('Form Prevent Default: Submit event triggered');
            log('Form Prevent Default: Calling preventDefault()');
            event.preventDefault();
        });

        log('Test page loaded successfully');
    </script>
</body>
</html>
