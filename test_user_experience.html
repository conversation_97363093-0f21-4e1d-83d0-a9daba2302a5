<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Experience Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; max-height: 200px; overflow-y: auto; }
        iframe { width: 100%; height: 400px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>User Experience Test</h1>
    
    <div class="log">
        <h3>Test Results:</h3>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 1: Add Task Page</h2>
        <p>This test will open the add task page and monitor for JavaScript errors and form submission behavior.</p>
        <button onclick="testAddTaskPage()">Test Add Task Page</button>
        <div id="add-task-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Edit Task Page</h2>
        <p>This test will open the edit task page and monitor for JavaScript errors and form submission behavior.</p>
        <button onclick="testEditTaskPage()">Test Edit Task Page</button>
        <div id="edit-task-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Archive Functionality</h2>
        <p>This test will open the tasks page and test the archive modal functionality.</p>
        <button onclick="testArchivePage()">Test Archive Functionality</button>
        <div id="archive-result"></div>
    </div>

    <div class="test-section">
        <h2>Manual Test Instructions:</h2>
        <ol>
            <li><strong>Add Task Test:</strong>
                <ul>
                    <li>Go to <a href="http://1********:5000/tasks/add" target="_blank">Add Task Page</a></li>
                    <li>Fill in the form with test data</li>
                    <li>Click "Add Task" button</li>
                    <li>Verify: Button shows "Adding Task..." briefly, then page redirects to tasks list</li>
                    <li>Check: New task appears in the list</li>
                </ul>
            </li>
            <li><strong>Edit Task Test:</strong>
                <ul>
                    <li>Go to <a href="http://1********:5000/tasks" target="_blank">Tasks Page</a></li>
                    <li>Click "Edit" on any task</li>
                    <li>Modify some fields</li>
                    <li>Click "Update Task" button</li>
                    <li>Verify: Button shows "Updating Task..." briefly, then page redirects to tasks list</li>
                    <li>Check: Task changes are saved</li>
                </ul>
            </li>
            <li><strong>Archive Test:</strong>
                <ul>
                    <li>Go to <a href="http://1********:5000/tasks" target="_blank">Tasks Page</a></li>
                    <li>Click "Archive" on any task</li>
                    <li>Confirm in the modal</li>
                    <li>Verify: Button shows "Archiving..." briefly, then modal closes and page refreshes</li>
                    <li>Check: Task is removed from active list</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : '';
            resultsDiv.innerHTML += `<div class="${className}">${timestamp}: ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`${timestamp}: ${message}`);
        }

        function testAddTaskPage() {
            logResult('Testing Add Task Page...', 'info');
            
            // Test if the page loads without errors
            fetch('http://1********:5000/tasks/add')
                .then(response => {
                    if (response.ok) {
                        logResult('✅ Add Task page loads successfully', 'success');
                        
                        // Test form submission
                        const testData = new FormData();
                        testData.append('title', 'UX Test Task');
                        testData.append('classification', 'Planning');
                        testData.append('description', 'Testing user experience');
                        testData.append('est_time', '30');
                        testData.append('date', '2025-08-01');
                        
                        return fetch('http://1********:5000/tasks/add', {
                            method: 'POST',
                            body: testData
                        });
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(response => {
                    if (response.status === 302 || response.redirected) {
                        logResult('✅ Add Task form submission works correctly (redirected)', 'success');
                        document.getElementById('add-task-result').innerHTML = '<span class="success">✅ PASS</span>';
                    } else {
                        logResult(`⚠️ Add Task form submission returned status ${response.status}`, 'warning');
                        document.getElementById('add-task-result').innerHTML = '<span class="warning">⚠️ PARTIAL</span>';
                    }
                })
                .catch(error => {
                    logResult(`❌ Add Task test failed: ${error.message}`, 'error');
                    document.getElementById('add-task-result').innerHTML = '<span class="error">❌ FAIL</span>';
                });
        }

        function testEditTaskPage() {
            logResult('Testing Edit Task Page...', 'info');
            
            // Test if the page loads without errors
            fetch('http://1********:5000/tasks/edit/1')
                .then(response => {
                    if (response.ok) {
                        logResult('✅ Edit Task page loads successfully', 'success');
                        
                        // Test form submission
                        const testData = new FormData();
                        testData.append('title', 'UX Test Task Updated');
                        testData.append('classification', 'Planning');
                        testData.append('description', 'Testing user experience - updated');
                        testData.append('est_time', '45');
                        testData.append('date', '2025-08-01');
                        
                        return fetch('http://1********:5000/tasks/edit/1', {
                            method: 'POST',
                            body: testData
                        });
                    } else if (response.status === 404) {
                        logResult('⚠️ Edit Task page: Task ID 1 not found (this is OK)', 'warning');
                        document.getElementById('edit-task-result').innerHTML = '<span class="warning">⚠️ SKIPPED (No task to edit)</span>';
                        return null;
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(response => {
                    if (response === null) return; // Skipped due to 404
                    
                    if (response.status === 302 || response.redirected) {
                        logResult('✅ Edit Task form submission works correctly (redirected)', 'success');
                        document.getElementById('edit-task-result').innerHTML = '<span class="success">✅ PASS</span>';
                    } else {
                        logResult(`⚠️ Edit Task form submission returned status ${response.status}`, 'warning');
                        document.getElementById('edit-task-result').innerHTML = '<span class="warning">⚠️ PARTIAL</span>';
                    }
                })
                .catch(error => {
                    logResult(`❌ Edit Task test failed: ${error.message}`, 'error');
                    document.getElementById('edit-task-result').innerHTML = '<span class="error">❌ FAIL</span>';
                });
        }

        function testArchivePage() {
            logResult('Testing Archive Functionality...', 'info');
            
            // Test if the tasks page loads
            fetch('http://1********:5000/tasks')
                .then(response => {
                    if (response.ok) {
                        logResult('✅ Tasks page loads successfully', 'success');
                        
                        // Test archive functionality
                        return fetch('http://1********:5000/tasks/delete/1', {
                            method: 'POST'
                        });
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                })
                .then(response => {
                    if (response.status === 302 || response.redirected) {
                        logResult('✅ Archive functionality works correctly (redirected)', 'success');
                        document.getElementById('archive-result').innerHTML = '<span class="success">✅ PASS</span>';
                    } else if (response.status === 404) {
                        logResult('⚠️ Archive: Task ID 1 not found (this is OK)', 'warning');
                        document.getElementById('archive-result').innerHTML = '<span class="warning">⚠️ SKIPPED (No task to archive)</span>';
                    } else {
                        logResult(`⚠️ Archive returned status ${response.status}`, 'warning');
                        document.getElementById('archive-result').innerHTML = '<span class="warning">⚠️ PARTIAL</span>';
                    }
                })
                .catch(error => {
                    logResult(`❌ Archive test failed: ${error.message}`, 'error');
                    document.getElementById('archive-result').innerHTML = '<span class="error">❌ FAIL</span>';
                });
        }

        // Run all tests automatically
        function runAllTests() {
            logResult('Starting comprehensive user experience tests...', 'info');
            setTimeout(() => testAddTaskPage(), 500);
            setTimeout(() => testEditTaskPage(), 1500);
            setTimeout(() => testArchivePage(), 2500);
        }

        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            logResult('User Experience Test page loaded', 'info');
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
