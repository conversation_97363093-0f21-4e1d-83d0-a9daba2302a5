#!/usr/bin/env python3
"""
Test script to verify form submission functionality
"""
import requests
import time
from datetime import date

BASE_URL = "http://127.0.0.1:5000"

def test_add_task():
    """Test adding a new task"""
    print("🧪 Testing Add Task functionality...")
    
    # Prepare test data
    task_data = {
        'title': f'Test Task {int(time.time())}',
        'classification': 'Planning',
        'description': 'Automated test task',
        'est_time': 30,
        'date': date.today().strftime('%Y-%m-%d')
    }
    
    try:
        # Submit the form
        response = requests.post(f"{BASE_URL}/tasks/add", data=task_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Add Task: SUCCESS - Form submitted and redirected")
            print(f"   Redirect location: {response.headers.get('Location', 'Unknown')}")
            return True
        else:
            print(f"❌ Add Task: FAILED - Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Add Task: ERROR - {str(e)}")
        return False

def test_edit_task():
    """Test editing an existing task"""
    print("🧪 Testing Edit Task functionality...")
    
    # First, get a task to edit
    try:
        response = requests.get(f"{BASE_URL}/tasks")
        if response.status_code != 200:
            print("❌ Edit Task: FAILED - Could not get tasks list")
            return False
        
        # Try to edit task ID 1 (assuming it exists)
        task_data = {
            'title': f'Updated Test Task {int(time.time())}',
            'classification': 'Planning',
            'description': 'Updated automated test task',
            'est_time': 45,
            'date': date.today().strftime('%Y-%m-%d')
        }
        
        response = requests.post(f"{BASE_URL}/tasks/edit/1", data=task_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Edit Task: SUCCESS - Form submitted and redirected")
            print(f"   Redirect location: {response.headers.get('Location', 'Unknown')}")
            return True
        elif response.status_code == 404:
            print("⚠️  Edit Task: SKIPPED - Task ID 1 not found")
            return True  # Not a failure, just no task to edit
        else:
            print(f"❌ Edit Task: FAILED - Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Edit Task: ERROR - {str(e)}")
        return False

def test_archive_task():
    """Test archiving a task"""
    print("🧪 Testing Archive Task functionality...")
    
    try:
        # Try to archive task ID 1 (assuming it exists)
        response = requests.post(f"{BASE_URL}/tasks/delete/1", allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ Archive Task: SUCCESS - Form submitted and redirected")
            print(f"   Redirect location: {response.headers.get('Location', 'Unknown')}")
            return True
        elif response.status_code == 404:
            print("⚠️  Archive Task: SKIPPED - Task ID 1 not found")
            return True  # Not a failure, just no task to archive
        else:
            print(f"❌ Archive Task: FAILED - Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Archive Task: ERROR - {str(e)}")
        return False

def test_server_connectivity():
    """Test if the server is running"""
    print("🧪 Testing server connectivity...")
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server: ONLINE and responding")
            return True
        else:
            print(f"❌ Server: FAILED - Status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server: ERROR - {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Form Submission Tests")
    print("=" * 50)
    
    # Test server connectivity first
    if not test_server_connectivity():
        print("\n❌ Cannot proceed - server is not responding")
        return
    
    print()
    
    # Run all tests
    results = []
    results.append(test_add_task())
    print()
    results.append(test_edit_task())
    print()
    results.append(test_archive_task())
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"   Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Form submission is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
